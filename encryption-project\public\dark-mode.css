/* Dark Mode CSS */
:root {
  /* Light Mode Variables */
  --bg-color: #ffffff;
  --bg-gradient: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  --text-color: #444444;
  --text-secondary: #666666;
  --border-color: rgba(0, 0, 0, 0.1);
  --card-bg: #ffffff;
  --sidebar-bg: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  --sidebar-border: rgba(0, 0, 0, 0.05);
  --primary-color: #ff3b5c;
  --primary-gradient: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
  --hover-bg: #f0f4f8;
  --input-bg: #ffffff;
  --input-border: #e0e0e0;
  --shadow-color: rgba(0, 0, 0, 0.08);
  --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  --button-text: #ffffff;
  --section-bg: #ffffff;
  --section-border: #f0f0f0;
  --table-header-bg: #f8f9fa;
  --table-border: #eaeaea;
  --table-row-hover: #f5f5f5;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;

  /* إضافة تأثير انتقالي سلس لجميع العناصر */
  --transition-speed: 0.3s;
}

/* Dark Mode Variables */
[data-theme="dark"] {
  --bg-color: #121212;
  --bg-gradient: linear-gradient(180deg, #121212 0%, #1e1e1e 100%);
  --text-color: #e0e0e0;
  --text-secondary: #aaaaaa;
  --border-color: rgba(255, 255, 255, 0.1);
  --card-bg: #1e1e1e;
  --sidebar-bg: linear-gradient(180deg, #1a1a1a 0%, #252525 100%);
  --sidebar-border: rgba(255, 255, 255, 0.05);
  --primary-color: #ff3b5c;
  --primary-gradient: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
  --hover-bg: #2c2c2c;
  --input-bg: #2a2a2a;
  --input-border: #3a3a3a;
  --shadow-color: rgba(0, 0, 0, 0.2);
  --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
  --button-text: #ffffff;
  --section-bg: #1e1e1e;
  --section-border: #333333;
  --table-header-bg: #252525;
  --table-border: #333333;
  --table-row-hover: #2a2a2a;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
}

/* تطبيق تأثير انتقالي سلس على جميع العناصر */
html, body, *, *:before, *:after {
  transition: background-color var(--transition-speed) ease,
              color var(--transition-speed) ease,
              border-color var(--transition-speed) ease,
              box-shadow var(--transition-speed) ease;
}

/* Apply Dark Mode Styles */
[data-theme="dark"] body {
  background-color: var(--bg-color);
  color: var(--text-color);
}

/* Bootstrap Modal Dark Mode */
[data-theme="dark"] .modal-content {
  background-color: var(--section-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .modal-header,
[data-theme="dark"] .modal-footer {
  border-color: var(--border-color);
}

[data-theme="dark"] .modal-title {
  color: var(--text-color);
}

[data-theme="dark"] .btn-close {
  filter: invert(1);
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--text-color);
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(255, 59, 92, 0.25);
}

[data-theme="dark"] .form-check-input {
  background-color: var(--input-bg);
  border-color: var(--input-border);
}

[data-theme="dark"] .form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

[data-theme="dark"] .sidebar {
  background: var(--sidebar-bg);
  border-left-color: var(--sidebar-border);
  box-shadow: 3px 0 15px var(--shadow-color);
}

[data-theme="dark"] .nav-menu a {
  color: var(--text-color);
  border-color: var(--border-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .nav-menu a:hover {
  background-color: var(--hover-bg);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] .container,
[data-theme="dark"] .section {
  background-color: var(--section-bg);
  border-color: var(--section-border);
}

[data-theme="dark"] input,
[data-theme="dark"] select,
[data-theme="dark"] textarea {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--text-color);
}

[data-theme="dark"] table {
  border-color: var(--table-border);
}

[data-theme="dark"] th {
  background-color: var(--table-header-bg);
  color: var(--text-color);
}

[data-theme="dark"] td {
  border-color: var(--table-border);
}

[data-theme="dark"] tr:hover {
  background-color: var(--table-row-hover);
}

[data-theme="dark"] .btn-primary {
  background: var(--primary-gradient);
  color: var(--button-text);
}

[data-theme="dark"] .btn-secondary {
  background-color: #444;
  color: var(--button-text);
}

/* Dark Mode Toggle Button */
.dark-mode-toggle {
  position: fixed;
  bottom: 20px;
  left: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.dark-mode-toggle:hover {
  transform: scale(1.1);
}

.dark-mode-toggle svg {
  width: 24px;
  height: 24px;
  fill: white;
}

/* Animation for toggle */
.dark-mode-toggle .sun,
.dark-mode-toggle .moon {
  position: absolute;
  transition: opacity 0.3s ease, transform 0.5s ease;
}

[data-theme="light"] .dark-mode-toggle .moon {
  opacity: 1;
  transform: scale(1);
}

[data-theme="light"] .dark-mode-toggle .sun {
  opacity: 0;
  transform: scale(0);
}

[data-theme="dark"] .dark-mode-toggle .moon {
  opacity: 0;
  transform: scale(0);
}

[data-theme="dark"] .dark-mode-toggle .sun {
  opacity: 1;
  transform: scale(1);
}
