# TikTok Live Overlay with <PERSON><PERSON> TTS

This project is a TikTok Live Overlay System for OBS that supports text-to-speech functionality for reading comments during live streams.

## Text-to-Speech with <PERSON><PERSON> TTS

This project uses Coqui TTS for high-quality text-to-speech conversion. Coqui TTS is an open-source neural text-to-speech system that provides natural sounding speech synthesis.

### Prerequisites

Before running the application, you need to install Coqui TTS:

```bash
# Install Coqui TTS
pip install TTS

# Verify installation
tts --list_models
```

### Available TTS Models

The application includes several pre-configured Coqui TTS models:

- English (Female) - `tts_models/en/ljspeech/tacotron2-DDC`
- English (Glow-TTS) - `tts_models/en/ljspeech/glow-tts`
- English (Multi-Speaker) - `tts_models/en/vctk/vits`
- English (Jenny) - `tts_models/en/jenny/jenny`
- French - `tts_models/fr/mai/tacotron-DDC`
- German - `tts_models/de/thorsten/tacotron2-DDC`
- Spanish - `tts_models/es/mai/tacotron2-DDC`

You can see a full list of available models by running:

```bash
tts --list_models
```

### Using TTS in the Application

1. Start the application: `npm start`
2. Open your browser and navigate to `http://localhost:3000/tts-comments.html`
3. Select a TTS model from the dropdown 
4. Adjust speed and other settings as needed
5. Test the voice using the "Test Voice" button
6. Enable TTS functionality when ready

## Project Setup

```bash
# Install dependencies
npm install

# Start the server
npm start
```

The server will run on port 3000 by default.

## Usage

- Connect to TikTok Live: http://localhost:3000/
- Gift Mappings: http://localhost:3000/mappings.html
- Overlay Settings: http://localhost:3000/settings.html
- TTS Comments: http://localhost:3000/tts-comments.html
- Open Overlay: http://localhost:3000/overlay.html

## Troubleshooting

If you encounter TTS-related issues:

1. Ensure Coqui TTS is installed correctly
2. Check if the command-line tool `tts` works properly
3. Try a different TTS model
4. Check console for error messages

For Windows users, ensure you have the Microsoft Visual C++ Build Tools installed if you encounter installation issues with Coqui TTS.

## المميزات

- الاتصال ببث TikTok Live مباشرةً
- عرض تنبيهات للهدايا، الإعجابات، التعليقات، والمتابعين
- ربط هدايا محددة بإجراءات مخصصة (صور، أصوات، فيديو، نصوص)
- مكتبة هدايا TikTok كاملة مع الصور والأسعار
- عرض صور الهدايا في تنبيهات الشاشة
- إمكانية اختبار الإجراءات قبل البث المباشر
- دعم قائمة انتظار للإجراءات لتجنب التداخل
- واجهة تحكم سهلة الاستخدام باللغة العربية

## المتطلبات

- Node.js (الإصدار 14 أو أحدث)
- اتصال إنترنت مستقر
- OBS Studio أو أي برنامج بث آخر يدعم مصادر المتصفح (Browser Sources)

## التثبيت

1. قم بتثبيت Node.js إذا لم يكن مثبتاً بالفعل
2. قم بتنزيل أو استنساخ هذا المشروع
3. افتح سطر الأوامر في مجلد المشروع وقم بتنفيذ الأمر التالي لتثبيت المتطلبات:

```
npm install
```

4. قم بتشغيل البرنامج باستخدام الأمر:

```
npm start
```

5. افتح المتصفح على العنوان http://localhost:3000

## طريقة الاستخدام

### 1. الاتصال ببث TikTok Live

- أدخل اسم المستخدم الخاص بحساب TikTok (بدون @)
- اضغط على زر "اتصال"
- في حالة نجاح الاتصال، ستظهر حالة "متصل بـ @اسم_المستخدم"

### 2. إعداد Overlay في OBS

- انسخ رابط Overlay من لوحة التحكم
- في OBS، أضف مصدر جديد من نوع "Browser"
- الصق الرابط في حقل URL
- حدد عرض وارتفاع الـ Overlay (يُوصى بـ 1920×1080)
- تأكد من تفعيل خاصية "الخلفية الشفافة" (Transparent background)

### 3. ربط الهدايا بالإجراءات

هناك طريقتان لإضافة الهدايا وربطها بالإجراءات:

#### أ. الإضافة اليدوية

- أدخل اسم الهدية (مثل Rose, Lion, etc.)
- اختر نوع الإجراء (صورة متحركة، صوت، فيديو، نص)
- أدخل محتوى الإجراء (رابط URL للصورة/الصوت/الفيديو أو النص المراد عرضه)
- حدد مدة الظهور بالثواني
- اضغط على زر "إضافة"

#### ب. الاختيار من مكتبة الهدايا

- انتقل إلى تبويب "اختيار من مكتبة الهدايا"
- استخدم مربع البحث للعثور على الهدية المطلوبة
- انقر على الهدية لاختيارها
- أكمل باقي الحقول (نوع الإجراء، المحتوى، المدة)
- اضغط على زر "إضافة"

### 4. اختبار الإجراءات

- اختر الهدية من القائمة المنسدلة في قسم "اختبار الإجراءات"
- اضغط على زر "اختبار الإجراء" لمشاهدة كيف سيظهر الإجراء في Overlay

## مكتبة الهدايا

- يتم تحميل مكتبة هدايا TikTok تلقائياً عند بدء تشغيل السيرفر
- تحتوي المكتبة على جميع الهدايا المتاحة في TikTok مع صورها وأسعارها بالماسات
- يمكنك تحديث قائمة الهدايا في أي وقت بالضغط على زر "تحديث الهدايا"
- البحث يتيح لك العثور على الهدايا بسرعة حسب اسمها

## ميزات متقدمة

- عند استلام هدية غالية (أكثر من 100 ماسة)، سيتم عرض صورتها بشكل كبير في وسط الشاشة
- يظهر اسم المستخدم المرسل وصورة الهدية في التنبيهات البسيطة
- يمكنك استخدام صورة الهدية نفسها كإجراء عن طريق تعيين نوع الإجراء إلى "صورة متحركة" واستخدام رابط صورة الهدية كمحتوى

## ملاحظات مهمة

- تأكد من أن حساب TikTok المستخدم للاتصال يجب أن يكون في وضع البث المباشر
- جميع الإجراءات (الصور، الفيديو، الأصوات) يجب أن تكون متاحة عبر روابط URL عامة
- يُفضل استخدام خدمات استضافة الصور مثل Imgur أو أي خدمة مماشلة للحصول على روابط دائمة
- قد تتغير أسماء بعض الهدايا في TikTok من وقت لآخر، لذا قد تحتاج إلى تحديث الإعدادات

## المساهمة

يُرحب بالمساهمات والاقتراحات لتحسين هذا المشروع. يمكنك فتح issue أو إرسال pull request.

## الترخيص

هذا المشروع مرخص تحت [MIT License](LICENSE). 