<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🏁 صراع الأعلام - Flag Clash</title>
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: '<PERSON><PERSON><PERSON>', sans-serif;
      background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
      color: white;
      overflow: hidden;
      height: 100vh;
    }

    #gameContainer {
      position: relative;
      width: 100vw;
      height: 100vh;
    }

    #gameCanvas {
      display: block;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle at center, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    }

    /* واجهة المستخدم */
    .ui-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 100;
    }

    .ui-element {
      pointer-events: auto;
    }

    /* لوحة النتائج */
    .scoreboard {
      position: absolute;
      top: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.8);
      border-radius: 15px;
      padding: 20px;
      min-width: 300px;
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255, 255, 255, 0.1);
    }

    .scoreboard h3 {
      text-align: center;
      margin-bottom: 15px;
      color: #ffd700;
      font-size: 1.2rem;
    }

    .country-score {
      margin-bottom: 10px;
      padding: 8px 12px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .country-score.leading {
      background: linear-gradient(45deg, #ffd700, #ffed4e);
      color: #000;
      font-weight: bold;
      transform: scale(1.05);
    }

    .country-main-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 5px;
    }

    .country-details {
      display: flex;
      justify-content: space-between;
      font-size: 0.8rem;
      opacity: 0.8;
    }

    .country-details small {
      display: flex;
      align-items: center;
      gap: 3px;
    }

    .gift-count {
      color: #4CAF50;
      font-weight: bold;
    }

    .last-sender {
      color: #2196F3;
      font-style: italic;
    }

    .team-members {
      color: #FF9800;
      font-weight: bold;
    }

    .country-flag {
      font-size: 1.5rem;
      margin-left: 10px;
    }

    .country-name {
      flex: 1;
      font-weight: 500;
    }

    .country-power {
      font-weight: bold;
      color: #00ff7f;
    }

    /* شريط الحالة */
    .status-bar {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      border-radius: 25px;
      padding: 15px 30px;
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255, 255, 255, 0.1);
    }

    .connection-status {
      text-align: center;
      font-weight: 500;
    }

    .connection-status.connected {
      color: #00ff7f;
    }

    .connection-status.disconnected {
      color: #ff4757;
    }

    /* أزرار التحكم */
    .controls {
      position: absolute;
      top: 20px;
      left: 20px;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .control-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 10px;
      padding: 12px 16px;
      color: white;
      font-family: 'Tajawal', sans-serif;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .control-btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }

    /* رسائل التنبيه */
    .alert-message {
      position: absolute;
      top: 20%; /* تم ترحيلها إلى الأعلى من 50% إلى 20% */
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(45deg, #ff6b87, #ff3b5c);
      color: white;
      padding: 12px 24px; /* تم تصغير الحشو من 20px 40px إلى 12px 24px */
      border-radius: 12px; /* تم تصغير الحواف من 15px إلى 12px */
      font-size: 1rem; /* تم تصغير الخط من 1.5rem إلى 1rem */
      font-weight: bold;
      text-align: center;
      box-shadow: 0 6px 20px rgba(255, 59, 92, 0.4); /* تم تصغير الظل */
      animation: alertPulse 2s ease-in-out;
      z-index: 200;
    }

    @keyframes alertPulse {
      0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0; }
      50% { transform: translate(-50%, -50%) scale(1.1); opacity: 1; }
      100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
    }

    /* تصميم متجاوب */
    @media (max-width: 768px) {
      .scoreboard {
        top: 10px;
        right: 10px;
        min-width: 250px;
        padding: 15px;
      }

      .controls {
        top: 10px;
        left: 10px;
      }

      .control-btn {
        padding: 10px 12px;
        font-size: 0.9rem;
      }

      .status-bar {
        bottom: 10px;
        padding: 12px 20px;
      }
    }

    /* تحميل */
    .loading-screen {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid #ffd700;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      font-size: 1.2rem;
      font-weight: 500;
      text-align: center;
    }

    /* أزرار التحكم اليدوي */
    .manual-controls {
      position: absolute;
      top: 10px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      padding: 20px;
      border-radius: 15px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      min-width: 400px;
    }

    .manual-controls h3 {
      color: #ffd700;
      text-align: center;
      margin-bottom: 15px;
    }

    .country-selector {
      margin-bottom: 15px;
    }

    .country-selector label {
      display: block;
      color: white;
      margin-bottom: 5px;
      font-weight: 500;
    }

    .country-selector select {
      width: 100%;
      padding: 8px 12px;
      border-radius: 8px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      background: rgba(255, 255, 255, 0.1);
      color: white;
      font-family: 'Tajawal', sans-serif;
    }

    .country-selector select option {
      background: #333;
      color: white;
    }

    .gift-buttons {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;
    }

    .gift-buttons .control-btn {
      margin: 0;
      padding: 10px;
      font-size: 0.9rem;
    }

    /* رسائل الانضمام للفرق */
    .team-join-messages {
      position: absolute;
      top: 20px;
      left: 20px;
      z-index: 1001;
      max-width: 400px;
    }

    .team-join-message {
      background: rgba(76, 175, 80, 0.9);
      color: white;
      padding: 8px 12px;
      margin-bottom: 5px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: bold;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
      border: 1px solid rgba(76, 175, 80, 0.3);
      animation: slideInLeft 0.3s ease-out;
      font-family: 'Tajawal', sans-serif;
      transition: opacity 0.3s ease-out, transform 0.3s ease-out;
    }

    .team-join-message.fade-out {
      opacity: 0;
      transform: translateX(-100%);
    }

    @keyframes slideInLeft {
      from {
        transform: translateX(-100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  </style>
</head>
<body>
  <!-- شاشة التحميل -->
  <div id="loadingScreen" class="loading-screen">
    <div class="loading-spinner"></div>
    <div class="loading-text">
      <h2>🏁 صراع الأعلام</h2>
      <p>جاري تحميل اللعبة...</p>
    </div>
  </div>

  <!-- حاوية اللعبة -->
  <div id="gameContainer">
    <canvas id="gameCanvas"></canvas>
    
    <!-- واجهة المستخدم -->
    <div class="ui-overlay">
      <!-- أزرار التحكم -->
      <div id="gameControls" class="controls ui-element">
        <button class="control-btn" onclick="toggleDemo()">🧪 وضع التجربة</button>
        <button class="control-btn" onclick="toggleManualMode()">🎮 تحكم يدوي</button>
        <button class="control-btn" onclick="resetGame()">🔄 إعادة تشغيل</button>
        <button class="control-btn" onclick="openSettings()">⚙️ الإعدادات</button>
        <button class="control-btn" onclick="toggleFullscreen()">🖥️ ملء الشاشة</button>
        <button class="control-btn" onclick="window.close()">❌ إغلاق</button>
      </div>

      <!-- أزرار التحكم اليدوي -->
      <div id="manualControls" class="manual-controls ui-element" style="display: none;">
        <h3>🎮 التحكم اليدوي</h3>

        <div class="country-selector">
          <label>اختر الدولة:</label>
          <select id="selectedCountry">
            <option value="EG">🇪🇬 مصر</option>
            <option value="SA">🇸🇦 السعودية</option>
            <option value="DZ">🇩🇿 الجزائر</option>
            <option value="MA">🇲🇦 المغرب</option>
            <option value="US">🇺🇸 أمريكا</option>
            <option value="FR">🇫🇷 فرنسا</option>
            <option value="GB">🇬🇧 بريطانيا</option>
            <option value="DE">🇩🇪 ألمانيا</option>
            <option value="IT">🇮🇹 إيطاليا</option>
            <option value="ES">🇪🇸 إسبانيا</option>
            <option value="TR">🇹🇷 تركيا</option>
            <option value="AE">🇦🇪 الإمارات</option>
          </select>
        </div>

        <div class="gift-buttons">
          <button onclick="sendManualGift('Rose', 1)" class="control-btn">🌹 وردة (1)</button>
          <button onclick="sendManualGift('Rose', 10)" class="control-btn">🌹 10 ورود</button>
          <button onclick="sendManualGift('Rose', 100)" class="control-btn">🌹 100 وردة</button>
          <button onclick="sendManualGift('Diamond', 1)" class="control-btn">💎 ماسة (5)</button>
          <button onclick="sendManualGift('Crown', 1)" class="control-btn">👑 تاج (10)</button>
          <button onclick="sendManualGift('Rocket', 1)" class="control-btn">🚀 صاروخ (20)</button>
        </div>

        <div class="team-test-section">
          <h4 style="color: #4CAF50; margin: 15px 0 10px 0;">🧪 اختبار نظام الفرق:</h4>
          <div class="gift-buttons">
            <button onclick="simulateTeamComment('join 1')" class="control-btn">join 1</button>
            <button onclick="simulateTeamComment('join 2')" class="control-btn">join 2</button>
            <button onclick="simulateTeamComment('join 3')" class="control-btn">join 3</button>
            <button onclick="simulateTeamComment('lev')" class="control-btn">lev</button>
            <button onclick="simulateTeamComment('فريقي')" class="control-btn">فريقي</button>
            <button onclick="simulateTeamComment('الفرق')" class="control-btn">الفرق</button>
          </div>
        </div>
      </div>

      <!-- لوحة النتائج -->
      <div class="scoreboard ui-element">
        <h3>🏆 ترتيب الدول</h3>
        <div id="scoreboardContent">
          <!-- سيتم ملؤها بالجافا سكريبت -->
        </div>
      </div>

      <!-- شريط الحالة -->
      <div class="status-bar ui-element">
        <div id="connectionStatus" class="connection-status disconnected">
          غير متصل بـ TikTok Live
        </div>
      </div>
    </div>
  </div>

  <!-- مكتبات JavaScript -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <script src="/socket.io/socket.io.js"></script>
  <script type="module" src="/js/firebase-config.js"></script>
  <script>
    // تأخير تحميل auth-guard حتى يتم تحميل Firebase
    setTimeout(() => {
      const script = document.createElement('script');
      script.type = 'module';
      script.src = '/js/auth-guard.js';
      document.head.appendChild(script);
    }, 500);
  </script>
  <script src="/js/flag-clash-game.js"></script>
  
  <script>
    // متغيرات حالة الاشتراك
    let currentUser = null;
    let userSubscription = null;
    let hasActiveSubscription = false;
    let authCheckDone = false;
    let hasRedirected = false;

    // التحقق من المصادقة
    window.addEventListener('authStateChanged', (event) => {
      if (authCheckDone || hasRedirected) return;

      const user = event.detail.user;
      console.log('🔐 Flag Clash - Auth state:', user?.email);

      if (!user) {
        console.log('❌ No user, redirecting to auth');
        hasRedirected = true;
        window.location.href = '/auth.html';
      } else if (!user.emailVerified) {
        console.log('❌ Email not verified, redirecting to verification');
        hasRedirected = true;
        window.location.href = '/email-verification.html';
      } else {
        console.log('✅ User authenticated and verified');
        authCheckDone = true;
        currentUser = user;

        // تحميل بيانات الاشتراك
        loadUserSubscription();
      }
    });

    // تحميل بيانات اشتراك المستخدم
    async function loadUserSubscription() {
      try {
        if (window.firebaseHelpers && currentUser) {
          userSubscription = await window.firebaseHelpers.getUserSubscription(currentUser.uid);
          hasActiveSubscription = userSubscription &&
            userSubscription.status === 'active' &&
            new Date() < (userSubscription.endDate?.toDate ? userSubscription.endDate.toDate() : new Date(userSubscription.endDate));

          console.log('User subscription status:', hasActiveSubscription ? 'Active' : 'None');

          if (!hasActiveSubscription) {
            showSubscriptionRequired();
          } else {
            hideSubscriptionMessage();
            initializeGame();
          }
        }
      } catch (error) {
        console.error('Error loading user subscription:', error);
        hasActiveSubscription = false;
        showSubscriptionRequired();
      }
    }

    // عرض رسالة الاشتراك المطلوب
    function showSubscriptionRequired() {
      // إخفاء اللعبة
      const gameContainer = document.querySelector('.game-container');
      if (gameContainer) {
        gameContainer.style.display = 'none';
      }

      // إنشاء رسالة الاشتراك
      const subscriptionMessage = document.createElement('div');
      subscriptionMessage.id = 'subscription-message';
      subscriptionMessage.innerHTML = `
        <div style="
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
          color: white;
          text-align: center;
          font-family: 'Tajawal', sans-serif;
        ">
          <div style="
            max-width: 600px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
          ">
            <div style="font-size: 4rem; margin-bottom: 20px;">🔒</div>
            <h1 style="font-size: 2.5rem; margin-bottom: 20px;">لعبة صراع الأعلام مقفلة</h1>
            <p style="font-size: 1.3rem; margin-bottom: 30px; opacity: 0.9;">
              هذه اللعبة متاحة فقط للمشتركين في الباقة الشاملة
            </p>

            <div style="
              background: rgba(255,255,255,0.1);
              border-radius: 15px;
              padding: 25px;
              margin: 30px 0;
            ">
              <h3 style="font-size: 1.5rem; margin-bottom: 20px;">🏁 ما ستحصل عليه:</h3>
              <div style="text-align: right; margin: 20px 0;">
                <div style="margin: 10px 0;">🎁 تفاعل مباشر مع TikTok Live</div>
                <div style="margin: 10px 0;">🌍 معركة الدول ثلاثية الأبعاد</div>
                <div style="margin: 10px 0;">👑 نظام ترتيب ديناميكي</div>
                <div style="margin: 10px 0;">🎨 رسوميات ثلاثية الأبعاد مذهلة</div>
                <div style="margin: 10px 0;">🎵 تعليق صوتي احترافي</div>
                <div style="margin: 10px 0;">⚙️ إعدادات متقدمة وتخصيص كامل</div>
              </div>
            </div>

            <div style="margin: 30px 0;">
              <div style="
                display: inline-block;
                background: rgba(255,255,255,0.2);
                padding: 15px 25px;
                border-radius: 50px;
                margin-bottom: 10px;
              ">
                <span style="font-size: 2rem; font-weight: bold; color: #00ff7f;">$10</span>
                <span style="font-size: 1rem; opacity: 0.8;"> / شهرياً</span>
              </div>
              <p style="font-size: 1.1rem; opacity: 0.9;">باقة واحدة شاملة - جميع الميزات</p>
            </div>

            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
              <button onclick="window.location.href='/subscriptions.html'" style="
                background: linear-gradient(45deg, #00ff7f, #00cc66);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 50px;
                font-size: 1.1rem;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                font-family: 'Tajawal', sans-serif;
                box-shadow: 0 10px 20px rgba(0,255,127,0.3);
              " onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 15px 30px rgba(0,255,127,0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 20px rgba(0,255,127,0.3)'">
                ⚡ اشترك الآن
              </button>
              <button onclick="window.location.href='/games.html'" style="
                background: rgba(255,255,255,0.2);
                color: white;
                border: 2px solid rgba(255,255,255,0.3);
                padding: 15px 30px;
                border-radius: 50px;
                font-size: 1.1rem;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                font-family: 'Tajawal', sans-serif;
                backdrop-filter: blur(10px);
              " onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-3px)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'">
                🔙 العودة للألعاب
              </button>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(subscriptionMessage);
    }

    // إخفاء رسالة الاشتراك
    function hideSubscriptionMessage() {
      const subscriptionMessage = document.getElementById('subscription-message');
      if (subscriptionMessage) {
        subscriptionMessage.remove();
      }
    }

    // تهيئة اللعبة للمشتركين
    function initializeGame() {
      const gameContainer = document.querySelector('.game-container');
      if (gameContainer) {
        gameContainer.style.display = 'block';
      }

      // تهيئة اللعبة هنا
      console.log('🎮 Initializing Flag Clash game for subscriber');
      startOriginalGameInitialization();
    }

    // تهيئة اللعبة الأصلية
    function startOriginalGameInitialization() {
      // فحص إذا كان في وضع "العب الآن"
      const isPlayMode = new URLSearchParams(window.location.search).has('play');

      if (isPlayMode) {
        // إخفاء أزرار التحكم في وضع "العب الآن"
        const gameControls = document.getElementById('gameControls');
        const manualControls = document.getElementById('manualControls');

        if (gameControls) {
          gameControls.style.display = 'none';
        }
        if (manualControls) {
          manualControls.style.display = 'none';
        }

        console.log('🎮 وضع العب الآن - تم إخفاء أزرار التحكم');
      }

      // إخفاء شاشة التحميل بعد تحميل المكتبات
      setTimeout(() => {
        document.getElementById('loadingScreen').style.display = 'none';

        // بدء اللعبة
        if (typeof FlagClashGame !== 'undefined') {
          window.game = new FlagClashGame();
          window.game.init();
        } else {
          console.error('فشل في تحميل لعبة صراع الأعلام');
        }
      }, 2000);
    }

    // تهيئة الصفحة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', () => {
      console.log('🏁 Flag Clash page loaded');
      // سيتم تهيئة اللعبة فقط بعد التحقق من الاشتراك
    });

    // وظائف التحكم
    function toggleDemo() {
      if (window.game) {
        window.game.toggleDemoMode();
      }
    }

    function resetGame() {
      if (window.game) {
        window.game.reset();
      }
    }

    function openSettings() {
      window.open('/games/flag-settings.html', '_blank');
    }

    function toggleFullscreen() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
      } else {
        document.exitFullscreen();
      }
    }

    // متغير لحالة التحكم اليدوي
    let manualModeActive = false;

    function toggleManualMode() {
      manualModeActive = !manualModeActive;
      const manualControls = document.getElementById('manualControls');

      if (manualModeActive) {
        manualControls.style.display = 'block';
        // إيقاف وضع التجربة العشوائي
        if (window.game && window.game.gameSettings.demoMode) {
          window.game.toggleDemoMode();
        }
      } else {
        manualControls.style.display = 'none';
      }
    }

    function sendManualGift(giftName, repeatCount) {
      if (!window.game) return;

      const selectedCountry = document.getElementById('selectedCountry').value;

      // إنشاء بيانات هدية مزيفة
      const fakeGiftData = {
        giftName: giftName,
        repeatCount: repeatCount,
        uniqueId: `manual_user_${selectedCountry}`,
        nickname: `مستخدم_${selectedCountry}`
      };

      // إرسال الهدية للعبة
      window.game.handleGiftReceived(fakeGiftData);

      console.log(`🎁 تم إرسال ${repeatCount} ${giftName} لـ ${selectedCountry}`);
    }

    // دالة محاكاة التعليقات لاختبار نظام الفرق
    function simulateTeamComment(comment) {
      if (window.game && window.game.socket) {
        const testData = {
          uniqueId: 'test_user_' + Math.random().toString(36).substr(2, 9),
          nickname: 'مستخدم_تجريبي',
          comment: comment
        };

        console.log('🧪 إرسال تعليق محاكي:', testData);
        window.game.socket.emit('simulateComment', testData);
      } else {
        console.error('❌ Socket غير متصل');
        alert('يرجى الانتظار حتى يتم تحميل اللعبة بالكامل');
      }
    }
  </script>
</body>
</html>
