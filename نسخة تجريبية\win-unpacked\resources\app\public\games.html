<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>الألعاب - TikTok Live Overlay</title>
  <!-- تحميل سكريبت الوضع المظلم قبل أي شيء آخر لمنع الوميض -->
  <script src="/preload-theme.js"></script>
  <link rel="stylesheet" href="/dark-mode.css">
  <link rel="stylesheet" href="/css/loading-indicator.css">
  <link rel="stylesheet" href="/css/sidebar-enhanced.css">
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    :root {
      color-scheme: light dark;
    }
    body {
      font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--bg-gradient);
      color: var(--text-color);
      transition: background 0.3s ease, color 0.3s ease;
      min-height: 100vh;
      display: flex;
    }

    .sidebar {
      width: 250px;
      background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
      box-shadow: 3px 0 15px rgba(0, 0, 0, 0.08);
      padding: 0;
      position: fixed;
      height: 100%;
      right: 0;
      top: 0;
      overflow-y: auto;
      z-index: 1000;
      border-left: 1px solid rgba(0, 0, 0, 0.05);
      border-top-left-radius: 15px;
      border-bottom-left-radius: 15px;
      opacity: 0.95 !important;
    }

    .logo {
      text-align: center;
      padding: 25px 15px;
      margin-bottom: 10px;
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      box-shadow: 0 4px 10px rgba(255, 59, 92, 0.2);
      position: relative;
      border-top-left-radius: 15px;
    }

    .logo h3 {
      color: white;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .logo::after {
      content: '';
      position: absolute;
      bottom: -10px;
      right: 50%;
      transform: translateX(50%);
      width: 20px;
      height: 20px;
      background: #ff3b5c;
      transform: rotate(45deg) translateX(50%);
      box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
      z-index: -1;
    }

    .nav-menu {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 20px 15px;
    }

    .nav-menu a {
      display: flex;
      align-items: center;
      padding: 14px 18px;
      color: #444;
      text-decoration: none;
      border-radius: 12px;
      transition: all 0.3s ease;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
      border: 1px solid rgba(0, 0, 0, 0.03);
    }

    .nav-menu a::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 3px;
      height: 100%;
      background: #ff3b5c;
      transform: scaleY(0);
      transition: transform 0.3s ease;
    }

    .nav-menu a:hover {
      background-color: #f0f4f8;
      color: #ff3b5c;
      transform: translateY(-2px);
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
    }

    .nav-menu a:hover::before {
      transform: scaleY(1);
    }

    .nav-menu a.active {
      background: linear-gradient(45deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      box-shadow: 0 5px 15px rgba(255, 59, 92, 0.25);
      border: none;
    }

    .nav-menu a.active::before {
      transform: scaleY(0);
    }

    .nav-icon {
      margin-left: 20px;
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .nav-menu a:hover .nav-icon {
      background-color: rgba(255, 59, 92, 0.1);
      transform: rotate(5deg);
    }

    .nav-menu a.active .nav-icon {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .main-content {
      flex: 1;
      margin-right: 250px;
      padding: 20px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: var(--section-bg);
      border-radius: 15px;
      padding: 30px;
      box-shadow: 0 10px 30px var(--shadow-color);
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
      margin-top: 30px;
      opacity: 0.95 !important;
    }

    h1 {
      color: var(--primary-color);
      text-align: center;
      font-size: 2.2rem;
      margin-bottom: 15px;
      font-weight: 700;
      border-bottom: 2px solid var(--border-color);
      padding-bottom: 15px;
    }

    .subtitle {
      text-align: center;
      color: var(--text-secondary);
      margin-bottom: 40px;
      font-size: 1.1rem;
    }

    .games-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 25px;
      margin-top: 30px;
    }

    .game-card {
      background: var(--card-bg);
      border-radius: 15px;
      padding: 25px;
      box-shadow: var(--card-shadow);
      transition: all 0.3s ease;
      border: 1px solid var(--border-color);
      position: relative;
      overflow: hidden;
    }

    .game-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #ff3b5c, #ff6b87, #ff3b5c);
      background-size: 200% 100%;
      animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
      0% { background-position: -200% 0; }
      100% { background-position: 200% 0; }
    }

    .game-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px var(--shadow-color);
    }

    .game-icon {
      font-size: 3rem;
      text-align: center;
      margin-bottom: 15px;
      color: var(--primary-color);
    }

    .game-title {
      font-size: 1.4rem;
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 10px;
      text-align: center;
    }

    .game-description {
      color: var(--text-secondary);
      text-align: center;
      margin-bottom: 20px;
      line-height: 1.6;
    }

    .game-actions {
      display: flex;
      gap: 10px;
      justify-content: center;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      font-family: 'Tajawal', sans-serif;
    }

    .btn-primary {
      background: var(--primary-gradient);
      color: white;
      box-shadow: 0 4px 6px rgba(255, 59, 92, 0.2);
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 8px rgba(255, 59, 92, 0.25);
    }

    .btn-secondary {
      background: var(--input-bg);
      color: var(--text-color);
      border: 1px solid var(--border-color);
    }

    .btn-secondary:hover {
      background: var(--hover-bg);
    }

    .btn-demo {
      background: linear-gradient(45deg, #00d4ff, #00f5ff);
      color: #001f3f;
      border: 2px solid #00d4ff;
      font-weight: 600;
    }

    .btn-demo:hover {
      background: linear-gradient(45deg, #00f5ff, #87ceeb);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
    }

    .coming-soon {
      text-align: center;
      padding: 40px 20px;
      color: var(--text-secondary);
    }

    .coming-soon-icon {
      font-size: 4rem;
      margin-bottom: 20px;
      opacity: 0.5;
    }

    .coming-soon h3 {
      margin-bottom: 10px;
      color: var(--text-color);
    }

    .status-badge {
      position: absolute;
      top: 15px;
      left: 15px;
      background: #28a745;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }

    .status-badge.coming-soon {
      background: #ffc107;
      color: #333;
    }

    /* تصميم بطاقة اللعبة */
    .game-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 20px;
      padding: 25px;
      color: white;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .game-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
      pointer-events: none;
    }

    .game-card:hover {
      transform: translateY(-10px) scale(1.02);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    }

    .game-icon {
      font-size: 48px;
      text-align: center;
      margin-bottom: 15px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .game-card h3 {
      font-size: 24px;
      margin-bottom: 15px;
      text-align: center;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .game-card p {
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 20px;
      opacity: 0.9;
    }

    .game-features {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
      margin-bottom: 20px;
    }

    .feature {
      background: rgba(255, 255, 255, 0.2);
      padding: 8px 12px;
      border-radius: 20px;
      font-size: 12px;
      text-align: center;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .game-actions {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .play-btn, .settings-btn, .preview-btn, .demo-btn {
      flex: 1;
      min-width: 100px;
      padding: 12px 16px;
      border: none;
      border-radius: 25px;
      font-family: 'Tajawal', sans-serif;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 12px;
      text-decoration: none;
      display: inline-block;
      text-align: center;
    }

    .play-btn {
      background: linear-gradient(45deg, #00ff7f, #00cc66);
      color: white;
      box-shadow: 0 4px 15px rgba(0, 255, 127, 0.3);
    }

    .play-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 255, 127, 0.4);
    }

    .settings-btn {
      background: linear-gradient(45deg, #9b59b6, #8e44ad);
      color: white;
      box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
    }

    .settings-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(155, 89, 182, 0.4);
    }

    .preview-btn {
      background: linear-gradient(45deg, #3498db, #2980b9);
      color: white;
      box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }

    .preview-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
    }

    .demo-btn {
      background: linear-gradient(45deg, #e74c3c, #c0392b);
      color: white;
      box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
    }

    .demo-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
    }

    @media (max-width: 768px) {
      .game-features {
        grid-template-columns: 1fr;
      }

      .game-actions {
        flex-direction: column;
      }

      .play-btn, .settings-btn, .preview-btn, .demo-btn {
        min-width: auto;
      }
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="logo">
      <h3>StreamTok</h3>
    </div>
    <div class="nav-menu">
      <a href="/">
        <span class="nav-icon">🔌</span>
        الاتصال بـ TikTok Live
      </a>
      <a href="/mappings.html">
        <span class="nav-icon">🛠️</span>
        ربط الهدايا بالإجراءات
      </a>
      <a href="/tts-comments.html">
        <span class="nav-icon">🔊</span>
        قراءة التعليقات
      </a>
      <a href="/games.html" class="active">
        <span class="nav-icon">🎯</span>
        الألعاب
      </a>
      <a href="/settings.html">
        <span class="nav-icon">⚙️</span>
        الإعدادات
      </a>
      <a href="/profiles.html">
        <span class="nav-icon">👤</span>
        الملفات الشخصية
      </a>
      <a href="/subscriptions.html">
        <span class="nav-icon">⚡</span>
        الاشتراكات
      </a>
      <a href="/contact.html">
        <span class="nav-icon">📞</span>
        اتصل بنا
      </a>
      <a href="/overlay.html" target="_blank">
        <span class="nav-icon">🖥️</span>
        فتح Overlay
      </a>
      <a href="/overlay-bar.html">
        <span class="nav-icon">🎮</span>
        Overlay Bar
      </a>
    </div>
  </div>

  <div class="main-content">
    <div class="container">
      <h1>الألعاب التفاعلية</h1>
      <p class="subtitle">استمتع بمجموعة من الألعاب التفاعلية التي تتفاعل مع أحداث البث المباشر على TikTok</p>

      <div class="games-grid">
        <!-- لعبة Fish Eat Fish -->
        <div class="game-card">
          <div class="game-icon">🐟</div>
          <h3>Fish Eat Fish</h3>
          <p>لعبة أسماك تفاعلية مع TikTok Live - الورود تضيف أسماك، الأسماك الأكبر تأكل الأصغر!</p>
          <div class="game-features">
            <span class="feature">🌹 تفاعل مع الهدايا</span>
            <span class="feature">🦈 نمو الأسماك</span>
            <span class="feature">⚔️ معارك ملحمية</span>
            <span class="feature">🏆 نظام إنجازات</span>
          </div>
          <div class="game-actions">
            <button onclick="window.open('/games/fish-eat-fish.html', '_blank')" class="play-btn">
              🎮 العب الآن
            </button>
            <button onclick="window.open('/games/fish-settings.html', '_blank')" class="settings-btn">
              ⚙️ إعدادات اللعبة
            </button>
            <button onclick="previewGame('fish')" class="preview-btn">
              👁️ معاينة
            </button>
            <button onclick="window.open('/games/fish-eat-fish.html?demo=true', '_blank')" class="demo-btn">
              🧪 تجربة محلية
            </button>
          </div>
        </div>

        <!-- لعبة صراع الأعلام -->
        <div class="game-card">
          <div class="game-icon">🏁</div>
          <h3>صراع الأعلام</h3>
          <p>معركة ثلاثية الأبعاد بين أعلام الدول! كل هدية تكبر علم دولة المرسل والدولة الأقوى تسيطر على الساحة!</p>
          <div class="game-features">
            <span class="feature">🌍 أعلام دولية</span>
            <span class="feature">📦 مكعبات ثلاثية الأبعاد</span>
            <span class="feature">⚡ تأثيرات بصرية</span>
            <span class="feature">🎵 تعليق صوتي</span>
          </div>
          <div class="game-actions">
            <button onclick="window.open('/games/flag-clash.html?play', '_blank')" class="play-btn">
              🎮 العب الآن
            </button>
            <button onclick="window.open('/games/flag-settings.html', '_blank')" class="settings-btn">
              ⚙️ إعدادات اللعبة
            </button>
            <button onclick="previewGame('flag-clash')" class="preview-btn">
              👁️ معاينة
            </button>
            <button onclick="window.open('/games/flag-clash.html?demo=true', '_blank')" class="demo-btn">
              🧪 تجربة محلية
            </button>
          </div>
        </div>




      </div>

      <div class="coming-soon">
        <div class="coming-soon-icon">🚀</div>
        <h3>المزيد قادم قريباً!</h3>
        <p>ستتم إضافة المزيد من الألعاب التفاعلية المثيرة</p>
      </div>
    </div>
  </div>

  <script src="/socket.io/socket.io.js"></script>
  <script src="/js/global-text-direction.js"></script>
  <script src="/dark-mode.js"></script>
  <script src="/js/translation.js"></script>
  <script src="/js/smooth-nav.js"></script>
  <script src="/js/background-manager.js"></script>
  <script type="module" src="/js/firebase-config.js"></script>
  <script>
    // تأخير تحميل auth-guard حتى يتم تحميل Firebase
    setTimeout(() => {
      const script = document.createElement('script');
      script.type = 'module';
      script.src = '/js/auth-guard.js';
      document.head.appendChild(script);
    }, 500);
  </script>

  <script>
    // إنشاء Socket connection
    const socket = io();
    window.socket = socket;

    // إرسال session عند الاتصال
    socket.on('connect', () => {
      console.log('🔌 Socket connected to games');

      if (window.pendingUserAuth) {
        socket.emit('userAuthenticated', window.pendingUserAuth);
        console.log('✅ Pending user session sent to server:', window.pendingUserAuth.email);
        window.pendingUserAuth = null;
      } else if (window.firebaseHelpers && window.firebaseHelpers.getCurrentUser()) {
        const user = window.firebaseHelpers.getCurrentUser();
        if (user && user.emailVerified) {
          socket.emit('userAuthenticated', {
            userId: user.uid,
            email: user.email,
            emailVerified: user.emailVerified
          });
          console.log('✅ Current user session sent to server:', user.email);
        }
      }
    });
  </script>

  <script>
    // متغيرات حالة الاشتراك
    let currentUser = null;
    let userSubscription = null;
    let hasActiveSubscription = false;
    let authCheckDone = false;
    let hasRedirected = false;

    // التحقق من المصادقة
    window.addEventListener('authStateChanged', (event) => {
      if (authCheckDone || hasRedirected) return;

      const user = event.detail.user;
      console.log('🔐 Games page - Auth state:', user?.email);

      if (!user) {
        console.log('❌ No user, redirecting to auth');
        hasRedirected = true;
        window.location.href = '/auth.html';
      } else if (!user.emailVerified) {
        console.log('❌ Email not verified, redirecting to verification');
        hasRedirected = true;
        window.location.href = '/email-verification.html';
      } else {
        console.log('✅ User authenticated and verified');
        authCheckDone = true;
        currentUser = user;

        // تحميل بيانات الاشتراك
        loadUserSubscription();
      }
    });

    // تحميل بيانات اشتراك المستخدم
    async function loadUserSubscription() {
      try {
        if (window.firebaseHelpers && currentUser) {
          userSubscription = await window.firebaseHelpers.getUserSubscription(currentUser.uid);
          hasActiveSubscription = userSubscription &&
            userSubscription.status === 'active' &&
            new Date() < (userSubscription.endDate?.toDate ? userSubscription.endDate.toDate() : new Date(userSubscription.endDate));

          console.log('User subscription status:', hasActiveSubscription ? 'Active' : 'None');
          updateGamesUI();
        }
      } catch (error) {
        console.error('Error loading user subscription:', error);
        hasActiveSubscription = false;
        updateGamesUI();
      }
    }

    // تحديث واجهة الألعاب حسب حالة الاشتراك
    function updateGamesUI() {
      // إزالة مؤشر التحميل
      const loadingIndicator = document.querySelector('.loading-indicator');
      if (loadingIndicator) {
        loadingIndicator.remove();
      }

      const gamesGrid = document.querySelector('.games-grid');
      const comingSoon = document.querySelector('.coming-soon');

      if (!hasActiveSubscription) {
        // إخفاء الألعاب وعرض رسالة القفل
        if (gamesGrid) gamesGrid.style.display = 'none';
        if (comingSoon) comingSoon.style.display = 'none';

        showGamesLockedMessage();
      } else {
        // إظهار الألعاب للمشتركين
        if (gamesGrid) gamesGrid.style.display = 'grid';
        if (comingSoon) comingSoon.style.display = 'block';

        hideGamesLockedMessage();
      }
    }

    // عرض رسالة قفل الألعاب
    function showGamesLockedMessage() {
      // إزالة الرسالة السابقة إن وجدت
      const existingMessage = document.querySelector('.games-locked-message');
      if (existingMessage) {
        existingMessage.remove();
      }

      const container = document.querySelector('.main-content .container');
      const lockedMessage = document.createElement('div');
      lockedMessage.className = 'games-locked-message';
      lockedMessage.innerHTML = `
        <div class="locked-content">
          <div class="lock-icon">🔒</div>
          <h2>قسم الألعاب مقفل</h2>
          <p class="lock-description">
            قسم الألعاب متاح فقط للمشتركين في الباقة الشاملة
          </p>

          <div class="subscription-benefits">
            <h3>🎮 ما ستحصل عليه مع الاشتراك:</h3>
            <div class="benefits-grid">
              <div class="benefit-item">
                <span class="benefit-icon">🐟</span>
                <span class="benefit-text">لعبة Fish Eat Fish التفاعلية</span>
              </div>
              <div class="benefit-item">
                <span class="benefit-icon">🏁</span>
                <span class="benefit-text">لعبة صراع الأعلام ثلاثية الأبعاد</span>
              </div>
              <div class="benefit-item">
                <span class="benefit-icon">🎯</span>
                <span class="benefit-text">ألعاب تفاعلية مع TikTok Live</span>
              </div>
              <div class="benefit-item">
                <span class="benefit-icon">⚙️</span>
                <span class="benefit-text">إعدادات متقدمة للألعاب</span>
              </div>
              <div class="benefit-item">
                <span class="benefit-icon">🎨</span>
                <span class="benefit-text">تخصيص كامل للواجهة</span>
              </div>
              <div class="benefit-item">
                <span class="benefit-icon">🚀</span>
                <span class="benefit-text">المزيد من الألعاب قادمة</span>
              </div>
            </div>
          </div>

          <div class="subscription-offer">
            <div class="price-tag">
              <span class="price">$10</span>
              <span class="period">/ شهرياً</span>
            </div>
            <p class="offer-text">باقة واحدة شاملة - جميع الميزات</p>
          </div>

          <div class="action-buttons">
            <button onclick="window.location.href='/subscriptions.html'" class="subscribe-btn">
              ⚡ اشترك الآن
            </button>
            <button onclick="showGamePreview()" class="preview-btn">
              👁️ معاينة الألعاب
            </button>
          </div>
        </div>
      `;

      // إضافة الأنماط
      const style = document.createElement('style');
      style.textContent = `
        .games-locked-message {
          text-align: center;
          padding: 40px 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 20px;
          margin: 20px 0;
          color: white;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .locked-content {
          max-width: 800px;
          margin: 0 auto;
        }

        .lock-icon {
          font-size: 4rem;
          margin-bottom: 20px;
          opacity: 0.9;
        }

        .games-locked-message h2 {
          font-size: 2.5rem;
          margin-bottom: 15px;
          font-weight: bold;
        }

        .lock-description {
          font-size: 1.2rem;
          margin-bottom: 30px;
          opacity: 0.9;
        }

        .subscription-benefits {
          background: rgba(255,255,255,0.1);
          border-radius: 15px;
          padding: 25px;
          margin: 30px 0;
          backdrop-filter: blur(10px);
        }

        .subscription-benefits h3 {
          font-size: 1.5rem;
          margin-bottom: 20px;
          color: #fff;
        }

        .benefits-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 15px;
          text-align: right;
        }

        .benefit-item {
          display: flex;
          align-items: center;
          gap: 10px;
          padding: 10px;
          background: rgba(255,255,255,0.1);
          border-radius: 10px;
          transition: transform 0.3s ease;
        }

        .benefit-item:hover {
          transform: translateY(-2px);
          background: rgba(255,255,255,0.2);
        }

        .benefit-icon {
          font-size: 1.5rem;
          min-width: 30px;
        }

        .benefit-text {
          font-size: 1rem;
          font-weight: 500;
        }

        .subscription-offer {
          margin: 30px 0;
        }

        .price-tag {
          display: inline-block;
          background: rgba(255,255,255,0.2);
          padding: 15px 25px;
          border-radius: 50px;
          margin-bottom: 10px;
          backdrop-filter: blur(10px);
        }

        .price {
          font-size: 2rem;
          font-weight: bold;
          color: #00ff7f;
        }

        .period {
          font-size: 1rem;
          opacity: 0.8;
        }

        .offer-text {
          font-size: 1.1rem;
          opacity: 0.9;
        }

        .action-buttons {
          display: flex;
          gap: 15px;
          justify-content: center;
          flex-wrap: wrap;
          margin-top: 30px;
        }

        .subscribe-btn, .preview-btn {
          padding: 15px 30px;
          border: none;
          border-radius: 50px;
          font-size: 1.1rem;
          font-weight: bold;
          cursor: pointer;
          transition: all 0.3s ease;
          font-family: 'Tajawal', sans-serif;
        }

        .subscribe-btn {
          background: linear-gradient(45deg, #00ff7f, #00cc66);
          color: white;
          box-shadow: 0 10px 20px rgba(0,255,127,0.3);
        }

        .subscribe-btn:hover {
          transform: translateY(-3px);
          box-shadow: 0 15px 30px rgba(0,255,127,0.4);
        }

        .preview-btn {
          background: rgba(255,255,255,0.2);
          color: white;
          border: 2px solid rgba(255,255,255,0.3);
          backdrop-filter: blur(10px);
        }

        .preview-btn:hover {
          background: rgba(255,255,255,0.3);
          transform: translateY(-3px);
        }

        @media (max-width: 768px) {
          .games-locked-message h2 {
            font-size: 2rem;
          }

          .benefits-grid {
            grid-template-columns: 1fr;
          }

          .action-buttons {
            flex-direction: column;
            align-items: center;
          }

          .subscribe-btn, .preview-btn {
            width: 100%;
            max-width: 300px;
          }
        }
      `;

      document.head.appendChild(style);
      container.appendChild(lockedMessage);
    }

    // إخفاء رسالة قفل الألعاب
    function hideGamesLockedMessage() {
      const existingMessage = document.querySelector('.games-locked-message');
      if (existingMessage) {
        existingMessage.remove();
      }
    }

    // عرض معاينة الألعاب للمستخدمين المجانيين
    function showGamePreview() {
      const previewContent = `
        <h3>🎮 معاينة الألعاب المتاحة</h3>
        <div style="text-align: right; margin: 20px 0;">
          <h4>🐟 Fish Eat Fish:</h4>
          <p>لعبة أسماك تفاعلية مع TikTok Live - الورود تضيف أسماك، الأسماك الأكبر تأكل الأصغر!</p>

          <h4>🏁 صراع الأعلام:</h4>
          <p>معركة الدول ثلاثية الأبعاد - الهدايا تكبر أعلام الدول والدولة الأقوى تفوز!</p>

          <h4>🚀 المزيد قادم:</h4>
          <p>ستتم إضافة المزيد من الألعاب التفاعلية المثيرة للمشتركين</p>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
          <h4 style="color: #007bff;">💡 لماذا الاشتراك؟</h4>
          <ul style="text-align: right; color: #666;">
            <li>🎮 وصول كامل لجميع الألعاب</li>
            <li>⚙️ إعدادات متقدمة وتخصيص</li>
            <li>🔄 تحديثات مستمرة وألعاب جديدة</li>
            <li>🎯 تفاعل مباشر مع TikTok Live</li>
            <li>💬 دعم فني متخصص</li>
          </ul>
        </div>
        <p style="color: #28a745; font-weight: bold;">⚡ اشترك الآن مقابل $10 شهرياً واستمتع بجميع الميزات!</p>
      `;

      // إنشاء نافذة منبثقة للمعاينة
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(5px);
      `;

      const modalContent = document.createElement('div');
      modalContent.style.cssText = `
        background: white;
        padding: 30px;
        border-radius: 15px;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        position: relative;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      `;

      modalContent.innerHTML = `
        ${previewContent}
        <div style="text-align: center; margin-top: 20px;">
          <button onclick="window.location.href='/subscriptions.html'" style="
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-family: 'Tajawal', sans-serif;
            font-weight: bold;
          ">⚡ اشترك الآن</button>
          <button onclick="this.closest('.modal').remove()" style="
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-family: 'Tajawal', sans-serif;
          ">إغلاق</button>
        </div>
      `;

      modal.className = 'modal';
      modal.appendChild(modalContent);
      document.body.appendChild(modal);

      // إغلاق عند النقر خارج المحتوى
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.remove();
        }
      });
    }

    // وظيفة معاينة الألعاب
    function previewGame(gameType) {
      let previewContent = '';

      switch(gameType) {
        case 'fish':
          previewContent = `
            <h3>🐟 Fish Eat Fish - لعبة الأسماك الاحترافية!</h3>
            <p><strong>كيفية اللعب:</strong></p>
            <ul style="text-align: right; margin: 20px 0;">
              <li>🌹 أرسل ورود في البث المباشر لإضافة أسماك جديدة</li>
              <li>🐠 كل وردة = سمكة جديدة باسم المرسل</li>
              <li>🦈 الأسماك الأكبر تأكل الأصغر لتنمو</li>
              <li>⚔️ معارك ملحمية في أعماق المحيط</li>
              <li>💬 استخدم التعليقات لتفعيل القدرات الخاصة:</li>
              <li>   • "هجوم" = وضع الصيد السريع</li>
              <li>   • "سرعة" = تسريع الأسماك</li>
              <li>   • "متوحش" = تحويل لأسماك قوية</li>
              <li>❤️ الإعجابات تعطي تسريع مؤقت لجميع الأسماك</li>
            </ul>
            <p><strong>أنواع الأسماك:</strong></p>
            <ul style="text-align: right; margin: 10px 0;">
              <li>🐟 صغيرة (1-4 ورود) - زرقاء اللون</li>
              <li>🐠 متوسطة (5-9 ورود) - خضراء اللون</li>
              <li>🦈 كبيرة (10-19 وردة) - حمراء اللون</li>
              <li>🐋 عملاقة (20+ وردة) - ذهبية اللون</li>
            </ul>
            <p><strong>المميزات الاحترافية:</strong></p>
            <ul style="text-align: right; margin: 10px 0;">
              <li>🎨 <strong>رسوميات 2D احترافية</strong> - تأثيرات بصرية مذهلة</li>
              <li>🌊 <strong>بيئة مائية واقعية</strong> - تيارات وفقاعات وإضاءة</li>
              <li>🐠 <strong>فيزياء متقدمة</strong> - حركة طبيعية وتصادمات واقعية</li>
              <li>🏆 <strong>نظام إنجازات</strong> - تقدم اللاعبين ومستويات</li>
              <li>🎵 <strong>أصوات تفاعلية</strong> - موسيقى وتأثيرات صوتية</li>
              <li>📊 <strong>إحصائيات مفصلة</strong> - لوحة نتائج وتتبع الأداء</li>
              <li>🧪 <strong>وضع تجربة محلي</strong> - اختبار بدون بث مباشر</li>
              <li>⚡ <strong>أداء محسن</strong> - 60 FPS مع مئات الأسماك</li>
            </ul>
            <p><strong>التحكم والتفاعل:</strong></p>
            <ul style="text-align: right; margin: 10px 0;">
              <li>🎮 <strong>تحكم سلس</strong> - حركة بالماوس أو اللمس</li>
              <li>📱 <strong>متوافق مع الجوال</strong> - يعمل على جميع الأجهزة</li>
              <li>🔄 <strong>تحديث فوري</strong> - تفاعل مباشر مع TikTok Live</li>
              <li>🎯 <strong>ذكاء اصطناعي</strong> - سلوك أسماك واقعي</li>
            </ul>
            <p><strong>الهدف:</strong> كن ملك المحيط بأقوى وأكثر الأسماك عدداً!</p>
            <p style="color: #00ff7f; font-weight: bold;">✨ جرب الوضع المحلي لاختبار جميع الميزات!</p>
          `;
          break;

        

        case 'flag-clash':
          previewContent = `
            <h3>🏁 صراع الأعلام - معركة الدول الثلاثية الأبعاد!</h3>
            <p><strong>كيفية اللعب:</strong></p>
            <ul style="text-align: right; margin: 20px 0;">
              <li>🎁 أرسل هدايا في البث المباشر لتكبير علم دولتك</li>
              <li>🌍 يتم تحديد الدولة تلقائياً أو يمكنك اختيارها</li>
              <li>📦 كل هدية تكبر المكعب الخاص بعلم دولتك</li>
              <li>👑 الدولة ذات العلم الأكبر تصبح "المسيطرة"</li>
              <li>⚡ تأثيرات بصرية مذهلة عند تلقي الهدايا</li>
              <li>🎵 تعليق صوتي يعلن تقدم الدول</li>
            </ul>
            <p><strong>الدول المتاحة:</strong></p>
            <ul style="text-align: right; margin: 10px 0;">
              <li>🇪🇬 مصر - أم الدنيا</li>
              <li>🇸🇦 السعودية - أرض الحرمين</li>
              <li>🇩🇿 الجزائر - بلد المليون شهيد</li>
              <li>🇲🇦 المغرب - المملكة المغربية</li>
              <li>🇺🇸 أمريكا - الولايات المتحدة</li>
              <li>🇫🇷 فرنسا - الجمهورية الفرنسية</li>
              <li>🇬🇧 بريطانيا - المملكة المتحدة</li>
              <li>🇩🇪 ألمانيا - الجمهورية الألمانية</li>
            </ul>
            <p><strong>المميزات التقنية:</strong></p>
            <ul style="text-align: right; margin: 10px 0;">
              <li>🎮 <strong>رسوميات ثلاثية الأبعاد</strong> - باستخدام Three.js</li>
              <li>🌟 <strong>إضاءة ديناميكية</strong> - تسليط الضوء على العلم المسيطر</li>
              <li>🎨 <strong>تأثيرات الجسيمات</strong> - انفجارات وألعاب نارية</li>
              <li>🔄 <strong>دوران تلقائي</strong> - عرض الأعلام من جميع الزوايا</li>
              <li>📊 <strong>لوحة النتائج</strong> - ترتيب الدول حسب القوة</li>
              <li>🎵 <strong>أصوات تفاعلية</strong> - تعليق عربي احترافي</li>
              <li>⚡ <strong>تحديث فوري</strong> - تفاعل مباشر مع TikTok Live</li>
              <li>📱 <strong>متوافق مع الجوال</strong> - يعمل على جميع الأجهزة</li>
            </ul>
            <p><strong>أنواع الهدايا والتأثيرات:</strong></p>
            <ul style="text-align: right; margin: 10px 0;">
              <li>🌹 وردة = تكبير صغير + تأثير وردي</li>
              <li>💎 ماسة = تكبير متوسط + تأثير لامع</li>
              <li>👑 تاج = تكبير كبير + تأثير ذهبي</li>
              <li>🚀 صاروخ = تكبير عملاق + انفجار ملون</li>
            </ul>
            <p><strong>التعليق الصوتي:</strong></p>
            <ul style="text-align: right; margin: 10px 0;">
              <li>🎤 "مصر تتقدم!" - عند تلقي هدية</li>
              <li>🏆 "السعودية تتصدر الآن!" - عند أخذ الصدارة</li>
              <li>⚔️ "معركة شرسة بين الدول!" - عند التنافس</li>
              <li>👑 "الجزائر تسيطر على الساحة!" - عند الهيمنة</li>
            </ul>
            <p><strong>الهدف:</strong> اجعل علم دولتك الأكبر والأقوى في الساحة!</p>
            <p style="color: #ff6b87; font-weight: bold;">🌟 تجربة ثلاثية الأبعاد مذهلة تجمع بين التقنية والوطنية!</p>
          `;
          break;

        default:
          previewContent = '<p>معاينة غير متاحة لهذه اللعبة.</p>';
      }

      // إنشاء نافذة منبثقة للمعاينة
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(5px);
      `;

      const modalContent = document.createElement('div');
      modalContent.style.cssText = `
        background: white;
        padding: 30px;
        border-radius: 15px;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        position: relative;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      `;

      modalContent.innerHTML = `
        ${previewContent}
        <button onclick="this.closest('.modal').remove()" style="
          background: #ff3b5c;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 8px;
          cursor: pointer;
          margin-top: 20px;
          font-family: 'Tajawal', sans-serif;
        ">إغلاق</button>
      `;

      modal.className = 'modal';
      modal.appendChild(modalContent);
      document.body.appendChild(modal);

      // إغلاق عند النقر خارج المحتوى
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.remove();
        }
      });
    }

    // تهيئة الصفحة
    document.addEventListener('DOMContentLoaded', () => {
      console.log('🎮 Games page loaded');

      // تأثيرات بصرية للبطاقات
      const gameCards = document.querySelectorAll('.game-card');

      gameCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
          card.style.transform = 'translateY(-10px) scale(1.02)';
        });

        card.addEventListener('mouseleave', () => {
          card.style.transform = 'translateY(0) scale(1)';
        });
      });

      // إخفاء الألعاب مؤقتاً حتى يتم التحقق من الاشتراك
      const gamesGrid = document.querySelector('.games-grid');
      const comingSoon = document.querySelector('.coming-soon');

      if (gamesGrid) gamesGrid.style.display = 'none';
      if (comingSoon) comingSoon.style.display = 'none';

      // إضافة مؤشر تحميل
      const container = document.querySelector('.main-content .container');
      const loadingIndicator = document.createElement('div');
      loadingIndicator.className = 'loading-indicator';
      loadingIndicator.innerHTML = `
        <div style="text-align: center; padding: 40px;">
          <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #ff3b5c; border-radius: 50%; animation: spin 1s linear infinite;"></div>
          <p style="margin-top: 15px; color: var(--text-secondary);">جاري التحقق من حالة الاشتراك...</p>
        </div>
        <style>
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        </style>
      `;
      container.appendChild(loadingIndicator);

      // إزالة مؤشر التحميل بعد فترة (في حالة عدم تحميل Firebase)
      setTimeout(() => {
        const indicator = document.querySelector('.loading-indicator');
        if (indicator) {
          indicator.remove();
          // إذا لم يتم تحميل Firebase، اعتبر المستخدم غير مشترك
          if (!hasActiveSubscription) {
            updateGamesUI();
          }
        }
      }, 5000);
    });
  </script>
</body>
</html>
