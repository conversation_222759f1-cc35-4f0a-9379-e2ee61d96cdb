<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>اتصل بنا - TikTok Live Overlay</title>
  <script src="/preload-theme.js"></script>
  <link rel="stylesheet" href="/dark-mode.css">
  <link rel="stylesheet" href="/css/loading-indicator.css">
  <link rel="stylesheet" href="/css/sidebar-enhanced.css">
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    :root {
      color-scheme: light dark;
    }
    body {
      font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f1 100%);
      color: #333;
      min-height: 100vh;
      display: flex;
    }

    /* تنسيقات الوضع الداكن */
    [data-theme="dark"] body {
      background: var(--bg-gradient);
      color: var(--text-color);
    }

    [data-theme="dark"] .container {
      background-color: var(--section-bg);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    [data-theme="dark"] h1,
    [data-theme="dark"] h2 {
      color: var(--primary-color);
      border-bottom-color: var(--border-color);
    }

    [data-theme="dark"] .contact-info {
      background-color: var(--card-bg);
      border: 1px solid var(--border-color);
    }

    [data-theme="dark"] .contact-item {
      color: var(--text-color);
    }

    [data-theme="dark"] .contact-icon {
      background-color: var(--primary-color);
    }

    [data-theme="dark"] label {
      color: var(--text-color);
    }

    [data-theme="dark"] input,
    [data-theme="dark"] textarea {
      background-color: var(--input-bg);
      border-color: var(--input-border);
      color: var(--text-color);
    }

    [data-theme="dark"] input:focus,
    [data-theme="dark"] textarea:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(255, 59, 92, 0.2);
    }

    [data-theme="dark"] button {
      background-color: var(--primary-color);
    }

    [data-theme="dark"] .social-icon {
      background-color: var(--hover-bg);
      color: var(--text-color);
    }

    [data-theme="dark"] .social-icon:hover {
      background-color: var(--primary-color);
      color: white;
    }

    [data-theme="dark"] .footer {
      color: var(--text-secondary);
    }

    /* تنسيقات الوضع الداكن للشريط الجانبي */
    [data-theme="dark"] .sidebar {
      background: var(--sidebar-bg);
      border-left-color: var(--sidebar-border);
      box-shadow: 3px 0 15px var(--shadow-color);
    }

    [data-theme="dark"] .nav-menu a {
      color: var(--text-color);
      border-color: var(--border-color);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    [data-theme="dark"] .nav-menu a:hover {
      background-color: var(--hover-bg);
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
    }

    .sidebar {
      width: 250px;
      background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
      box-shadow: 3px 0 15px rgba(0, 0, 0, 0.08);
      padding: 0;
      position: fixed;
      height: 100%;
      right: 0;
      top: 0;
      overflow-y: auto;
      z-index: 1000;
      border-left: 1px solid rgba(0, 0, 0, 0.05);
      border-top-left-radius: 15px;
      border-bottom-left-radius: 15px;
      opacity: 0.95 !important;
    }

    .logo {
      text-align: center;
      padding: 25px 15px;
      margin-bottom: 10px;
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      box-shadow: 0 4px 10px rgba(255, 59, 92, 0.2);
      position: relative;
      border-top-left-radius: 15px;
    }

    .logo h3 {
      color: white;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .logo::after {
      content: '';
      position: absolute;
      bottom: -10px;
      right: 50%;
      transform: translateX(50%);
      width: 20px;
      height: 20px;
      background: #ff3b5c;
      transform: rotate(45deg) translateX(50%);
      box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
      z-index: -1;
    }

    .logo img {
      max-width: 80%;
      height: auto;
    }

    .nav-menu {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 20px 15px;
    }

    .nav-menu a {
      display: flex;
      align-items: center;
      padding: 14px 18px;
      color: #444;
      text-decoration: none;
      border-radius: 12px;
      transition: all 0.3s ease;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
      border: 1px solid rgba(0, 0, 0, 0.03);
    }

    .nav-menu a::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 3px;
      height: 100%;
      background: #ff3b5c;
      transform: scaleY(0);
      transition: transform 0.3s ease;
    }

    .nav-menu a:hover {
      background-color: #f0f4f8;
      color: #ff3b5c;
      transform: translateY(-2px);
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
    }

    .nav-menu a:hover::before {
      transform: scaleY(1);
    }

    .nav-menu a.active {
      background: linear-gradient(45deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      box-shadow: 0 5px 15px rgba(255, 59, 92, 0.25);
      border: none;
    }

    .nav-menu a.active::before {
      transform: scaleY(0);
    }

    .nav-icon {
      margin-left: 15px;
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .nav-menu a:hover .nav-icon {
      background-color: rgba(255, 59, 92, 0.1);
      transform: rotate(5deg);
    }

    .nav-menu a.active .nav-icon {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .main-content {
      flex: 1;
      margin-right: 250px;
      padding: 20px;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      border-radius: 15px;
      padding: 30px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
      margin-top: 30px;
      opacity: 0.95 !important;
    }

    h1 {
      color: #ff3b5c;
      text-align: center;
      font-size: 2.2rem;
      margin-bottom: 30px;
      font-weight: 700;
      border-bottom: 2px solid #f2f2f2;
      padding-bottom: 15px;
    }

    p {
      line-height: 1.6;
      margin-bottom: 20px;
    }

    .contact-info {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 10px;
      margin-bottom: 30px;
      opacity: 0.95 !important;
    }

    .contact-item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }

    .contact-icon {
      background-color: #ff3b5c;
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 15px;
      font-size: 18px;
    }

    .contact-form {
      margin-top: 30px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #444;
    }

    input, textarea {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 16px;
      box-sizing: border-box;
      transition: border-color 0.3s, box-shadow 0.3s;
      font-family: 'Tajawal', sans-serif;
      background-color: #ffffff;
      color: #333;
    }

    input:focus, textarea:focus {
      outline: none;
      border-color: #ff3b5c;
      box-shadow: 0 0 0 3px rgba(255, 59, 92, 0.1);
    }

    textarea {
      min-height: 150px;
    }

    button {
      background-color: #ff3b5c;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s;
      font-weight: 500;
      box-shadow: 0 4px 6px rgba(255, 59, 92, 0.2);
      font-family: 'Tajawal', sans-serif;
    }

    button:hover {
      background-color: #e6354f;
      transform: translateY(-2px);
      box-shadow: 0 6px 8px rgba(255, 59, 92, 0.25);
    }

    button:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(255, 59, 92, 0.25);
    }

    .social-media {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin-top: 30px;
    }

    .social-icon {
      background-color: #f0f2f5;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      color: #555;
      text-decoration: none;
      transition: all 0.3s;
    }

    .social-icon:hover {
      background-color: #ff3b5c;
      color: white;
      transform: translateY(-3px);
    }

    .footer {
      text-align: center;
      margin-top: 40px;
      color: #777;
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="logo">
      <h3>StreamTok</h3>
    </div>
    <div class="nav-menu">
      <a href="/">
        <span class="nav-icon">🔌</span>
        الاتصال بـ TikTok Live
      </a>
      <a href="/mappings.html">
        <span class="nav-icon">🛠️</span>
        ربط الهدايا بالإجراءات
      </a>
      <a href="/tts-comments.html">
        <span class="nav-icon">🔊</span>
        قراءة التعليقات
      </a>
      <a href="/games.html">
        <span class="nav-icon">🎯</span>
        الألعاب
      </a>
      <a href="/settings.html">
        <span class="nav-icon">⚙️</span>
        الإعدادات
      </a>
      <a href="/profiles.html">
        <span class="nav-icon">👤</span>
        الملفات الشخصية
      </a>
      <a href="/subscriptions.html">
        <span class="nav-icon">⚡</span>
        الاشتراكات
      </a>
      <a href="/contact.html" class="active">
        <span class="nav-icon">📞</span>
        اتصل بنا
      </a>
      <a href="/overlay.html" target="_blank">
        <span class="nav-icon">🖥️</span>
        فتح Overlay
      </a>
      <a href="/overlay-bar.html">
        <span class="nav-icon">🎮</span>
        Overlay Bar
      </a>
    </div>
  </div>

  <div class="main-content">
    <div class="container">
      <h1>اتصل بنا</h1>

      <p>نحن نرحب بالاستفسارات والاقتراحات والملاحظات. يمكنك التواصل معنا من خلال أي من وسائل الاتصال التالية أو باستخدام نموذج الاتصال أدناه.</p>

      <div class="contact-info">
        <div class="contact-item">
          <div class="contact-icon">📧</div>
          <div>
            <strong>البريد الإلكتروني:</strong>
            <p><EMAIL></p>
          </div>
        </div>

        <div class="contact-item">
          <div class="contact-icon">📱</div>
          <div>
            <strong>الهاتف:</strong>
            <p>+0201140018541</p>
          </div>
        </div>

        <div class="contact-item">
          <div class="contact-icon">📍</div>
          <div>
            <strong>العنوان:</strong>
            <p>مصر . القاهرة . حلوان</p>
          </div>
        </div>
      </div>

      <div class="contact-form">
        <h2>نموذج الاتصال</h2>

        <form id="contactForm">
          <div class="form-group">
            <label for="name">الاسم الكامل</label>
            <input type="text" id="name" name="name" required>
          </div>

          <div class="form-group">
            <label for="email">البريد الإلكتروني</label>
            <input type="email" id="email" name="email" required>
          </div>

          <div class="form-group">
            <label for="subject">الموضوع</label>
            <input type="text" id="subject" name="subject" required>
          </div>

          <div class="form-group">
            <label for="message">الرسالة</label>
            <textarea id="message" name="message" required></textarea>
          </div>

          <button type="submit">إرسال الرسالة</button>
        </form>
      </div>

      <div class="social-media">
        <a href="#" class="social-icon">
          <span>📱</span>
        </a>
        <a href="#" class="social-icon">
          <span>💬</span>
        </a>
        <a href="#" class="social-icon">
          <span>🐦</span>
        </a>
        <a href="#" class="social-icon">
          <span>📸</span>
        </a>
      </div>

      <div class="footer">
        TikTok Live Overlay &copy; 2023
      </div>
    </div>
  </div>

  <script src="/socket.io/socket.io.js"></script>
  <script>
    const socket = io();

    document.getElementById('contactForm').addEventListener('submit', function(event) {
      event.preventDefault();

      const formData = {
        name: document.getElementById('name').value,
        email: document.getElementById('email').value,
        subject: document.getElementById('subject').value,
        message: document.getElementById('message').value
      };

      socket.emit('submitContactForm', formData);

      // إظهار رسالة تأكيد (يمكن تحسينها لاحقًا)
      alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريبًا.');

      // إعادة ضبط النموذج
      this.reset();
    });
  </script>
  <script src="/js/global-text-direction.js"></script>
  <script src="/dark-mode.js"></script>
  <script src="/js/translation.js"></script>
  <script src="/js/smooth-nav.js"></script>
  <script src="/js/background-manager.js"></script>
</body>
</html>