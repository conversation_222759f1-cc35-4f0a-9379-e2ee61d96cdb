// Auth Guard - نظام حماية الصفحات
// يتحقق من تسجيل دخول المستخدم ويوجهه للصفحة المناسبة

class AuthGuard {
  constructor() {
    this.currentUser = null;
    this.userSubscription = null;
    this.isInitialized = false;
    this.isRedirecting = false;
    
    // صفحات لا تحتاج تسجيل دخول
    this.publicPages = [
      '/auth.html',
      '/email-verification.html',
      '/contact.html'
    ];
    
    // صفحات تحتاج اشتراك مميز
    this.premiumPages = [
      '/games.html',
      '/games/flag-clash.html',
      '/games/fish-eat-fish.html'
    ];
    
    this.init();
  }
  
  init() {
    // انتظار تحميل Firebase
    if (typeof window.firebaseAuth === 'undefined') {
      setTimeout(() => this.init(), 100);
      return;
    }
    
    // الاستماع لتغييرات حالة المصادقة
    window.addEventListener('authStateChanged', (event) => {
      this.handleAuthStateChange(event.detail.user);
    });
    
    // التحقق من الحالة الحالية
    this.currentUser = window.firebaseHelpers.getCurrentUser();
    if (this.currentUser) {
      this.loadUserData();
    } else {
      this.checkPageAccess();
    }
  }
  
  async handleAuthStateChange(user) {
    this.currentUser = user;

    // تجنب التوجيه إذا كنا في صفحات المصادقة أو التأكيد
    const currentPath = window.location.pathname;
    const isAuthRelatedPage = this.isPublicPage(currentPath);

    if (user) {
      await this.loadUserData();
      this.updateUI();

      // فقط تحقق من الوصول إذا لم نكن في صفحة مصادقة
      if (!isAuthRelatedPage && this.isInitialized) {
        this.checkPageAccess();
      }
    } else {
      this.userSubscription = null;

      // فقط تحقق من الوصول إذا لم نكن في صفحة مصادقة
      if (!isAuthRelatedPage) {
        this.checkPageAccess();
      }

      this.updateUI();
    }

    this.isInitialized = true;
  }
  
  async loadUserData() {
    if (!this.currentUser) return;
    
    try {
      // تحميل بيانات الاشتراك
      this.userSubscription = await window.firebaseHelpers.getUserSubscription(this.currentUser.uid);
      
      console.log('User subscription loaded:', this.userSubscription);
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  }
  
  checkPageAccess() {
    // تجنب التحقق المتكرر
    if (this.isRedirecting) return true;

    const currentPath = window.location.pathname;
    console.log('🔍 Checking page access for:', currentPath, 'User:', this.currentUser?.email);

    // إذا كان المستخدم غير مسجل دخول
    if (!this.currentUser) {
      // إذا لم تكن صفحة عامة، وجه لصفحة تسجيل الدخول
      if (!this.isPublicPage(currentPath)) {
        console.log('❌ No user, redirecting to auth');
        this.redirectToAuth();
        return false;
      }
    } else {
      // التحقق من تأكيد البريد الإلكتروني
      if (!this.currentUser.emailVerified && currentPath !== '/email-verification.html') {
        console.log('❌ Email not verified, redirecting to verification page');
        this.isRedirecting = true;
        setTimeout(() => {
          window.location.href = '/email-verification.html';
        }, 100);
        return false;
      }

      // إذا كان المستخدم مسجل دخول وفي صفحة تسجيل الدخول
      if (currentPath === '/auth.html') {
        console.log('✅ User logged in, redirecting from auth page');
        this.redirectToHome();
        return false;
      }

      // إذا كان البريد مؤكد وفي صفحة التأكيد، وجه للرئيسية
      if (this.currentUser.emailVerified && currentPath === '/email-verification.html') {
        console.log('✅ Email verified, redirecting from verification page');
        this.redirectToHome();
        return false;
      }

      // التحقق من صفحات الاشتراك المميز
      if (this.isPremiumPage(currentPath)) {
        if (!this.hasActiveSubscription()) {
          this.redirectToSubscriptions();
          return false;
        }
      }
    }

    console.log('✅ Page access granted');
    return true;
  }
  
  isPublicPage(path) {
    return this.publicPages.some(page => path === page || path.endsWith(page));
  }
  
  isPremiumPage(path) {
    return this.premiumPages.some(page => path === page || path.endsWith(page));
  }
  
  hasActiveSubscription() {
    if (!this.userSubscription) return false;
    
    // التحقق من انتهاء صلاحية الاشتراك
    const now = new Date();
    const endDate = this.userSubscription.endDate.toDate ? 
      this.userSubscription.endDate.toDate() : 
      new Date(this.userSubscription.endDate);
    
    return this.userSubscription.status === 'active' && now < endDate;
  }
  
  redirectToAuth() {
    if (this.isRedirecting) return;
    if (window.location.pathname !== '/auth.html') {
      console.log('🔄 Redirecting to auth page');
      this.isRedirecting = true;

      // تأخير قصير لتجنب التضارب
      setTimeout(() => {
        window.location.href = '/auth.html';
      }, 100);
    }
  }

  redirectToHome() {
    if (this.isRedirecting) return;
    // تحديد الصفحة الرئيسية بوضوح
    const homePage = '/connection.html';
    if (window.location.pathname !== homePage) {
      console.log('🔄 Redirecting to home page');
      this.isRedirecting = true;

      // تأخير قصير لتجنب التضارب
      setTimeout(() => {
        window.location.href = homePage;
      }, 100);
    }
  }
  
  redirectToSubscriptions() {
    // عرض رسالة تنبيه قبل التوجيه
    if (confirm('هذه الميزة تتطلب اشتراك مميز. هل تريد الاشتراك الآن؟')) {
      window.location.href = '/subscriptions.html';
    } else {
      window.location.href = '/';
    }
  }
  
  updateUI() {
    this.updateNavigation();
    this.updateUserInfo();
  }
  
  updateNavigation() {
    // إضافة/إزالة روابط بناءً على حالة المستخدم
    const sidebar = document.querySelector('.nav-menu');
    if (!sidebar) return;
    
    // إزالة روابط المصادقة الموجودة
    const existingAuthLinks = sidebar.querySelectorAll('.auth-link');
    existingAuthLinks.forEach(link => link.remove());
    
    if (this.currentUser) {
      // إضافة رابط تسجيل الخروج
      const logoutLink = this.createNavLink('🚪', 'تسجيل الخروج', '#', 'auth-link');
      logoutLink.addEventListener('click', (e) => {
        e.preventDefault();
        this.logout();
      });
      sidebar.appendChild(logoutLink);
      
    } else {
      // إضافة رابط تسجيل الدخول
      const loginLink = this.createNavLink('🔑', 'تسجيل الدخول', '/auth.html', 'auth-link');
      sidebar.appendChild(loginLink);
    }
  }
  
  createNavLink(icon, text, href, className = '') {
    const link = document.createElement('a');
    link.href = href;
    link.className = className;
    link.innerHTML = `
      <span class="nav-icon">${icon}</span>
      ${text}
    `;
    return link;
  }
  
  updateUserInfo() {
    // تحديث معلومات المستخدم في الواجهة
    const userInfoElements = document.querySelectorAll('.user-info');
    
    userInfoElements.forEach(element => {
      if (this.currentUser) {
        element.innerHTML = `
          <div class="user-details">
            <span class="user-name">${this.currentUser.displayName || this.currentUser.email}</span>
            <span class="user-subscription">${this.getSubscriptionStatus()}</span>
          </div>
        `;
        element.style.display = 'block';
      } else {
        element.style.display = 'none';
      }
    });
  }
  
  getSubscriptionStatus() {
    if (!this.userSubscription) {
      return 'بدون اشتراك';
    }
    
    if (this.hasActiveSubscription()) {
      return `${this.userSubscription.plan === 'premium' ? 'مميز' : 'أساسي'} - نشط`;
    } else {
      return 'منتهي الصلاحية';
    }
  }
  
  async logout() {
    try {
      await window.firebaseFunctions.signOut(window.firebaseAuth);
      window.location.href = '/auth.html';
    } catch (error) {
      console.error('Logout error:', error);
      alert('حدث خطأ أثناء تسجيل الخروج');
    }
  }
  
  // دوال مساعدة للاستخدام في الصفحات الأخرى
  getCurrentUser() {
    return this.currentUser;
  }
  
  getUserSubscription() {
    return this.userSubscription;
  }
  
  isLoggedIn() {
    return !!this.currentUser;
  }
  
  hasFeatureAccess(feature) {
    if (!this.currentUser) return false;
    
    // الميزات الأساسية متاحة للجميع
    const basicFeatures = ['connection', 'comments', 'basic-alerts'];
    if (basicFeatures.includes(feature)) return true;
    
    // الميزات المتقدمة تحتاج اشتراك
    const premiumFeatures = ['games', 'advanced-alerts', 'custom-overlays'];
    if (premiumFeatures.includes(feature)) {
      return this.hasActiveSubscription();
    }
    
    return false;
  }
}

// إنشاء instance عام
window.authGuard = new AuthGuard();

// تصدير للاستخدام في الصفحات الأخرى
window.AuthGuard = AuthGuard;
