<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>قراءة التعليقات</title>
  <!-- تحميل سكريبت الوضع المظلم قبل أي شيء آخر لمنع الوميض -->
  <script src="/preload-theme.js"></script>
  <link rel="stylesheet" href="/dark-mode.css">
  <link rel="stylesheet" href="/css/loading-indicator.css">
  <link rel="stylesheet" href="/css/sidebar-enhanced.css">
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    :root {
      color-scheme: light dark;
    }
    body {
      font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--bg-gradient);
      color: var(--text-color);
      transition: background 0.3s ease, color 0.3s ease;
      min-height: 100vh;
      display: flex;
    }

    .sidebar {
      width: 250px;
      background: var(--sidebar-bg);
      box-shadow: 3px 0 15px var(--shadow-color);
      padding: 0;
      position: fixed;
      height: 100%;
      right: 0;
      top: 0;
      overflow-y: auto;
      z-index: 1000;
      border-left: 1px solid var(--sidebar-border);
      border-top-left-radius: 15px;
      border-bottom-left-radius: 15px;
      transition: background 0.3s ease, box-shadow 0.3s ease;
      opacity: 0.95 !important;
    }

    .logo {
      text-align: center;
      padding: 25px 15px;
      margin-bottom: 10px;
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      box-shadow: 0 4px 10px rgba(255, 59, 92, 0.2);
      position: relative;
      border-top-left-radius: 15px;
    }

    .logo h3 {
      color: white;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .logo::after {
      content: '';
      position: absolute;
      bottom: -10px;
      right: 50%;
      transform: translateX(50%);
      width: 20px;
      height: 20px;
      background: #ff3b5c;
      transform: rotate(45deg) translateX(50%);
      box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
      z-index: -1;
    }

    .logo img {
      max-width: 80%;
      height: auto;
    }

    .nav-menu {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 20px 15px;
    }

    .nav-menu a {
      display: flex;
      align-items: center;
      padding: 14px 18px;
      color: #444;
      text-decoration: none;
      border-radius: 12px;
      transition: all 0.3s ease;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
      border: 1px solid rgba(0, 0, 0, 0.03);
    }

    .nav-menu a::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 3px;
      height: 100%;
      background: #ff3b5c;
      transform: scaleY(0);
      transition: transform 0.3s ease;
    }

    .nav-menu a:hover {
      background-color: #f0f4f8;
      color: #ff3b5c;
      transform: translateY(-2px);
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
    }

    .nav-menu a:hover::before {
      transform: scaleY(1);
    }

    .nav-menu a.active {
      background: linear-gradient(45deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      box-shadow: 0 5px 15px rgba(255, 59, 92, 0.25);
      border: none;
    }

    .nav-menu a.active::before {
      transform: scaleY(0);
    }

    .nav-icon {
      margin-left: 15px;
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .nav-menu a:hover .nav-icon {
      background-color: rgba(255, 59, 92, 0.1);
      transform: rotate(5deg);
    }

    .nav-menu a.active .nav-icon {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .main-content {
      flex: 1;
      margin-right: 250px;
      padding: 20px;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: var(--section-bg);
      border-radius: 15px;
      padding: 30px;
      box-shadow: 0 10px 30px var(--shadow-color);
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
      margin-top: 30px;
      margin-bottom: 30px;
      opacity: 0.95 !important;
    }

    h1 {
      color: var(--primary-color);
      text-align: center;
      font-size: 2.2rem;
      margin-bottom: 30px;
      font-weight: 700;
      border-bottom: 2px solid var(--border-color);
      padding-bottom: 15px;
    }

    .section {
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid var(--border-color);
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-color);
    }

    input, select, textarea {
      width: 100%;
      padding: 12px;
      border: 1px solid var(--input-border);
      border-radius: 8px;
      font-size: 16px;
      box-sizing: border-box;
      transition: border-color 0.3s, box-shadow 0.3s, background-color 0.3s, color 0.3s;
      font-family: 'Tajawal', sans-serif;
      background-color: var(--input-bg);
      color: var(--text-color);
    }

    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(255, 59, 92, 0.1);
    }

    button {
      background: var(--primary-gradient);
      color: var(--button-text);
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s;
      font-weight: 500;
      box-shadow: 0 4px 6px rgba(255, 59, 92, 0.2);
      font-family: 'Tajawal', sans-serif;
    }

    button:hover {
      background-color: #e6354f;
      transform: translateY(-2px);
      box-shadow: 0 6px 8px rgba(255, 59, 92, 0.25);
    }

    button:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(255, 59, 92, 0.25);
    }

    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
      box-shadow: none;
      transform: none;
    }

    button.secondary {
      background-color: #6c757d;
      box-shadow: 0 4px 6px rgba(108, 117, 125, 0.2);
    }

    button.secondary:hover {
      background-color: #5a6268;
      box-shadow: 0 6px 8px rgba(108, 117, 125, 0.25);
    }

    .status {
      padding: 12px;
      border-radius: 8px;
      margin-top: 15px;
      font-weight: 500;
      transition: all 0.3s;
    }

    .enabled {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .disabled {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .footer {
      text-align: center;
      margin-top: 40px;
      color: #777;
      font-size: 0.9rem;
    }

    /* تنسيقات خاصة بصفحة قراءة التعليقات */
    .voice-selector {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 20px;
    }

    .voice-option {
      display: flex;
      align-items: center;
      padding: 8px 15px;
      border: 1px solid #ddd;
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .voice-option:hover {
      background-color: var(--hover-bg);
    }

    .voice-option.selected {
      background: var(--primary-gradient);
      color: var(--button-text);
      border-color: var(--primary-color);
    }

    .voice-option input {
      margin-left: 8px;
      width: auto;
    }

    .filter-settings {
      background-color: var(--table-header-bg);
      border-radius: 10px;
      padding: 15px;
      margin-bottom: 20px;
      transition: background-color 0.3s ease;
    }

    .comment-example {
      background-color: var(--table-header-bg);
      border-radius: 8px;
      padding: 15px;
      margin-top: 15px;
      border-right: 4px solid var(--primary-color);
      transition: background-color 0.3s ease;
      position: relative;
    }

    .test-voice-button {
      background-color: #6c757d;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 14px;
      margin-top: 10px;
    }

    .range-slider {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .range-slider input[type="range"] {
      flex: 1;
    }

    .range-value {
      min-width: 40px;
      text-align: center;
      font-weight: bold;
    }

    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 34px;
    }

    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 34px;
    }

    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }

    input:checked + .toggle-slider {
      background-color: #ff3b5c;
    }

    input:checked + .toggle-slider:before {
      transform: translateX(26px);
    }

    .test-voice-button.success {
      background-color: #28a745;
    }

    .test-voice-button.error {
      background-color: #dc3545;
    }

    /* تحسين ظهور قائمة الأصوات المنسدلة */
    .voice-selector-dropdown {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 16px;
      margin-bottom: 15px;
      direction: rtl;
      font-family: 'Tajawal', sans-serif;
      background-color: #f9f9f9;
      appearance: auto;
      -webkit-appearance: auto;
      -moz-appearance: auto;
    }

    /* تنسيق مربع البحث */
    #voice-search {
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 16px;
      font-family: 'Tajawal', sans-serif;
      background-color: #f5f5f5;
    }



    #test-text {
      direction: rtl;
      text-align: right;
      font-family: 'Tajawal', sans-serif;
      resize: vertical;
    }

    /* تنسيق صندوق الملاحظات */
    .note-box {
      background-color: #fff3cd;
      border: 1px solid #ffeeba;
      color: #856404;
      padding: 15px;
      border-radius: 6px;
      margin-top: 15px;
      font-size: 0.95rem;
    }

    .note-box strong {
      color: #7d5a00;
    }

    /* لوحة المعلومات */
    .info-panel {
      background-color: #e7f5ff;
      border: 1px solid #b8daff;
      color: #004085;
      padding: 15px;
      border-radius: 6px;
      margin-bottom: 20px;
    }

    .info-panel h4 {
      margin-top: 0;
      margin-bottom: 10px;
      color: #0056b3;
    }



    /* تنسيق حالة التحميل */
    .status.loading {
      background-color: #cce5ff;
      border-color: #b8daff;
      color: #004085;
      position: relative;
      padding-right: 30px;
    }

    .status.loading:after {
      content: "";
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      width: 15px;
      height: 15px;
      border: 2px solid #007bff;
      border-radius: 50%;
      border-top-color: transparent;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      to { transform: translateY(-50%) rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="logo">
      <h3>StreamTok</h3>
    </div>
    <div class="nav-menu">
      <a href="/">
        <span class="nav-icon">🔌</span>
        الاتصال بـ TikTok Live
      </a>
      <a href="/mappings.html">
        <span class="nav-icon">🛠️</span>
        ربط الهدايا بالإجراءات
      </a>
      <a href="/tts-comments.html" class="active">
        <span class="nav-icon">🔊</span>
        قراءة التعليقات
      </a>
      <a href="/games.html">
        <span class="nav-icon">🎯</span>
        الألعاب
      </a>
      <a href="/settings.html">
        <span class="nav-icon">⚙️</span>
        الإعدادات
      </a>
      <a href="/profiles.html">
        <span class="nav-icon">👤</span>
        الملفات الشخصية
      </a>
      <a href="/subscriptions.html">
        <span class="nav-icon">⚡</span>
        الاشتراكات
      </a>
      <a href="/contact.html">
        <span class="nav-icon">📞</span>
        اتصل بنا
      </a>
      <a href="/overlay.html" target="_blank">
        <span class="nav-icon">🖥️</span>
        فتح Overlay
      </a>
      <a href="/overlay-bar.html">
        <span class="nav-icon">🎮</span>
        Overlay Bar
      </a>
    </div>
  </div>

  <div class="main-content">
    <div class="container">
      <h1>قراءة التعليقات</h1>

      <div class="section">
        <div class="form-group">
          <label for="tts-toggle">تفعيل قراءة التعليقات:</label>
          <label class="toggle-switch">
            <input type="checkbox" id="tts-toggle">
            <span class="toggle-slider"></span>
          </label>
        </div>

        <div class="status disabled" id="tts-status">
          قراءة التعليقات غير مفعلة
        </div>


      </div>

      <div class="section">
        <h3>اختيار النموذج الصوتي</h3>
        <p>اختر النموذج الصوتي المناسب لقراءة التعليقات:</p>

        <div class="form-group">
          <label for="voice-select">اختر النموذج الصوتي:</label>
          <input type="text" id="voice-search" placeholder="ابحث عن نموذج..." style="width: 100%; margin-bottom: 10px;">
          <select id="voice-select" class="voice-selector-dropdown" style="height: auto;">
            <!-- سيتم ملء هذه القائمة ديناميكياً بالنماذج الصوتية المتاحة -->
          </select>
        </div>



        <div class="form-group">
          <label for="voice-speed">سرعة القراءة:</label>
          <div class="range-slider">
            <input type="range" id="voice-speed" min="0.5" max="2" step="0.1" value="1">
            <span class="range-value" id="speed-value">1.0</span>
          </div>
        </div>

        <div class="form-group">
          <label for="voice-volume">مستوى الصوت:</label>
          <div class="range-slider">
            <input type="range" id="voice-volume" min="1" max="100" step="1" value="50">
            <span class="range-value" id="volume-value">50</span>
          </div>
        </div>

        <div class="form-group">
          <label for="test-text">نص الاختبار:</label>
          <textarea id="test-text" rows="3" placeholder="اكتب هنا النص الذي تريد اختباره"></textarea>
        </div>

        <div id="model-loading-status" class="status" style="display: none;"></div>

        <button class="test-voice-button" id="test-voice-btn">اختبار الصوت</button>
      </div>

      <div class="section">
        <h3>إعدادات التصفية</h3>
        <p>تخصيص التعليقات التي سيتم قراءتها:</p>

        <div class="filter-settings">
          <div class="form-group">
            <label for="min-length">الحد الأدنى لطول التعليق (عدد الأحرف):</label>
            <input type="number" id="min-length" min="1" max="100" value="3">
          </div>

          <div class="form-group">
            <label for="max-length">الحد الأقصى لطول التعليق (عدد الأحرف):</label>
            <input type="number" id="max-length" min="10" max="500" value="100">
          </div>

          <div class="form-group">
            <label for="read-rate">معدل قراءة التعليقات (ثانية):</label>
            <input type="number" id="read-rate" min="1" max="60" value="5">
            <small>القراءة كل هذا العدد من الثواني</small>
          </div>

          <div class="form-group">
            <label for="blocked-words">كلمات محظورة (تفصل بـ ،):</label>
            <textarea id="blocked-words" rows="2" placeholder="أدخل الكلمات التي تريد منع قراءتها مفصولة بفاصلة"></textarea>
          </div>
        </div>

        <button id="save-settings-btn">حفظ الإعدادات</button>
      </div>


      </div>

      <div class="footer">
        StreamTok &copy; 2025
        - By : Abdelrahman Mohamed
      </div>
    </div>
  </div>

  <script src="/socket.io/socket.io.js"></script>
  <script type="module" src="/js/firebase-config.js"></script>
  <script src="/tts-voices.js"></script>
  <script>
    const socket = io();

    // جعل socket متاح عالمي<|im_start|>
    window.socket = socket;

    // إرسال session عند الاتصال إذا كان المستخدم مسجل دخول
    socket.on('connect', () => {
      console.log('🔌 Socket connected to TTS');

      // إرسال session إذا كان هناك مستخدم مسجل دخول
      if (window.pendingUserAuth) {
        socket.emit('userAuthenticated', window.pendingUserAuth);
        console.log('✅ Pending user session sent to server:', window.pendingUserAuth.email);
        window.pendingUserAuth = null;
      } else if (window.firebaseHelpers && window.firebaseHelpers.getCurrentUser()) {
        const user = window.firebaseHelpers.getCurrentUser();
        if (user && user.emailVerified) {
          socket.emit('userAuthenticated', {
            userId: user.uid,
            email: user.email,
            emailVerified: user.emailVerified
          });
          console.log('✅ Current user session sent to server:', user.email);
        }
      }
    });

    // متغيرات الاشتراك
    let currentUser = null;
    let userSubscription = null;
    let hasActiveSubscription = false;

    // العناصر في DOM
    const ttsToggle = document.getElementById('tts-toggle');
    const ttsStatus = document.getElementById('tts-status');
    const voiceSelect = document.getElementById('voice-select');
    // لم نعد نستخدم modelSpecs
    const voiceSpeed = document.getElementById('voice-speed');
    const speedValue = document.getElementById('speed-value');
    const voiceVolume = document.getElementById('voice-volume');
    const volumeValue = document.getElementById('volume-value');
    const testText = document.getElementById('test-text');
    const testVoiceBtn = document.getElementById('test-voice-btn');
    const minLength = document.getElementById('min-length');
    const maxLength = document.getElementById('max-length');
    const readRate = document.getElementById('read-rate');
    const blockedWords = document.getElementById('blocked-words');
    const saveSettingsBtn = document.getElementById('save-settings-btn');
    const modelLoadingStatus = document.getElementById('model-loading-status');

    // تحميل بيانات اشتراك المستخدم
    async function loadUserSubscription() {
      try {
        if (window.firebaseHelpers && currentUser) {
          userSubscription = await window.firebaseHelpers.getUserSubscription(currentUser.uid);
          hasActiveSubscription = userSubscription &&
            userSubscription.status === 'active' &&
            new Date() < (userSubscription.endDate?.toDate ? userSubscription.endDate.toDate() : new Date(userSubscription.endDate));

          console.log('User subscription status:', hasActiveSubscription ? 'Active' : 'None');

          // تفعيل الحماية إذا كان هناك صوت محفوظ
          if (hasActiveSubscription && userSelectedVoice && userSelectedVoice !== 'ar-EG-ShakirNeural') {
            preventServerOverride = true;
          }

          updateVoiceSelectionUI();

          // إعادة تحديث قائمة الأصوات بعد تحديث حالة الاشتراك
          if (typeof updateModelsList === 'function') {
            updateModelsList();
          }

          // إعادة بناء القائمة إذا كانت موجودة بالفعل
          if (voiceSelect && voiceSelect.options.length > 0) {
            socket.emit('getTtsSettings');
          }
        }
      } catch (error) {
        console.error('Error loading user subscription:', error);
        hasActiveSubscription = false;
        updateVoiceSelectionUI();

        // إعادة تحديث قائمة الأصوات في حالة الخطأ أيض<|im_start|>
        if (typeof updateModelsList === 'function') {
          updateModelsList();
        }
      }
    }

    // تحديث واجهة اختيار الصوت حسب حالة الاشتراك
    function updateVoiceSelectionUI() {
      const voiceSearch = document.getElementById('voice-search');

      if (!hasActiveSubscription) {
        // للمستخدمين غير المشتركين، فرض استخدام صوت شاكر
        voiceSelect.value = 'ar-EG-ShakirNeural';
        socket.emit('updateTtsSetting', { voice: 'ar-EG-ShakirNeural' });

        // مسح أي اختيار محفوظ
        userSelectedVoice = null;
        preventServerOverride = false;
        localStorage.removeItem('selectedVoice');

        // تعطيل البحث للمستخدمين غير المشتركين
        if (voiceSearch) {
          voiceSearch.disabled = true;
          voiceSearch.placeholder = 'اشتراك مطلوب للبحث في الأصوات';
          voiceSearch.style.backgroundColor = '#f8f9fa';
          voiceSearch.style.color = '#6c757d';
        }

        // تعطيل القائمة المنسدلة
        voiceSelect.disabled = true;
        voiceSelect.style.backgroundColor = '#f8f9fa';
        voiceSelect.style.color = '#6c757d';

        // إضافة رسالة تنبيه
        showVoiceLimitMessage();
      } else {
        // تفعيل البحث للمشتركين
        if (voiceSearch) {
          voiceSearch.disabled = false;
          voiceSearch.placeholder = 'ابحث عن نموذج...';
          voiceSearch.style.backgroundColor = '';
          voiceSearch.style.color = '';
        }

        // تفعيل القائمة المنسدلة
        voiceSelect.disabled = false;
        voiceSelect.style.backgroundColor = '';
        voiceSelect.style.color = '';

        // إخفاء رسالة التنبيه
        hideVoiceLimitMessage();
      }
    }

    // إظهار رسالة تحديد الأصوات
    function showVoiceLimitMessage() {
      // البحث عن رسالة موجودة أو إنشاء واحدة جديدة
      let limitMessage = document.getElementById('voice-limit-message');
      if (!limitMessage) {
        limitMessage = document.createElement('div');
        limitMessage.id = 'voice-limit-message';
        limitMessage.style.cssText = `
          background-color: #fff3cd;
          border: 1px solid #ffeaa7;
          border-radius: 5px;
          padding: 15px;
          margin: 10px 0;
          color: #856404;
          text-align: center;
        `;
        limitMessage.innerHTML = `
          <strong>ℹ️ النسخة المجانية:</strong> متاح صوت "شاكر" فقط.
          <a href="/subscriptions.html" style="color: #ff3b5c; text-decoration: none; font-weight: bold;">اشترك الآن</a>
          للوصول إلى جميع الأصوات المتاحة.
        `;

        // إدراج الرسالة بعد عنوان القسم
        const section = document.querySelector('.section h3');
        if (section && section.parentNode) {
          section.parentNode.insertBefore(limitMessage, section.nextSibling);
        }
      }
      limitMessage.style.display = 'block';
    }

    // إخفاء رسالة تحديد الأصوات
    function hideVoiceLimitMessage() {
      const limitMessage = document.getElementById('voice-limit-message');
      if (limitMessage) {
        limitMessage.style.display = 'none';
      }
    }

    // مراقبة حالة المصادقة
    window.addEventListener('authStateChanged', (event) => {
      currentUser = event.detail.user;
      if (currentUser) {
        console.log('User authenticated:', currentUser.email);
        loadUserSubscription();
      } else {
        console.log('User not authenticated');
        hasActiveSubscription = false;
        updateVoiceSelectionUI();

        // إعادة تحديث قائمة الأصوات عند عدم المصادقة
        if (typeof updateModelsList === 'function') {
          updateModelsList();
        }
      }
    });

    // استدعاء إعدادات القراءة الصوتية من الخادم
    socket.emit('getTtsSettings');

    // استقبال إعدادات القراءة الصوتية
    socket.on('ttsSettings', (settings) => {
      // منع التحديث إذا كان المستخدم يتفاعل مع العناصر
      if (isUserInteracting) {
        return;
      }

      // تحديث واجهة المستخدم بناءً على الإعدادات المستلمة
      if (settings) {
        ttsToggle.checked = settings.enabled;
        updateStatusDisplay(settings.enabled);

        // إنشاء قائمة النماذج الصوتية
        // مسح القائمة الحالية
        voiceSelect.innerHTML = '';

        // تخزين النماذج المتاحة للاستخدام لاحقاً
        let availableModels = [];

        // استخدام النماذج من ملف tts-voices.js
        if (typeof ttsModels !== 'undefined' && Array.isArray(ttsModels)) {
          availableModels = ttsModels;
        } else if (settings.models && Array.isArray(settings.models)) {
          // استخدام النماذج من الإعدادات كاحتياطي
          availableModels = settings.models;
        }

        // دالة لتحديث قائمة النماذج بناءً على البحث
        function updateModelsList(searchText = '') {
          // مسح القائمة الحالية
          voiceSelect.innerHTML = '';

          let modelsToShow = availableModels;

          // للمستخدمين غير المشتركين، إظهار صوت "شاكر" فقط
          if (!hasActiveSubscription) {
            modelsToShow = availableModels.filter(model =>
              model.id === 'ar-EG-ShakirNeural'
            );
          }

          // تصفية النماذج بناءً على البحث
          const filteredModels = modelsToShow.filter(model => {
            // تصفية بناءً على البحث
            return searchText === '' ||
                   model.name.toLowerCase().includes(searchText.toLowerCase()) ||
                   model.id.toLowerCase().includes(searchText.toLowerCase());
          });

          // إضافة النماذج المصفاة إلى القائمة
          filteredModels.forEach(model => {
            const option = document.createElement('option');
            option.value = model.id;
            option.textContent = model.name;
            voiceSelect.appendChild(option);
          });

          // للمستخدمين غير المشتركين، إضافة خيارات معطلة للأصوات الأخرى
          if (!hasActiveSubscription) {
            const lockedModels = availableModels.filter(model =>
              model.id !== 'ar-EG-ShakirNeural'
            );

            if (lockedModels.length > 0) {
              // إضافة فاصل
              const separator = document.createElement('option');
              separator.disabled = true;
              separator.textContent = '── أصوات مقفلة (اشتراك مطلوب) ──';
              voiceSelect.appendChild(separator);

              // إضافة الأصوات المقفلة
              lockedModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = `🔒 ${model.name}`;
                option.disabled = true;
                option.style.color = '#6c757d';
                voiceSelect.appendChild(option);
              });
            }
          }

          // تحديد النموذج الحالي إذا كان موجوداً في القائمة المصفاة
          if (settings.voice) {
            const currentVoiceOption = Array.from(voiceSelect.options).find(option => option.value === settings.voice);
            if (currentVoiceOption && !currentVoiceOption.disabled) {
              if (hasActiveSubscription && userSelectedVoice && preventServerOverride) {
                voiceSelect.value = userSelectedVoice;
              } else if (hasActiveSubscription && settings.voice === 'ar-EG-ShakirNeural') {
                // السماح بشاكر للمشتركين أيضاً
                voiceSelect.value = settings.voice;
              } else {
                voiceSelect.value = settings.voice;
              }
            } else if (voiceSelect.options.length > 0) {
              // للمستخدمين غير المشتركين، فرض استخدام صوت شاكر
              if (!hasActiveSubscription) {
                const shakirOption = Array.from(voiceSelect.options).find(option => option.value === 'ar-EG-ShakirNeural');
                if (shakirOption) {
                  voiceSelect.value = 'ar-EG-ShakirNeural';
                  // إرسال التحديث للخادم
                  socket.emit('updateTtsSetting', { voice: 'ar-EG-ShakirNeural' });
                }
              } else {
                voiceSelect.selectedIndex = 0;
              }
            }
          }
        }

        // تحديث القائمة للمرة الأولى
        updateModelsList();

        // إضافة مستمع الأحداث للبحث
        const voiceSearch = document.getElementById('voice-search');

        voiceSearch.addEventListener('input', () => {
          updateModelsList(voiceSearch.value);
        });

        // تحديد النموذج الحالي
        if (settings.voice) {
          if (hasActiveSubscription && userSelectedVoice && preventServerOverride) {
            voiceSelect.value = userSelectedVoice;
          } else if (hasActiveSubscription && settings.voice === 'ar-EG-ShakirNeural') {
            // السماح بشاكر للمشتركين أيضاً
            voiceSelect.value = settings.voice;
          } else {
            voiceSelect.value = settings.voice;
          }
        }

        // تحديث واجهة المستخدم حسب حالة الاشتراك
        updateVoiceSelectionUI();

        // تحديث باقي الإعدادات (فقط إذا لم يكن المستخدم يتفاعل)
        if (!isUserInteracting) {
          voiceSpeed.value = settings.speed;
          speedValue.textContent = settings.speed;

          // تحديث مستوى الصوت إذا كان موجوداً
          if (typeof settings.volume !== 'undefined') {
            voiceVolume.value = settings.volume;
            volumeValue.textContent = settings.volume;
          }
        }

        // تحديث إعدادات التصفية (فقط إذا لم يكن المستخدم يتفاعل)
        if (!isUserInteracting) {
          minLength.value = settings.minLength;
          maxLength.value = settings.maxLength;
          readRate.value = settings.readRate;
          blockedWords.value = Array.isArray(settings.blockedWords) ? settings.blockedWords.join('، ') : '';
        }
      }
    });

    // تحديث حالة العرض
    function updateStatusDisplay(enabled) {
      if (enabled) {
        ttsStatus.textContent = 'قراءة التعليقات مفعلة';
        ttsStatus.className = 'status enabled';
      } else {
        ttsStatus.textContent = 'قراءة التعليقات غير مفعلة';
        ttsStatus.className = 'status disabled';
      }
    }

    // معالجة تبديل حالة التفعيل
    ttsToggle.addEventListener('change', () => {
      startUserInteraction(); // منع إعادة الكتابة من الخادم

      const enabled = ttsToggle.checked;
      updateStatusDisplay(enabled);

      // إرسال حالة التفعيل للخادم
      socket.emit('updateTtsSetting', { enabled });
    });

    // متغيرات لحفظ اختيار المستخدم
    let userSelectedVoice = localStorage.getItem('selectedVoice');
    let preventServerOverride = false;
    let isUserInteracting = false; // متغير لتتبع تفاعل المستخدم
    let userInteractionTimeout = null; // مؤقت لإنهاء حالة التفاعل

    // دالة لبدء حالة تفاعل المستخدم
    function startUserInteraction() {
      isUserInteracting = true;

      if (userInteractionTimeout) {
        clearTimeout(userInteractionTimeout);
      }

      userInteractionTimeout = setTimeout(() => {
        isUserInteracting = false;
      }, 3000);
    }

    // معالجة اختيار النموذج الصوتي
    voiceSelect.addEventListener('change', () => {
      const voice = voiceSelect.value;

      // التحقق من إمكانية تغيير الصوت للمستخدمين غير المشتركين
      if (!hasActiveSubscription && voice !== 'ar-EG-ShakirNeural') {
        voiceSelect.value = 'ar-EG-ShakirNeural';
        showVoiceChangeAlert();
        return;
      }

      // حفظ اختيار المستخدم
      if (hasActiveSubscription) {
        userSelectedVoice = voice;
        preventServerOverride = true;
        localStorage.setItem('selectedVoice', voice);
      } else if (voice === 'ar-EG-ShakirNeural') {
        // للمستخدمين غير المشتركين، شاكر هو الخيار الوحيد
        userSelectedVoice = null;
        preventServerOverride = false;
        localStorage.removeItem('selectedVoice');
      }

      socket.emit('updateTtsSetting', { voice });
    });

    // إظهار رسالة تنبيه عند محاولة تغيير الصوت
    function showVoiceChangeAlert() {
      const alertMessage = `
        <div style="text-align: center; padding: 20px;">
          <div style="font-size: 48px; margin-bottom: 20px;">🔒</div>
          <h3 style="color: #495057; margin-bottom: 15px;">الأصوات محدودة</h3>
          <p style="color: #6c757d; margin-bottom: 20px;">
            النسخة المجانية تسمح باستخدام صوت "شاكر" فقط.<br>
            للوصول إلى جميع الأصوات المتاحة، يرجى الاشتراك.
          </p>
          <p style="color: #28a745; font-weight: bold; margin-bottom: 25px;">
            الاشتراك الشامل - $10/شهر
          </p>
          <a href="/subscriptions.html" style="
            display: inline-block;
            background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(255, 59, 92, 0.3);
          ">اشترك الآن</a>
        </div>
      `;

      // إنشاء نافذة منبثقة
      const alertDiv = document.createElement('div');
      alertDiv.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 10000;
        display: flex;
        justify-content: center;
        align-items: center;
      `;

      const contentDiv = document.createElement('div');
      contentDiv.style.cssText = `
        background: white;
        border-radius: 15px;
        max-width: 500px;
        margin: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      `;
      contentDiv.innerHTML = alertMessage;

      alertDiv.appendChild(contentDiv);
      document.body.appendChild(alertDiv);

      // إغلاق النافذة عند النقر خارجها
      alertDiv.addEventListener('click', (e) => {
        if (e.target === alertDiv) {
          document.body.removeChild(alertDiv);
        }
      });

      // إغلاق النافذة تلقائياً بعد 5 ثوانٍ
      setTimeout(() => {
        if (document.body.contains(alertDiv)) {
          document.body.removeChild(alertDiv);
        }
      }, 5000);
    }

    // معالجة تغيير سرعة القراءة
    voiceSpeed.addEventListener('input', () => {
      startUserInteraction(); // منع إعادة الكتابة من الخادم

      const speed = parseFloat(voiceSpeed.value);
      speedValue.textContent = speed.toFixed(1);

      // إرسال سرعة القراءة للخادم
      socket.emit('updateTtsSetting', { speed });
    });

    // معالجة تغيير مستوى الصوت
    voiceVolume.addEventListener('input', () => {
      startUserInteraction(); // منع إعادة الكتابة من الخادم

      const volume = parseInt(voiceVolume.value);
      volumeValue.textContent = volume;

      // تحديث العرض فقط، لا نرسل للخادم مع كل تغيير
    });

    // إرسال مستوى الصوت للخادم عند الانتهاء من السحب
    voiceVolume.addEventListener('change', () => {
      startUserInteraction(); // منع إعادة الكتابة من الخادم

      const volume = parseInt(voiceVolume.value);
      socket.emit('updateTtsSetting', { volume });
    });

    // معالجة اختبار الصوت
    testVoiceBtn.addEventListener('click', () => {
      // الحصول على النموذج المحدد والنص المخصص
      const selectedModel = voiceSelect.value;
      const speed = parseFloat(voiceSpeed.value);
      const volume = parseInt(voiceVolume.value);
      const customText = testText.value || 'شكراً على هذا البث الرائع!';

      // تغيير نص الزر للإشارة إلى أن الاختبار قيد التنفيذ
      testVoiceBtn.textContent = 'جاري الاختبار...';
      testVoiceBtn.disabled = true;

      // إرسال طلب اختبار الصوت للخادم
      console.log(`اختبار الصوت: النموذج=${selectedModel}, السرعة=${speed}, مستوى الصوت=${volume}, النص="${customText}"`);
      socket.emit('testTtsVoice', {
        model: selectedModel,
        speed: speed,
        volume: volume,
        text: customText
      });
    });

    // معالجات إعدادات التصفية
    minLength.addEventListener('input', () => {
      startUserInteraction(); // منع إعادة الكتابة من الخادم
    });

    maxLength.addEventListener('input', () => {
      startUserInteraction(); // منع إعادة الكتابة من الخادم
    });

    readRate.addEventListener('input', () => {
      startUserInteraction(); // منع إعادة الكتابة من الخادم
    });

    blockedWords.addEventListener('input', () => {
      startUserInteraction(); // منع إعادة الكتابة من الخادم
    });

    // معالجة حفظ الإعدادات
    saveSettingsBtn.addEventListener('click', () => {
      startUserInteraction(); // منع إعادة الكتابة من الخادم بعد الحفظ

      // جمع جميع الإعدادات
      const settings = {
        enabled: ttsToggle.checked,
        voice: voiceSelect.value,
        speed: parseFloat(voiceSpeed.value),
        volume: parseInt(voiceVolume.value),
        minLength: parseInt(minLength.value),
        maxLength: parseInt(maxLength.value),
        readRate: parseInt(readRate.value),
        blockedWords: blockedWords.value.split('،').map(word => word.trim()).filter(word => word),
        useSystemTTS: false // Always set to false
      };

      // إرسال الإعدادات للخادم
      socket.emit('saveTtsSettings', settings);

      // تغيير نص الزر للإشارة إلى أن الحفظ قد تم
      saveSettingsBtn.textContent = 'تم الحفظ';

      // إعادة تعيين نص الزر بعد ثانيتين
      setTimeout(() => {
        saveSettingsBtn.textContent = 'حفظ الإعدادات';
      }, 2000);
    });

    // استقبال رسائل النجاح
    socket.on('ttsSettingsSaved', () => {
      alert('تم حفظ إعدادات قراءة التعليقات بنجاح');
    });

    // استقبال رسائل الخطأ
    socket.on('errorMessage', (data) => {
      alert(`خطأ: ${data.message}`);
    });

    // استقبال حالة تحميل النموذج
    socket.on('ttsStatus', (data) => {
      if (data.message) {
        modelLoadingStatus.textContent = data.message;
        modelLoadingStatus.style.display = 'block';
        modelLoadingStatus.className = 'status';

        // إضافة فئة التحميل
        if (data.message.includes('جاري تحميل')) {
          modelLoadingStatus.classList.add('loading');
          testVoiceBtn.disabled = true;
        } else {
          modelLoadingStatus.classList.remove('loading');
          testVoiceBtn.disabled = false;
        }
      } else {
        modelLoadingStatus.style.display = 'none';
      }
    });

    // استقبال نتيجة اختبار الصوت
    socket.on('testTtsVoiceResult', (data) => {
      if (data.success) {
        testVoiceBtn.textContent = 'تم الاختبار بنجاح';
        testVoiceBtn.classList.add('success');
      } else {
        testVoiceBtn.textContent = 'فشل الاختبار';
        testVoiceBtn.classList.add('error');
        // إظهار رسالة الخطأ
        if (data.error) {
          alert(`خطأ في اختبار الصوت: ${data.error || 'حدث خطأ غير معروف'}`);
        }
      }

      // إعادة تعيين زر الاختبار بعد 3 ثوانٍ
      setTimeout(() => {
        testVoiceBtn.textContent = 'اختبار الصوت';
        testVoiceBtn.disabled = false;
        testVoiceBtn.classList.remove('success', 'error');
      }, 3000);
    });
  </script>
  <script src="/js/global-text-direction.js"></script>
  <script src="/dark-mode.js"></script>
  <script src="/js/translation.js"></script>
  <script src="/js/smooth-nav.js"></script>
  <script src="/js/background-manager.js"></script>
</body>
</html>