@echo off

:: التحقق من صلاحيات المسؤول وإعادة تشغيل الملف بصلاحيات المسؤول إذا لزم الأمر
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"
if '%errorlevel%' NEQ '0' (
    echo طلب صلاحيات المسؤول...
    goto UACPrompt
) else ( goto gotAdmin )

:UACPrompt
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "", "", "runas", 1 >> "%temp%\getadmin.vbs"
    "%temp%\getadmin.vbs"
    exit /B

:gotAdmin
    if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )
    pushd "%CD%"
    CD /D "%~dp0"

echo TikTok Live Overlay System
echo =========================
echo.

:: تحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
  echo [خطأ] لم يتم العثور على Node.js
  echo الرجاء تثبيت Node.js من https://nodejs.org
  echo.
  pause
  exit /b 1
)

:: تحقق من تثبيت المكتبات
if not exist "node_modules" (
  echo تثبيت المكتبات المطلوبة...
  npm install
)

:: تشغيل البرنامج
echo جاري تشغيل البرنامج...
echo سيتم فتح المتصفح تلقائياً بعد بدء التشغيل.
echo.
echo اضغط Ctrl+C لإيقاف الخادم عند الانتهاء.
echo.

:: انتظر قليلاً ثم افتح المتصفح
start "" http://localhost:3000

:: تشغيل السيرفر
npm start