const fs = require('fs');
const crypto = require('crypto');
const path = require('path');

console.log('🔐 بدء الحماية البسيطة...');

// قراءة index.js الأصلي
const originalCode = fs.readFileSync('index.js', 'utf8');
console.log('✅ تم قراءة index.js');

// إنشاء مفتاح تشفير
const key = crypto.randomBytes(32);
const iv = crypto.randomBytes(16);

// تشفير الكود
const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
let encrypted = cipher.update(originalCode, 'utf8', 'hex');
encrypted += cipher.final('hex');

console.log('✅ تم تشفير الكود');

// إنشاء البيانات للرفع على Firebase
const firebaseData = {
  encrypted: encrypted,
  iv: iv.toString('hex'),
  key: key.toString('hex'),
  timestamp: Date.now()
};

// حفظ الملف المشفر للرفع على Firebase
fs.writeFileSync('encrypted-index.json', JSON.stringify(firebaseData), 'utf8');
console.log('✅ تم إنشاء ملف للرفع على Firebase: encrypted-index.json');

// إنشاء index.js الجديد (المحمل)
const loaderCode = `// StreamTok Secure Loader
const https = require('https');
const crypto = require('crypto');
const vm = require('vm');

async function loadFromFirebase() {
  return new Promise((resolve, reject) => {
    const url = 'https://streamtok-c6830.web.app/encrypted-index.json';
    
    https.get(url, (res) => {
      if (res.statusCode !== 200) {
        reject(new Error(\`Failed to load: \${res.statusCode}\`));
        return;
      }
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          
          // فك التشفير
          const key = Buffer.from(parsed.key, 'hex');
          const iv = Buffer.from(parsed.iv, 'hex');
          const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
          
          let decrypted = decipher.update(parsed.encrypted, 'hex', 'utf8');
          decrypted += decipher.final('utf8');
          
          // تنفيذ في الذاكرة
          eval(decrypted);
          
          resolve();
        } catch (error) {
          reject(error);
        }
      });
    }).on('error', reject);
  });
}

// بدء التحميل
loadFromFirebase().catch(error => {
  console.error('Failed to load StreamTok:', error.message);
  process.exit(1);
});
`;

// حفظ نسخة احتياطية
fs.writeFileSync('index-backup.js', originalCode, 'utf8');

// استبدال index.js
fs.writeFileSync('index.js', loaderCode, 'utf8');

console.log('✅ تم إنشاء المحمل الجديد');
console.log('📁 الملفات:');
console.log('  - index.js (المحمل الجديد)');
console.log('  - index-backup.js (النسخة الأصلية)');
console.log('  - encrypted-index.json (للرفع على Firebase)');
console.log('');
console.log('🔥 الخطوة التالية: ارفع encrypted-index.json على Firebase Hosting');
console.log('🧪 ثم اختبر: npm start');
