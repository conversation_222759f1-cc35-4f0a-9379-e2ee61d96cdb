# إعداد Firebase للمشروع

## 🔧 معلومات المشروع:
- **Project ID:** streamtok-c6830
- **Auth Domain:** streamtok-c6830.firebaseapp.com

## ✅ تم الإنجاز:

### 1. إعداد Firebase في المشروع
- ✅ إنشاء ملف تكوين Firebase (`public/js/firebase-config.js`)
- ✅ تهيئة Firebase Authentication و Firestore
- ✅ إنشاء نظام حماية الصفحات (`public/js/auth-guard.js`)

### 2. نظام المصادقة
- ✅ صفحة تسجيل الدخول/إنشاء حساب (`public/auth.html`)
- ✅ تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
- ✅ إنشاء حسابات جديدة
- ✅ تسجيل الخروج
- ✅ حماية الصفحات (توجيه غير المسجلين لصفحة تسجيل الدخول)

### 3. نظام الاشتراكات المحدث
- ✅ ربط الاشتراكات بـ Firebase Firestore
- ✅ إنشاء اشتراكات جديدة
- ✅ عرض حالة الاشتراك الحالي
- ✅ التحقق من صلاحية الاشتراك
- ✅ تقييد الوصول للميزات المتقدمة

## 🔧 هيكل قاعدة البيانات في Firestore:

### مجموعة `users`:
```javascript
{
  userId: "user_id_here",
  name: "اسم المستخدم",
  email: "<EMAIL>",
  subscriptionStatus: "none|basic|premium",
  createdAt: timestamp,
  updatedAt: timestamp
}
```

### مجموعة `subscriptions`:
```javascript
{
  userId: "user_id_here",
  plan: "basic|premium",
  status: "active|expired|cancelled",
  startDate: timestamp,
  endDate: timestamp,
  features: ["feature1", "feature2", ...],
  price: "$19.99",
  createdAt: timestamp,
  updatedAt: timestamp
}
```

## 🚀 الميزات الجديدة:

### 1. نظام المصادقة الكامل:
- تسجيل دخول آمن
- إنشاء حسابات جديدة
- حفظ بيانات المستخدم في Firestore
- حماية الصفحات تلقائياً

### 2. نظام الاشتراكات المتطور:
- **الباقة الأساسية ($9.99/شهر):**
  - اتصال بـ TikTok Live
  - عرض التعليقات والهدايا
  - تنبيهات أساسية

- **الباقة المميزة ($19.99/شهر):**
  - جميع ميزات الباقة الأساسية
  - دعم الألعاب والتفاعلات المتقدمة
  - تخصيص كامل للواجهة
  - دعم فني على مدار الساعة

### 3. حماية الميزات:
- الألعاب تتطلب اشتراك مميز
- التحقق التلقائي من صلاحية الاشتراك
- توجيه المستخدمين لصفحة الاشتراك عند الحاجة

## 📋 قواعد Firestore المطلوبة:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد مجموعة المستخدمين
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد مجموعة الاشتراكات
    match /subscriptions/{subscriptionId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
  }
}
```

## 🔐 إعداد Firebase Authentication:

1. في Firebase Console، اذهب إلى Authentication
2. فعّل "Email/Password" في تبويب Sign-in method
3. (اختياري) فعّل طرق تسجيل دخول أخرى مثل Google

## 💾 إعداد Cloud Firestore:

1. في Firebase Console، اذهب إلى Firestore Database
2. أنشئ قاعدة بيانات في Production mode
3. اختر المنطقة الأقرب لك
4. أضف القواعد المذكورة أعلاه

## 🧪 اختبار النظام:

### 1. اختبار المصادقة:
- اذهب إلى `/auth.html`
- أنشئ حساب جديد
- سجل دخول وخروج
- تأكد من التوجيه التلقائي

### 2. اختبار الاشتراكات:
- سجل دخول
- اذهب إلى `/subscriptions.html`
- جرب شراء اشتراك
- تحقق من تحديث البيانات في Firestore

### 3. اختبار حماية الصفحات:
- حاول الوصول للألعاب بدون اشتراك
- تأكد من التوجيه لصفحة الاشتراكات

## 🔄 الخطوات التالية (اختيارية):

### 1. تكامل بوابة الدفع:
- إضافة Stripe أو PayPal
- معالجة المدفوعات الحقيقية
- تجديد تلقائي للاشتراكات

### 2. ميزات متقدمة:
- إشعارات انتهاء الاشتراك
- كوبونات خصم
- باقات اشتراك متعددة المدة
- نظام إحالة

### 3. لوحة إدارة:
- إدارة المستخدمين
- إحصائيات الاشتراكات
- تقارير الإيرادات

## 📞 الدعم:

إذا واجهت أي مشاكل:
1. تحقق من console المتصفح للأخطاء
2. تأكد من إعداد Firebase بشكل صحيح
3. تحقق من قواعد Firestore
4. تأكد من تفعيل Authentication
