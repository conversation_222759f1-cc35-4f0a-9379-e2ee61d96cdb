/**
 * إعدادات لعبة صراع الأعلام
 * تطوير: Augment Agent
 */

// إعدادات افتراضية
const defaultSettings = {
  particleEffects: true,
  autoRotate: true,
  rotationSpeed: 0.01,
  maxCubeSize: 5,
  minCubeSize: 0.5,
  growthRate: 0.002, // معدل النمو: 2000 وردة للفوز
  soundEnabled: true,
  soundVolume: 50,
  demoMode: false,
  demoSpeed: 2,
  countryCount: 12,
  // إعدادات الكاميرا
  cameraControls: false,
  cameraX: 0,
  cameraY: 8,
  cameraZ: 15,
  cameraLookAtX: 0,
  cameraLookAtY: 0,
  cameraLookAtZ: 0
};

// تحميل الإعدادات المحفوظة
let currentSettings = { ...defaultSettings };

// بيانات الدول العربية الافتراضية
const defaultCountries = {
  'EG': { name: 'مصر', flag: '🇪🇬', color: '#ff0000' },
  'SA': { name: 'السعودية', flag: '🇸🇦', color: '#00ff00' },
  'DZ': { name: 'الجزائر', flag: '🇩🇿', color: '#0080ff' },
  'MA': { name: 'المغرب', flag: '🇲🇦', color: '#ff8000' },
  'IQ': { name: 'العراق', flag: '🇮🇶', color: '#8000ff' },
  'AE': { name: 'الإمارات', flag: '🇦🇪', color: '#ff0080' },
  'TN': { name: 'تونس', flag: '🇹🇳', color: '#00ff80' },
  'SY': { name: 'سوريا', flag: '🇸🇾', color: '#ffff00' },
  'LB': { name: 'لبنان', flag: '🇱🇧', color: '#ff4080' },
  'QA': { name: 'قطر', flag: '🇶🇦', color: '#80ff40' },
  'KW': { name: 'الكويت', flag: '🇰🇼', color: '#4080ff' },
  'BH': { name: 'البحرين', flag: '🇧🇭', color: '#ff8040' }
};

// إعدادات الدول المخصصة
let customCountries = {};

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', () => {
  loadSettings();
  loadCustomCountries();
  loadFloorPreview();
  initializeControls();
  renderCustomCountries();
});

/**
 * تحميل معاينة الأرض الحالية
 */
function loadFloorPreview() {
  // التحقق من وجود صورة في IndexedDB أولاً
  if (localStorage.getItem('flagClashFloorSaved') === 'true') {
    loadFloorImageFromIndexedDB(function(imageUrl) {
      if (imageUrl) {
        updateFloorPreview(imageUrl);
      } else {
        // إذا فشل تحميل IndexedDB، جرب localStorage
        const customFloorImage = localStorage.getItem('flagClashCustomFloor');
        updateFloorPreview(customFloorImage);
      }
    });
  } else {
    // تحميل من localStorage
    const customFloorImage = localStorage.getItem('flagClashCustomFloor');
    updateFloorPreview(customFloorImage);
  }
}

/**
 * تنظيف التخزين لإفساح المجال لصورة الأرض (تنظيف قوي)
 */
function cleanupStorageForFloor() {
  try {
    // إزالة الصورة القديمة أولاً
    localStorage.removeItem('flagClashCustomFloor');

    // إزالة جميع الصور المخصصة للأعلام لتوفير مساحة أكبر
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.includes('temp') ||
        key.includes('cache') ||
        key.includes('old') ||
        key.includes('flagClashCustomFlag') || // إزالة صور الأعلام المخصصة
        key.includes('backup') ||
        key.includes('preview')
      )) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });

    // محاولة تنظيف إضافي إذا لزم الأمر
    if (keysToRemove.length > 0) {
      console.log(`🧹 تم تنظيف ${keysToRemove.length} عنصر من التخزين لإفساح المجال للصورة الجديدة`);
    }

    // فرض تنظيف الذاكرة
    if (window.gc) {
      window.gc();
    }

  } catch (error) {
    console.warn('تحذير: فشل في تنظيف التخزين:', error);
  }
}

/**
 * فحص مساحة التخزين المتاحة (محسن)
 */
function checkStorageSpace(dataSize) {
  try {
    // فحص بسيط بدون استخدام بيانات كبيرة
    const testKey = 'storageTest';
    const testData = 'test'; // بيانات صغيرة للاختبار

    localStorage.setItem(testKey, testData);
    localStorage.removeItem(testKey);

    // إذا نجح الاختبار البسيط، نفترض أن المساحة كافية
    return true;
  } catch (error) {
    console.warn('مساحة التخزين غير كافية:', error);
    return false;
  }
}

/**
 * تحميل الإعدادات من التخزين المحلي
 */
function loadSettings() {
  try {
    const saved = localStorage.getItem('flagClashSettings');
    if (saved) {
      currentSettings = { ...defaultSettings, ...JSON.parse(saved) };
    }
    updateUI();
  } catch (error) {
    console.error('خطأ في تحميل الإعدادات:', error);
    currentSettings = { ...defaultSettings };
    updateUI();
  }
}

/**
 * تحميل إعدادات الدول المخصصة
 */
function loadCustomCountries() {
  try {
    const saved = localStorage.getItem('flagClashCustomCountries');
    if (saved) {
      customCountries = JSON.parse(saved);
    }
  } catch (error) {
    console.error('خطأ في تحميل الدول المخصصة:', error);
    customCountries = {};
  }
}

/**
 * حفظ إعدادات الدول المخصصة
 */
function saveCustomCountries() {
  try {
    localStorage.setItem('flagClashCustomCountries', JSON.stringify(customCountries));
    console.log('تم حفظ الدول المخصصة');
  } catch (error) {
    console.error('خطأ في حفظ الدول المخصصة:', error);
  }
}

/**
 * تهيئة عناصر التحكم
 */
function initializeControls() {
  // مفاتيح التبديل
  document.querySelectorAll('.toggle-switch').forEach(toggle => {
    toggle.addEventListener('click', () => {
      toggle.classList.toggle('active');
      const setting = toggle.dataset.setting;
      currentSettings[setting] = toggle.classList.contains('active');
      console.log(`تم تغيير ${setting} إلى ${currentSettings[setting]}`);

      // تطبيق التغيير فوراً
      applySettingChange(setting);
    });
  });

  // شرائح التمرير
  document.querySelectorAll('.slider').forEach(slider => {
    slider.addEventListener('input', (e) => {
      const setting = e.target.dataset.setting;
      const value = parseFloat(e.target.value);
      currentSettings[setting] = value;
      
      // تحديث عرض القيمة
      const valueDisplay = document.getElementById(setting + 'Value');
      if (valueDisplay) {
        valueDisplay.textContent = value;
      }
      
      console.log(`تم تغيير ${setting} إلى ${value}`);

      // تطبيق التغيير فوراً
      applySettingChange(setting);
    });
  });

  // قوائم الاختيار
  document.querySelectorAll('.select-box').forEach(select => {
    select.addEventListener('change', (e) => {
      const setting = e.target.dataset.setting;
      const value = e.target.value;
      currentSettings[setting] = value;
      console.log(`تم تغيير ${setting} إلى ${value}`);

      // تطبيق التغيير فوراً
      applySettingChange(setting);
    });
  });
}

/**
 * تحديث واجهة المستخدم
 */
function updateUI() {
  // تحديث مفاتيح التبديل
  document.querySelectorAll('.toggle-switch').forEach(toggle => {
    const setting = toggle.dataset.setting;
    if (currentSettings[setting]) {
      toggle.classList.add('active');
    } else {
      toggle.classList.remove('active');
    }
  });

  // تحديث شرائح التمرير
  document.querySelectorAll('.slider').forEach(slider => {
    const setting = slider.dataset.setting;
    slider.value = currentSettings[setting];
    
    // تحديث عرض القيمة
    const valueDisplay = document.getElementById(setting + 'Value');
    if (valueDisplay) {
      valueDisplay.textContent = currentSettings[setting];
    }
  });

  // تحديث قوائم الاختيار
  document.querySelectorAll('.select-box').forEach(select => {
    const setting = select.dataset.setting;
    select.value = currentSettings[setting];
  });
}

/**
 * حفظ الإعدادات
 */
function saveSettings() {
  try {
    localStorage.setItem('flagClashSettings', JSON.stringify(currentSettings));
    showAlert('✅ تم حفظ الإعدادات بنجاح!');
    
    // إرسال الإعدادات للعبة إذا كانت مفتوحة
    if (window.opener && window.opener.game) {
      // استخدام الوظيفة الجديدة لإعادة تحميل الإعدادات
      if (window.opener.game.reloadSettings) {
        window.opener.game.reloadSettings();
      } else {
        // الطريقة القديمة كاحتياط
        window.opener.game.gameSettings = { ...currentSettings };

        if (window.opener.game.rearrangeCubes) {
          window.opener.game.rearrangeCubes();
        }

        if (window.opener.game.updateCameraSettings) {
          window.opener.game.updateCameraSettings();
        }
      }

      console.log('تم إرسال الإعدادات للعبة وتطبيق التغييرات');
    }
    
    console.log('تم حفظ الإعدادات:', currentSettings);
  } catch (error) {
    console.error('خطأ في حفظ الإعدادات:', error);
    showAlert('❌ حدث خطأ في حفظ الإعدادات');
  }
}

/**
 * إعادة تعيين الإعدادات
 */
function resetSettings() {
  if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
    currentSettings = { ...defaultSettings };
    updateUI();
    showAlert('🔄 تم إعادة تعيين الإعدادات!');
    console.log('تم إعادة تعيين الإعدادات إلى القيم الافتراضية');
  }
}

/**
 * اختبار الإعدادات
 */
function testSettings() {
  saveSettings();
  showAlert('🧪 تم تطبيق الإعدادات للاختبار!');

  // فتح اللعبة في وضع التجربة
  const gameUrl = '/games/flag-clash.html?demo=true';
  window.open(gameUrl, '_blank');
  console.log('تم فتح اللعبة في وضع التجربة');
}

/**
 * تحديث صورة الأرض (بجودة عالية جداً)
 */
function updateFloorImage(input) {
  const file = input.files[0];
  if (!file) return;

  // التحقق من نوع الملف
  if (!file.type.startsWith('image/')) {
    showAlert('❌ يرجى اختيار ملف صورة صحيح');
    return;
  }

  // التحقق من حجم الملف (أقل من 100MB)
  if (file.size > 100 * 1024 * 1024) {
    showAlert('❌ حجم الصورة كبير جداً (أقل من 100MB)');
    return;
  }

  // عرض رسالة تحميل
  showAlert('⏳ جاري معالجة الصورة بأعلى جودة ممكنة... يرجى الانتظار');

  // معالجة الصورة بجودة عالية وحفظها في IndexedDB
  processAndSaveHighQualityFloorImage(file);
}

/**
 * معالجة وحفظ صورة الأرض بجودة عالية جداً
 */
function processAndSaveHighQualityFloorImage(file) {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const img = new Image();

  img.onload = function() {
    // استخدام أقصى جودة ممكنة - بدون ضغط تقريباً
    let maxSize = 2048; // دقة عالية جداً
    let quality = 0.98; // جودة استثنائية

    // حتى للصور الكبيرة جداً، نحافظ على جودة عالية
    if (file.size > 50 * 1024 * 1024) { // أكبر من 50MB
      maxSize = 1536;
      quality = 0.95;
    } else if (file.size > 30 * 1024 * 1024) { // أكبر من 30MB
      maxSize = 1792;
      quality = 0.96;
    }

    let { width, height } = img;

    // حساب الأبعاد الجديدة مع الحفاظ على النسبة
    if (width > height) {
      if (width > maxSize) {
        height = (height * maxSize) / width;
        width = maxSize;
      }
    } else {
      if (height > maxSize) {
        width = (width * maxSize) / height;
        height = maxSize;
      }
    }

    canvas.width = width;
    canvas.height = height;

    // رسم الصورة بأعلى جودة
    ctx.drawImage(img, 0, 0, width, height);

    // تحويل إلى blob بجودة عالية
    canvas.toBlob(function(blob) {
      // حفظ في IndexedDB
      saveFloorImageToIndexedDB(blob, file.name);
    }, 'image/jpeg', quality);
  };

  img.onerror = function() {
    showAlert('❌ فشل في تحميل الصورة');
  };

  // تحميل الصورة
  const reader = new FileReader();
  reader.onload = function(e) {
    img.src = e.target.result;
  };
  reader.readAsDataURL(file);
}

/**
 * حفظ صورة الأرض في IndexedDB
 */
function saveFloorImageToIndexedDB(blob, fileName) {
  const request = indexedDB.open('FlagClashDB', 1);

  request.onerror = function() {
    showAlert('❌ فشل في فتح قاعدة البيانات');
  };

  request.onupgradeneeded = function(event) {
    const db = event.target.result;
    if (!db.objectStoreNames.contains('floorImages')) {
      db.createObjectStore('floorImages', { keyPath: 'id' });
    }
  };

  request.onsuccess = function(event) {
    const db = event.target.result;
    const transaction = db.transaction(['floorImages'], 'readwrite');
    const store = transaction.objectStore('floorImages');

    // حفظ الصورة
    const floorData = {
      id: 'currentFloor',
      blob: blob,
      fileName: fileName,
      timestamp: Date.now()
    };

    const saveRequest = store.put(floorData);

    saveRequest.onsuccess = function() {
      // تحويل blob إلى URL للمعاينة
      const imageUrl = URL.createObjectURL(blob);
      updateFloorPreview(imageUrl);

      // حفظ مرجع في localStorage
      localStorage.setItem('flagClashFloorSaved', 'true');

      const sizeMB = (blob.size / (1024 * 1024)).toFixed(2);
      showAlert(`✅ تم حفظ صورة الأرض بأعلى جودة! (${sizeMB}MB)`);
      console.log(`🎨 تم حفظ صورة الأرض بحجم ${sizeMB}MB في IndexedDB`);
    };

    saveRequest.onerror = function() {
      showAlert('❌ فشل في حفظ الصورة');
    };
  };
}

/**
 * تحميل صورة الأرض من IndexedDB
 */
function loadFloorImageFromIndexedDB(callback) {
  const request = indexedDB.open('FlagClashDB', 1);

  request.onsuccess = function(event) {
    const db = event.target.result;
    if (!db.objectStoreNames.contains('floorImages')) {
      callback(null);
      return;
    }

    const transaction = db.transaction(['floorImages'], 'readonly');
    const store = transaction.objectStore('floorImages');
    const getRequest = store.get('currentFloor');

    getRequest.onsuccess = function() {
      if (getRequest.result) {
        const imageUrl = URL.createObjectURL(getRequest.result.blob);
        callback(imageUrl);
      } else {
        callback(null);
      }
    };

    getRequest.onerror = function() {
      callback(null);
    };
  };

  request.onerror = function() {
    callback(null);
  };
}

/**
 * ضغط وحفظ صورة الأرض (الطريقة القديمة - للاحتياط)
 */
function compressAndSaveFloorImage(file) {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const img = new Image();

  img.onload = function() {
    // تحديد أبعاد مناسبة حسب حجم الملف الأصلي (ضغط قوي للصور الكبيرة)
    let maxSize = 512; // أبعاد آمنة للتخزين
    let quality = 0.7; // جودة متوازنة

    // للصور الكبيرة جداً، استخدم ضغط قوي
    if (file.size > 20 * 1024 * 1024) { // أكبر من 20MB (صور 4K)
      maxSize = 256;
      quality = 0.5;
    } else if (file.size > 15 * 1024 * 1024) { // أكبر من 15MB
      maxSize = 320;
      quality = 0.55;
    } else if (file.size > 10 * 1024 * 1024) { // أكبر من 10MB (صور 2K)
      maxSize = 384;
      quality = 0.6;
    } else if (file.size > 5 * 1024 * 1024) { // أكبر من 5MB
      maxSize = 448;
      quality = 0.65;
    }

    let { width, height } = img;

    // حساب الأبعاد الجديدة مع الحفاظ على النسبة
    if (width > height) {
      if (width > maxSize) {
        height = (height * maxSize) / width;
        width = maxSize;
      }
    } else {
      if (height > maxSize) {
        width = (width * maxSize) / height;
        height = maxSize;
      }
    }

    canvas.width = width;
    canvas.height = height;

    // رسم الصورة المضغوطة
    ctx.drawImage(img, 0, 0, width, height);

    // ضغط متدرج أقوى مع حد أقصى للحجم
    let compressedImageUrl;
    let attempts = 0;
    const maxStorageSize = 1 * 1024 * 1024; // 1MB كحد أقصى للتخزين (آمن جداً)

    do {
      // استخدام JPEG دائماً للتحكم الأفضل في الحجم
      compressedImageUrl = canvas.toDataURL('image/jpeg', quality);

      // إذا كانت الصورة لا تزال كبيرة
      if (compressedImageUrl.length > maxStorageSize) {
        if (attempts < 5) {
          // قلل الجودة بخطوات أكبر
          quality = Math.max(quality - 0.1, 0.3);
          attempts++;
        } else {
          // قلل الأبعاد بنسبة أكبر
          width = Math.floor(width * 0.8);
          height = Math.floor(height * 0.8);

          if (width < 128 || height < 128) {
            showAlert('❌ لا يمكن ضغط الصورة أكثر. جرب صورة أصغر.');
            return;
          }

          canvas.width = width;
          canvas.height = height;
          ctx.drawImage(img, 0, 0, width, height);
          quality = Math.max(quality, 0.3); // الحد الأدنى للجودة
          attempts = 0;
        }
      }
    } while (compressedImageUrl.length > maxStorageSize && width >= 128 && height >= 128);

    // التحقق النهائي من الحجم
    if (compressedImageUrl.length > maxStorageSize) {
      showAlert('❌ الصورة كبيرة جداً للتخزين. جرب صورة أصغر أو بدقة أقل.');
      return;
    }

    try {
      // تنظيف التخزين قبل حفظ الصورة الجديدة
      cleanupStorageForFloor();

      // تم إزالة فحص المساحة المسبق - سنعتمد على try/catch

      // محاولة حفظ الصورة المضغوطة
      localStorage.setItem('flagClashCustomFloor', compressedImageUrl);

      // تحديث المعاينة
      updateFloorPreview(compressedImageUrl);

      const originalSizeMB = (file.size / (1024 * 1024)).toFixed(2);
      const compressedSizeKB = (compressedImageUrl.length / 1024).toFixed(0);

      showAlert(`✅ تم حفظ صورة الأرض بنجاح! (${originalSizeMB}MB → ${compressedSizeKB}KB)`);
      console.log(`📏 تم ضغط الصورة من ${originalSizeMB}MB إلى ${compressedSizeKB}KB بأبعاد ${width}x${height} وجودة ${quality}`);

    } catch (error) {
      if (error.name === 'QuotaExceededError') {
        showAlert('❌ مساحة التخزين ممتلئة. احذف بعض البيانات وحاول مرة أخرى.');
      } else {
        showAlert('❌ حدث خطأ في حفظ الصورة');
      }
      console.error('خطأ في حفظ صورة الأرض:', error);
    }
  };

  img.onerror = function() {
    showAlert('❌ فشل في تحميل الصورة');
  };

  // تحميل الصورة
  const reader = new FileReader();
  reader.onload = function(e) {
    img.src = e.target.result;
  };
  reader.readAsDataURL(file);
}

/**
 * إعادة تعيين صورة الأرض
 */
function resetFloorImage() {
  if (confirm('هل تريد إعادة تعيين الأرض للون الافتراضي؟')) {
    // إزالة من localStorage
    localStorage.removeItem('flagClashCustomFloor');
    localStorage.removeItem('flagClashFloorSaved');

    // إزالة من IndexedDB
    const request = indexedDB.open('FlagClashDB', 1);
    request.onsuccess = function(event) {
      const db = event.target.result;
      if (db.objectStoreNames.contains('floorImages')) {
        const transaction = db.transaction(['floorImages'], 'readwrite');
        const store = transaction.objectStore('floorImages');
        store.delete('currentFloor');
      }
    };

    updateFloorPreview(null);
    showAlert('✅ تم إعادة تعيين الأرض للون الافتراضي!');
  }
}

/**
 * تحديث معاينة الأرض
 */
function updateFloorPreview(imageUrl) {
  const preview = document.getElementById('currentFloorPreview');
  if (preview) {
    if (imageUrl) {
      preview.style.backgroundImage = `url(${imageUrl})`;
      preview.classList.add('has-image');
    } else {
      preview.style.backgroundImage = '';
      preview.classList.remove('has-image');
    }
  }
}

/**
 * عرض رسالة تنبيه
 */
function showAlert(message) {
  // إزالة التنبيه السابق
  const existingAlert = document.querySelector('.alert');
  if (existingAlert) {
    existingAlert.remove();
  }

  // إنشاء تنبيه جديد
  const alert = document.createElement('div');
  alert.className = 'alert';
  alert.textContent = message;
  document.body.appendChild(alert);

  // إزالة التنبيه بعد 3 ثوان
  setTimeout(() => {
    if (alert.parentNode) {
      alert.remove();
    }
  }, 3000);
}

/**
 * تصدير الإعدادات
 */
function exportSettings() {
  try {
    const dataStr = JSON.stringify(currentSettings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = 'flag-clash-settings.json';
    link.click();
    
    showAlert('📁 تم تصدير الإعدادات بنجاح!');
  } catch (error) {
    console.error('خطأ في تصدير الإعدادات:', error);
    showAlert('❌ حدث خطأ في تصدير الإعدادات');
  }
}

/**
 * استيراد الإعدادات
 */
function importSettings() {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  
  input.onchange = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedSettings = JSON.parse(e.target.result);
        
        // التحقق من صحة البيانات
        if (typeof importedSettings === 'object' && importedSettings !== null) {
          currentSettings = { ...defaultSettings, ...importedSettings };
          updateUI();
          showAlert('📥 تم استيراد الإعدادات بنجاح!');
          console.log('تم استيراد الإعدادات:', currentSettings);
        } else {
          throw new Error('ملف الإعدادات غير صحيح');
        }
      } catch (error) {
        console.error('خطأ في استيراد الإعدادات:', error);
        showAlert('❌ ملف الإعدادات غير صحيح');
      }
    };
    
    reader.readAsText(file);
  };
  
  input.click();
}

/**
 * معاينة الإعدادات
 */
function previewSettings() {
  const preview = {
    'التأثيرات البصرية': currentSettings.particleEffects ? 'مفعلة' : 'معطلة',
    'الدوران التلقائي': currentSettings.autoRotate ? 'مفعل' : 'معطل',
    'سرعة الدوران': currentSettings.rotationSpeed,
    'الحد الأقصى للحجم': currentSettings.maxCubeSize,
    'معدل النمو': currentSettings.growthRate,
    'الأصوات': currentSettings.soundEnabled ? 'مفعلة' : 'معطلة',
    'مستوى الصوت': currentSettings.soundVolume + '%',
    'وضع التجربة': currentSettings.demoMode ? 'مفعل' : 'معطل',
    'سرعة التجربة': currentSettings.demoSpeed + ' ثانية',
    'عدد الدول': currentSettings.countryCount + ' دول'
  };
  
  let previewText = 'معاينة الإعدادات الحالية:\n\n';
  for (const [key, value] of Object.entries(preview)) {
    previewText += `${key}: ${value}\n`;
  }
  
  alert(previewText);
}

// إضافة أزرار إضافية للإعدادات المتقدمة
document.addEventListener('DOMContentLoaded', () => {
  // إضافة أزرار التصدير والاستيراد
  const actionsDiv = document.querySelector('.actions');
  if (actionsDiv) {
    const exportBtn = document.createElement('button');
    exportBtn.className = 'btn btn-secondary';
    exportBtn.innerHTML = '📁 تصدير الإعدادات';
    exportBtn.onclick = exportSettings;
    
    const importBtn = document.createElement('button');
    importBtn.className = 'btn btn-secondary';
    importBtn.innerHTML = '📥 استيراد الإعدادات';
    importBtn.onclick = importSettings;
    
    const previewBtn = document.createElement('button');
    previewBtn.className = 'btn btn-secondary';
    previewBtn.innerHTML = '👁️ معاينة الإعدادات';
    previewBtn.onclick = previewSettings;
    
    // إدراج الأزرار قبل زر الإغلاق
    const closeBtn = actionsDiv.querySelector('button:last-child');
    actionsDiv.insertBefore(exportBtn, closeBtn);
    actionsDiv.insertBefore(importBtn, closeBtn);
    actionsDiv.insertBefore(previewBtn, closeBtn);
  }
});

/**
 * عرض الدول المخصصة
 */
function renderCustomCountries() {
  const container = document.getElementById('customCountriesContainer');
  if (!container) return;

  let html = '';

  // عرض الدول الافتراضية مع إمكانية التخصيص
  Object.keys(defaultCountries).forEach((countryCode, index) => {
    const country = defaultCountries[countryCode];
    const custom = customCountries[countryCode] || {};

    html += `
      <div class="country-item" data-country="${countryCode}">
        <div class="country-header">
          <span class="country-number">${index + 1}.</span>
          <span class="country-flag-preview">${country.flag}</span>
          <span>${custom.customName || country.name} (${countryCode})</span>
          <span class="join-command">join ${index + 1}</span>
        </div>

        <div class="country-inputs">
          <div class="input-group">
            <label>اسم مخصص:</label>
            <input type="text" class="text-input"
                   placeholder="${country.name}"
                   value="${custom.customName || ''}"
                   onchange="updateCountryName('${countryCode}', this.value)">
          </div>

          <div class="input-group">
            <label>رفع صورة العلم:</label>
            <input type="file" class="file-input" id="flag-${countryCode}"
                   accept="image/*" onchange="updateCountryFlag('${countryCode}', this)">
            <label for="flag-${countryCode}" class="file-input-label">
              📁 اختيار صورة
            </label>
          </div>

          <div class="input-group">
            <label>نوع الهدية المطلوبة:</label>
            <select class="select-box" onchange="updateGiftType('${countryCode}', this.value)">
              <option value="" ${!custom.giftType ? 'selected' : ''}>لا يوجد</option>

              <!-- هدايا قوة 1 -->
              <option value="Love you so much" ${custom.giftType === 'Love you so much' ? 'selected' : ''}>💕 Love you so much - قوة: 1</option>
              <option value="Wink Charm" ${custom.giftType === 'Wink Charm' ? 'selected' : ''}>😉 Wink Charm - قوة: 1</option>
              <option value="GG" ${custom.giftType === 'GG' ? 'selected' : ''}>🎮 GG - قوة: 1</option>
              <option value="Ice Cream Cone" ${custom.giftType === 'Ice Cream Cone' ? 'selected' : ''}>🍦 Ice Cream Cone - قوة: 1</option>
              <option value="Rose" ${custom.giftType === 'Rose' ? 'selected' : ''}>🌹 Rose - قوة: 1</option>
              <option value="TikTok" ${custom.giftType === 'TikTok' ? 'selected' : ''}>🎵 TikTok - قوة: 1</option>
              <option value="Heart Me" ${custom.giftType === 'Heart Me' ? 'selected' : ''}>💖 Heart Me - قوة: 1</option>
              <option value="Thumbs Up" ${custom.giftType === 'Thumbs Up' ? 'selected' : ''}>👍 Thumbs Up - قوة: 1</option>
              <option value="Cake Slice" ${custom.giftType === 'Cake Slice' ? 'selected' : ''}>🍰 Cake Slice - قوة: 1</option>
              <option value="Glow Stick" ${custom.giftType === 'Glow Stick' ? 'selected' : ''}>✨ Glow Stick - قوة: 1</option>
              <option value="Love you" ${custom.giftType === 'Love you' ? 'selected' : ''}>💝 Love you - قوة: 1</option>
              <option value="Enjoy Music" ${custom.giftType === 'Enjoy Music' ? 'selected' : ''}>🎶 Enjoy Music - قوة: 1</option>
              <option value="Happy Eid" ${custom.giftType === 'Happy Eid' ? 'selected' : ''}>🌙 Happy Eid - قوة: 1</option>
              <option value="Music Mic" ${custom.giftType === 'Music Mic' ? 'selected' : ''}>🎤 Music Mic - قوة: 1</option>
              <option value="Level Up" ${custom.giftType === 'Level Up' ? 'selected' : ''}>⬆️ Level Up - قوة: 1</option>
              <option value="really hot" ${custom.giftType === 'really hot' ? 'selected' : ''}>🔥 really hot - قوة: 1</option>
              <option value="Go Popular" ${custom.giftType === 'Go Popular' ? 'selected' : ''}>📈 Go Popular - قوة: 1</option>
              <option value="Club Cheers" ${custom.giftType === 'Club Cheers' ? 'selected' : ''}>🥂 Club Cheers - قوة: 1</option>

              <!-- هدايا قوة متوسطة -->
              <option value="Team Bracelet" ${custom.giftType === 'Team Bracelet' ? 'selected' : ''}>🔗 Team Bracelet - قوة: 2</option>
              <option value="Diamond" ${custom.giftType === 'Diamond' ? 'selected' : ''}>💎 Diamond - قوة: 5</option>
              <option value="Finger Heart" ${custom.giftType === 'Finger Heart' ? 'selected' : ''}>🤏 Finger Heart - قوة: 5</option>
              <option value="Yeet" ${custom.giftType === 'Yeet' ? 'selected' : ''}>🎯 Yeet - قوة: 8</option>
              <option value="Cheer You Up" ${custom.giftType === 'Cheer You Up' ? 'selected' : ''}>😊 Cheer You Up - قوة: 9</option>
              <option value="Club Power" ${custom.giftType === 'Club Power' ? 'selected' : ''}>💪 Club Power - قوة: 9</option>
              <option value="Heart" ${custom.giftType === 'Heart' ? 'selected' : ''}>❤️ Heart - قوة: 10</option>
              <option value="Heart Gaze" ${custom.giftType === 'Heart Gaze' ? 'selected' : ''}>👀 Heart Gaze - قوة: 10</option>
              <option value="Friendship Necklace" ${custom.giftType === 'Friendship Necklace' ? 'selected' : ''}>📿 Friendship Necklace - قوة: 10</option>
              <option value="Rosa" ${custom.giftType === 'Rosa' ? 'selected' : ''}>🌹 Rosa - قوة: 10</option>
              <option value="Stitch" ${custom.giftType === 'Stitch' ? 'selected' : ''}>🧸 Stitch - قوة: 10</option>
              <option value="Go Go Go" ${custom.giftType === 'Go Go Go' ? 'selected' : ''}>🚀 Go Go Go - قوة: 10</option>
              <option value="Celestial Badge" ${custom.giftType === 'Celestial Badge' ? 'selected' : ''}>🌟 Celestial Badge - قوة: 10</option>
              <option value="Gold Boxing Gloves" ${custom.giftType === 'Gold Boxing Gloves' ? 'selected' : ''}>🥊 Gold Boxing Gloves - قوة: 10</option>
              <option value="Crown" ${custom.giftType === 'Crown' ? 'selected' : ''}>👑 Crown - قوة: 10</option>
              <option value="Next Legends" ${custom.giftType === 'Next Legends' ? 'selected' : ''}>🏆 Next Legends - قوة: 15</option>
              <option value="Rocket" ${custom.giftType === 'Rocket' ? 'selected' : ''}>🚀 Rocket - قوة: 20</option>
              <option value="Perfume" ${custom.giftType === 'Perfume' ? 'selected' : ''}>🌸 Perfume - قوة: 20</option>
              <option value="Doughnut" ${custom.giftType === 'Doughnut' ? 'selected' : ''}>🍩 Doughnut - قوة: 30</option>
              <option value="Witchy Kitty" ${custom.giftType === 'Witchy Kitty' ? 'selected' : ''}>🐱 Witchy Kitty - قوة: 30</option>
              <option value="Butterfly" ${custom.giftType === 'Butterfly' ? 'selected' : ''}>🦋 Butterfly - قوة: 88</option>
              <option value="Family" ${custom.giftType === 'Family' ? 'selected' : ''}>👨‍👩‍👧‍👦 Family - قوة: 90</option>

              <!-- هدايا قوة 99 -->
              <option value="Paper Crane" ${custom.giftType === 'Paper Crane' ? 'selected' : ''}>🕊️ Paper Crane - قوة: 99</option>
              <option value="Little Crown" ${custom.giftType === 'Little Crown' ? 'selected' : ''}>👑 Little Crown - قوة: 99</option>
              <option value="Cap" ${custom.giftType === 'Cap' ? 'selected' : ''}>🧢 Cap - قوة: 99</option>
              <option value="Hat and Mustache" ${custom.giftType === 'Hat and Mustache' ? 'selected' : ''}>🎩 Hat and Mustache - قوة: 99</option>
              <option value="Noor" ${custom.giftType === 'Noor' ? 'selected' : ''}>✨ Noor - قوة: 99</option>
              <option value="Like-Pop" ${custom.giftType === 'Like-Pop' ? 'selected' : ''}>👍 Like-Pop - قوة: 99</option>
              <option value="Love Painting" ${custom.giftType === 'Love Painting' ? 'selected' : ''}>🎨 Love Painting - قوة: 99</option>
              <option value="Little Wing" ${custom.giftType === 'Little Wing' ? 'selected' : ''}>🪶 Little Wing - قوة: 99</option>
              <option value="Love Chain" ${custom.giftType === 'Love Chain' ? 'selected' : ''}>⛓️ Love Chain - قوة: 99</option>
              <option value="Bubble Gum" ${custom.giftType === 'Bubble Gum' ? 'selected' : ''}>🍬 Bubble Gum - قوة: 99</option>
              <option value="Mark of Love" ${custom.giftType === 'Mark of Love' ? 'selected' : ''}>💖 Mark of Love - قوة: 99</option>
              <option value="Star" ${custom.giftType === 'Star' ? 'selected' : ''}>⭐ Star - قوة: 99</option>
              <option value="Club Victory" ${custom.giftType === 'Club Victory' ? 'selected' : ''}>🏆 Club Victory - قوة: 99</option>
              <option value="Level-up Sparks" ${custom.giftType === 'Level-up Sparks' ? 'selected' : ''}>✨ Level-up Sparks - قوة: 99</option>
              <option value="Greeting Heart" ${custom.giftType === 'Greeting Heart' ? 'selected' : ''}>💝 Greeting Heart - قوة: 99</option>

              <!-- هدايا قوة 100 -->
              <option value="Singing Magic" ${custom.giftType === 'Singing Magic' ? 'selected' : ''}>🎵 Singing Magic - قوة: 100</option>
              <option value="Confetti" ${custom.giftType === 'Confetti' ? 'selected' : ''}>🎊 Confetti - قوة: 100</option>
              <option value="Hand Hearts" ${custom.giftType === 'Hand Hearts' ? 'selected' : ''}>👐 Hand Hearts - قوة: 100</option>
              <option value="Hand Heart" ${custom.giftType === 'Hand Heart' ? 'selected' : ''}>🤲 Hand Heart - قوة: 100</option>
              <option value="Balloon Gift Box" ${custom.giftType === 'Balloon Gift Box' ? 'selected' : ''}>🎁 Balloon Gift Box - قوة: 100</option>
              <option value="Shell Energy" ${custom.giftType === 'Shell Energy' ? 'selected' : ''}>🐚 Shell Energy - قوة: 100</option>
              <option value="Mini Star" ${custom.giftType === 'Mini Star' ? 'selected' : ''}>⭐ Mini Star - قوة: 100</option>
              <option value="Bouquet" ${custom.giftType === 'Bouquet' ? 'selected' : ''}>💐 Bouquet - قوة: 100</option>
              <option value="Marvelous Confetti" ${custom.giftType === 'Marvelous Confetti' ? 'selected' : ''}>🎉 Marvelous Confetti - قوة: 100</option>

              <!-- هدايا قوة 149-150 -->
              <option value="Heart Rain" ${custom.giftType === 'Heart Rain' ? 'selected' : ''}>💕 Heart Rain - قوة: 149</option>
              <option value="Bowknot" ${custom.giftType === 'Bowknot' ? 'selected' : ''}>🎀 Bowknot - قوة: 149</option>
              <option value="Dizzy Bird" ${custom.giftType === 'Dizzy Bird' ? 'selected' : ''}>🐦 Dizzy Bird - قوة: 149</option>
              <option value="Big Shout Out" ${custom.giftType === 'Big Shout Out' ? 'selected' : ''}>📢 Big Shout Out - قوة: 149</option>
              <option value="Chatting Popcorn" ${custom.giftType === 'Chatting Popcorn' ? 'selected' : ''}>🍿 Chatting Popcorn - قوة: 149</option>
              <option value="Kiss" ${custom.giftType === 'Kiss' ? 'selected' : ''}>💋 Kiss - قوة: 150</option>
              <option value="Sceptre" ${custom.giftType === 'Sceptre' ? 'selected' : ''}>🪄 Sceptre - قوة: 150</option>

              <!-- هدايا قوة 199 -->
              <option value="Love Charger" ${custom.giftType === 'Love Charger' ? 'selected' : ''}>🔋 Love Charger - قوة: 199</option>
              <option value="Sunglasses" ${custom.giftType === 'Sunglasses' ? 'selected' : ''}>🕶️ Sunglasses - قوة: 199</option>
              <option value="Hearts" ${custom.giftType === 'Hearts' ? 'selected' : ''}>💕 Hearts - قوة: 199</option>
              <option value="Garland Headpiece" ${custom.giftType === 'Garland Headpiece' ? 'selected' : ''}>🌸 Garland Headpiece - قوة: 199</option>
              <option value="Love You" ${custom.giftType === 'Love You' ? 'selected' : ''}>💖 Love You - قوة: 199</option>
              <option value="Cheer For You" ${custom.giftType === 'Cheer For You' ? 'selected' : ''}>📣 Cheer For You - قوة: 199</option>
              <option value="Stinging Bee" ${custom.giftType === 'Stinging Bee' ? 'selected' : ''}>🐝 Stinging Bee - قوة: 199</option>
              <option value="Massage for You" ${custom.giftType === 'Massage for You' ? 'selected' : ''}>💆 Massage for You - قوة: 199</option>
              <option value="Coffee Magic" ${custom.giftType === 'Coffee Magic' ? 'selected' : ''}>☕ Coffee Magic - قوة: 199</option>
              <option value="Dancing Hands" ${custom.giftType === 'Dancing Hands' ? 'selected' : ''}>👐 Dancing Hands - قوة: 199</option>
              <option value="Meerkat" ${custom.giftType === 'Meerkat' ? 'selected' : ''}>🦫 Meerkat - قوة: 199</option>
              <option value="Cheering Crab" ${custom.giftType === 'Cheering Crab' ? 'selected' : ''}>🦀 Cheering Crab - قوة: 199</option>
              <option value="The Crown" ${custom.giftType === 'The Crown' ? 'selected' : ''}>👑 The Crown - قوة: 199</option>
              <option value="Night Star" ${custom.giftType === 'Night Star' ? 'selected' : ''}>🌟 Night Star - قوة: 199</option>
              <option value="Twinkling Star" ${custom.giftType === 'Twinkling Star' ? 'selected' : ''}>✨ Twinkling Star - قوة: 199</option>
              <option value="Floating Octopus" ${custom.giftType === 'Floating Octopus' ? 'selected' : ''}>🐙 Floating Octopus - قوة: 199</option>
              <option value="Flower Headband" ${custom.giftType === 'Flower Headband' ? 'selected' : ''}>🌺 Flower Headband - قوة: 199</option>

              <!-- هدايا قوة 249-299 -->
              <option value="Pinch Face" ${custom.giftType === 'Pinch Face' ? 'selected' : ''}>😊 Pinch Face - قوة: 249</option>
              <option value="Candy Bouquet" ${custom.giftType === 'Candy Bouquet' ? 'selected' : ''}>🍭 Candy Bouquet - قوة: 249</option>
              <option value="Hawaiian Lei" ${custom.giftType === 'Hawaiian Lei' ? 'selected' : ''}>🌺 Hawaiian Lei - قوة: 299</option>
              <option value="Boxing Gloves" ${custom.giftType === 'Boxing Gloves' ? 'selected' : ''}>🥊 Boxing Gloves - قوة: 299</option>
              <option value="Corgi" ${custom.giftType === 'Corgi' ? 'selected' : ''}>🐕 Corgi - قوة: 299</option>
              <option value="Victory Sign" ${custom.giftType === 'Victory Sign' ? 'selected' : ''}>✌️ Victory Sign - قوة: 299</option>
              <option value="Fruit Friends" ${custom.giftType === 'Fruit Friends' ? 'selected' : ''}>🍎 Fruit Friends - قوة: 299</option>
              <option value="Naughty Chicken" ${custom.giftType === 'Naughty Chicken' ? 'selected' : ''}>🐔 Naughty Chicken - قوة: 299</option>
              <option value="Play for You" ${custom.giftType === 'Play for You' ? 'selected' : ''}>🎮 Play for You - قوة: 299</option>
              <option value="Rock Star" ${custom.giftType === 'Rock Star' ? 'selected' : ''}>🎸 Rock Star - قوة: 299</option>
              <option value="Elephant trunk" ${custom.giftType === 'Elephant trunk' ? 'selected' : ''}>🐘 Elephant trunk - قوة: 299</option>
              <option value="Butterfly for You" ${custom.giftType === 'Butterfly for You' ? 'selected' : ''}>🦋 Butterfly for You - قوة: 299</option>
              <option value="Golden Crown" ${custom.giftType === 'Golden Crown' ? 'selected' : ''}>👑 Golden Crown - قوة: 299</option>
              <option value="Starlight Compass" ${custom.giftType === 'Starlight Compass' ? 'selected' : ''}>🧭 Starlight Compass - قوة: 299</option>
              <option value="Puppy Kisses" ${custom.giftType === 'Puppy Kisses' ? 'selected' : ''}>🐶 Puppy Kisses - قوة: 299</option>
              <option value="LIVE Ranking Crown" ${custom.giftType === 'LIVE Ranking Crown' ? 'selected' : ''}>👑 LIVE Ranking Crown - قوة: 299</option>
              <option value="United Heart" ${custom.giftType === 'United Heart' ? 'selected' : ''}>💞 United Heart - قوة: 299</option>
              <option value="Budding Heart" ${custom.giftType === 'Budding Heart' ? 'selected' : ''}>💗 Budding Heart - قوة: 299</option>

              <!-- هدايا قوة 300-399 -->
              <option value="Birthday Cake" ${custom.giftType === 'Birthday Cake' ? 'selected' : ''}>🎂 Birthday Cake - قوة: 300</option>
              <option value="Feather Mask" ${custom.giftType === 'Feather Mask' ? 'selected' : ''}>🎭 Feather Mask - قوة: 300</option>
              <option value="Forever Rosa" ${custom.giftType === 'Forever Rosa' ? 'selected' : ''}>🌹 Forever Rosa - قوة: 399</option>
              <option value="Magic Rhythm" ${custom.giftType === 'Magic Rhythm' ? 'selected' : ''}>🎵 Magic Rhythm - قوة: 399</option>
              <option value="Relaxed Goose" ${custom.giftType === 'Relaxed Goose' ? 'selected' : ''}>🦢 Relaxed Goose - قوة: 399</option>
              <option value="Tom's Hug" ${custom.giftType === "Tom's Hug" ? 'selected' : ''}>🤗 Tom's Hug - قوة: 399</option>
              <option value="Rosie the Rose Bean" ${custom.giftType === 'Rosie the Rose Bean' ? 'selected' : ''}>🌹 Rosie the Rose Bean - قوة: 399</option>
              <option value="Jollie the Joy Bean" ${custom.giftType === 'Jollie the Joy Bean' ? 'selected' : ''}>😊 Jollie the Joy Bean - قوة: 399</option>
              <option value="Rocky the Rock Bean" ${custom.giftType === 'Rocky the Rock Bean' ? 'selected' : ''}>🪨 Rocky the Rock Bean - قوة: 399</option>
              <option value="Sage the Smart Bean" ${custom.giftType === 'Sage the Smart Bean' ? 'selected' : ''}>🧠 Sage the Smart Bean - قوة: 399</option>
              <option value="Sage's Slash" ${custom.giftType === "Sage's Slash" ? 'selected' : ''}>⚔️ Sage's Slash - قوة: 399</option>
              <option value="Let butterfly dances" ${custom.giftType === 'Let butterfly dances' ? 'selected' : ''}>🦋 Let butterfly dances - قوة: 399</option>
              <option value="Kitten Kneading" ${custom.giftType === 'Kitten Kneading' ? 'selected' : ''}>🐱 Kitten Kneading - قوة: 399</option>
              <option value="Shoot the Apple" ${custom.giftType === 'Shoot the Apple' ? 'selected' : ''}>🍎 Shoot the Apple - قوة: 399</option>
              <option value="Alien Buddy" ${custom.giftType === 'Alien Buddy' ? 'selected' : ''}>👽 Alien Buddy - قوة: 399</option>

              <!-- هدايا قوة 400-500 -->
              <option value="Crystal Dreams" ${custom.giftType === 'Crystal Dreams' ? 'selected' : ''}>💎 Crystal Dreams - قوة: 400</option>
              <option value="Wishing Cake" ${custom.giftType === 'Wishing Cake' ? 'selected' : ''}>🎂 Wishing Cake - قوة: 400</option>
              <option value="Mic Champ" ${custom.giftType === 'Mic Champ' ? 'selected' : ''}>🎤 Mic Champ - قوة: 400</option>
              <option value="Beating Heart" ${custom.giftType === 'Beating Heart' ? 'selected' : ''}>💓 Beating Heart - قوة: 449</option>
              <option value="Fairy Mask" ${custom.giftType === 'Fairy Mask' ? 'selected' : ''}>🧚 Fairy Mask - قوة: 450</option>
              <option value="Powerful Mind" ${custom.giftType === 'Powerful Mind' ? 'selected' : ''}>🧠 Powerful Mind - قوة: 450</option>
              <option value="Hat of Joy" ${custom.giftType === 'Hat of Joy' ? 'selected' : ''}>🎩 Hat of Joy - قوة: 450</option>
              <option value="Coral" ${custom.giftType === 'Coral' ? 'selected' : ''}>🪸 Coral - قوة: 499</option>
              <option value="Hands Up" ${custom.giftType === 'Hands Up' ? 'selected' : ''}>🙌 Hands Up - قوة: 499</option>
              <option value="Flower Show" ${custom.giftType === 'Flower Show' ? 'selected' : ''}>🌺 Flower Show - قوة: 500</option>
              <option value="Money Gun" ${custom.giftType === 'Money Gun' ? 'selected' : ''}>💰 Money Gun - قوة: 500</option>
            </select>
            <small style="color: #888; font-size: 11px; display: block; margin-top: 5px;">
              💡 تم إضافة جميع هدايا TikTok الحقيقية مع قيمها الصحيحة (133 هدية!)
            </small>
          </div>
        </div>

        <div class="country-actions">
          <button class="btn btn-secondary btn-small" onclick="resetCountry('${countryCode}')">
            🔄 إعادة تعيين
          </button>
        </div>
      </div>
    `;
  });

  container.innerHTML = html;
}

/**
 * تحديث اسم دولة
 */
function updateCountryName(countryCode, customName) {
  if (!customCountries[countryCode]) {
    customCountries[countryCode] = {};
  }

  customCountries[countryCode].customName = customName.trim() || null;
  saveCustomCountries();

  console.log(`تم تحديث اسم ${countryCode} إلى: ${customName}`);
}

/**
 * تحديث علم دولة
 */
function updateCountryFlag(countryCode, fileInput) {
  const file = fileInput.files[0];
  if (!file) return;

  // التحقق من نوع الملف
  if (!file.type.startsWith('image/')) {
    showAlert('❌ يرجى اختيار ملف صورة صحيح');
    return;
  }

  // التحقق من حجم الملف (أقل من 5MB)
  if (file.size > 5 * 1024 * 1024) {
    showAlert('❌ حجم الصورة كبير جداً (أقل من 5MB)');
    return;
  }

  const reader = new FileReader();
  reader.onload = function(e) {
    if (!customCountries[countryCode]) {
      customCountries[countryCode] = {};
    }

    customCountries[countryCode].customFlag = e.target.result;
    saveCustomCountries();

    showAlert(`✅ تم تحديث علم ${defaultCountries[countryCode].name}`);
    console.log(`تم تحديث علم ${countryCode}`);
  };

  reader.readAsDataURL(file);
}

/**
 * تحديث نوع الهدية لدولة
 */
function updateGiftType(countryCode, giftType) {
  if (!customCountries[countryCode]) {
    customCountries[countryCode] = {};
  }

  customCountries[countryCode].giftType = giftType || null;
  saveCustomCountries();

  const giftName = giftType ? giftType : 'لا يوجد';
  showAlert(`✅ تم تحديث نوع الهدية لـ ${defaultCountries[countryCode].name} إلى: ${giftName}`);
  console.log(`تم تحديث نوع الهدية لـ ${countryCode} إلى: ${giftType}`);
}

/**
 * إعادة تعيين دولة
 */
function resetCountry(countryCode) {
  if (confirm(`هل تريد إعادة تعيين تخصيصات ${defaultCountries[countryCode].name}؟`)) {
    delete customCountries[countryCode];
    saveCustomCountries();
    renderCustomCountries();
    showAlert(`🔄 تم إعادة تعيين ${defaultCountries[countryCode].name}`);
  }
}

/**
 * إضافة دولة جديدة
 */
function addNewCountry() {
  // إنشاء نافذة حوار لإضافة دولة جديدة
  const modal = document.createElement('div');
  modal.className = 'modal-overlay';
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h3>➕ إضافة دولة جديدة</h3>
        <button class="modal-close" onclick="closeModal()">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label>رمز الدولة (مثل: JP, KR, BR):</label>
          <input type="text" id="newCountryCode" placeholder="مثال: JP" maxlength="2" style="text-transform: uppercase;">
        </div>
        <div class="form-group">
          <label>اسم الدولة:</label>
          <input type="text" id="newCountryName" placeholder="مثال: اليابان">
        </div>
        <div class="form-group">
          <label>إيموجي العلم:</label>
          <input type="text" id="newCountryFlag" placeholder="مثال: 🇯🇵">
        </div>
        <div class="form-group">
          <label>لون الدولة:</label>
          <input type="color" id="newCountryColor" value="#ff0000">
        </div>
        <div class="form-group">
          <label>نوع الهدية المرتبطة (اختياري):</label>
          <select id="newCountryGiftType">
            <option value="">بدون ربط</option>

            <!-- هدايا قوة 1 -->
            <option value="Love you so much">💕 Love you so much - قوة: 1</option>
            <option value="Wink Charm">😉 Wink Charm - قوة: 1</option>
            <option value="GG">🎮 GG - قوة: 1</option>
            <option value="Ice Cream Cone">🍦 Ice Cream Cone - قوة: 1</option>
            <option value="Rose">🌹 Rose - قوة: 1</option>
            <option value="TikTok">🎵 TikTok - قوة: 1</option>
            <option value="Heart Me">💖 Heart Me - قوة: 1</option>
            <option value="Thumbs Up">👍 Thumbs Up - قوة: 1</option>
            <option value="Cake Slice">🍰 Cake Slice - قوة: 1</option>
            <option value="Glow Stick">✨ Glow Stick - قوة: 1</option>
            <option value="Love you">💝 Love you - قوة: 1</option>
            <option value="Enjoy Music">🎶 Enjoy Music - قوة: 1</option>
            <option value="Happy Eid">🌙 Happy Eid - قوة: 1</option>
            <option value="Music Mic">🎤 Music Mic - قوة: 1</option>
            <option value="Level Up">⬆️ Level Up - قوة: 1</option>
            <option value="really hot">🔥 really hot - قوة: 1</option>
            <option value="Go Popular">📈 Go Popular - قوة: 1</option>
            <option value="Club Cheers">🥂 Club Cheers - قوة: 1</option>

            <!-- هدايا قوة متوسطة -->
            <option value="Team Bracelet">🔗 Team Bracelet - قوة: 2</option>
            <option value="Diamond">💎 Diamond - قوة: 5</option>
            <option value="Finger Heart">🤏 Finger Heart - قوة: 5</option>
            <option value="Yeet">🎯 Yeet - قوة: 8</option>
            <option value="Cheer You Up">😊 Cheer You Up - قوة: 9</option>
            <option value="Club Power">💪 Club Power - قوة: 9</option>
            <option value="Heart">❤️ Heart - قوة: 10</option>
            <option value="Heart Gaze">👀 Heart Gaze - قوة: 10</option>
            <option value="Friendship Necklace">📿 Friendship Necklace - قوة: 10</option>
            <option value="Rosa">🌹 Rosa - قوة: 10</option>
            <option value="Stitch">🧸 Stitch - قوة: 10</option>
            <option value="Go Go Go">🚀 Go Go Go - قوة: 10</option>
            <option value="Celestial Badge">🌟 Celestial Badge - قوة: 10</option>
            <option value="Gold Boxing Gloves">🥊 Gold Boxing Gloves - قوة: 10</option>
            <option value="Crown">👑 Crown - قوة: 10</option>
            <option value="Next Legends">🏆 Next Legends - قوة: 15</option>
            <option value="Rocket">🚀 Rocket - قوة: 20</option>
            <option value="Perfume">🌸 Perfume - قوة: 20</option>
            <option value="Doughnut">🍩 Doughnut - قوة: 30</option>
            <option value="Witchy Kitty">🐱 Witchy Kitty - قوة: 30</option>
            <option value="Butterfly">🦋 Butterfly - قوة: 88</option>
            <option value="Family">👨‍👩‍👧‍👦 Family - قوة: 90</option>

            <!-- هدايا قوة 99 -->
            <option value="Paper Crane">🕊️ Paper Crane - قوة: 99</option>
            <option value="Little Crown">👑 Little Crown - قوة: 99</option>
            <option value="Cap">🧢 Cap - قوة: 99</option>
            <option value="Hat and Mustache">🎩 Hat and Mustache - قوة: 99</option>
            <option value="Noor">✨ Noor - قوة: 99</option>
            <option value="Like-Pop">👍 Like-Pop - قوة: 99</option>
            <option value="Love Painting">🎨 Love Painting - قوة: 99</option>
            <option value="Little Wing">🪶 Little Wing - قوة: 99</option>
            <option value="Love Chain">⛓️ Love Chain - قوة: 99</option>
            <option value="Bubble Gum">🍬 Bubble Gum - قوة: 99</option>
            <option value="Mark of Love">💖 Mark of Love - قوة: 99</option>
            <option value="Star">⭐ Star - قوة: 99</option>
            <option value="Club Victory">🏆 Club Victory - قوة: 99</option>
            <option value="Level-up Sparks">✨ Level-up Sparks - قوة: 99</option>
            <option value="Greeting Heart">💝 Greeting Heart - قوة: 99</option>

            <!-- هدايا قوة 100 -->
            <option value="Singing Magic">🎵 Singing Magic - قوة: 100</option>
            <option value="Confetti">🎊 Confetti - قوة: 100</option>
            <option value="Hand Hearts">👐 Hand Hearts - قوة: 100</option>
            <option value="Hand Heart">🤲 Hand Heart - قوة: 100</option>
            <option value="Balloon Gift Box">🎁 Balloon Gift Box - قوة: 100</option>
            <option value="Shell Energy">🐚 Shell Energy - قوة: 100</option>
            <option value="Mini Star">⭐ Mini Star - قوة: 100</option>
            <option value="Bouquet">💐 Bouquet - قوة: 100</option>
            <option value="Marvelous Confetti">🎉 Marvelous Confetti - قوة: 100</option>

            <!-- هدايا قوة 149-150 -->
            <option value="Heart Rain">💕 Heart Rain - قوة: 149</option>
            <option value="Bowknot">🎀 Bowknot - قوة: 149</option>
            <option value="Dizzy Bird">🐦 Dizzy Bird - قوة: 149</option>
            <option value="Big Shout Out">📢 Big Shout Out - قوة: 149</option>
            <option value="Chatting Popcorn">🍿 Chatting Popcorn - قوة: 149</option>
            <option value="Kiss">💋 Kiss - قوة: 150</option>
            <option value="Sceptre">🪄 Sceptre - قوة: 150</option>

            <!-- هدايا قوة 199 -->
            <option value="Love Charger">🔋 Love Charger - قوة: 199</option>
            <option value="Sunglasses">🕶️ Sunglasses - قوة: 199</option>
            <option value="Hearts">💕 Hearts - قوة: 199</option>
            <option value="Garland Headpiece">🌸 Garland Headpiece - قوة: 199</option>
            <option value="Love You">💖 Love You - قوة: 199</option>
            <option value="Cheer For You">📣 Cheer For You - قوة: 199</option>
            <option value="Stinging Bee">🐝 Stinging Bee - قوة: 199</option>
            <option value="Massage for You">💆 Massage for You - قوة: 199</option>
            <option value="Coffee Magic">☕ Coffee Magic - قوة: 199</option>
            <option value="Dancing Hands">👐 Dancing Hands - قوة: 199</option>
            <option value="Meerkat">🦫 Meerkat - قوة: 199</option>
            <option value="Cheering Crab">🦀 Cheering Crab - قوة: 199</option>
            <option value="The Crown">👑 The Crown - قوة: 199</option>
            <option value="Night Star">🌟 Night Star - قوة: 199</option>
            <option value="Twinkling Star">✨ Twinkling Star - قوة: 199</option>
            <option value="Floating Octopus">🐙 Floating Octopus - قوة: 199</option>
            <option value="Flower Headband">🌺 Flower Headband - قوة: 199</option>

            <!-- هدايا قوة 249-299 -->
            <option value="Pinch Face">😊 Pinch Face - قوة: 249</option>
            <option value="Candy Bouquet">🍭 Candy Bouquet - قوة: 249</option>
            <option value="Hawaiian Lei">🌺 Hawaiian Lei - قوة: 299</option>
            <option value="Boxing Gloves">🥊 Boxing Gloves - قوة: 299</option>
            <option value="Corgi">🐕 Corgi - قوة: 299</option>
            <option value="Victory Sign">✌️ Victory Sign - قوة: 299</option>
            <option value="Fruit Friends">🍎 Fruit Friends - قوة: 299</option>
            <option value="Naughty Chicken">🐔 Naughty Chicken - قوة: 299</option>
            <option value="Play for You">🎮 Play for You - قوة: 299</option>
            <option value="Rock Star">🎸 Rock Star - قوة: 299</option>
            <option value="Elephant trunk">🐘 Elephant trunk - قوة: 299</option>
            <option value="Butterfly for You">🦋 Butterfly for You - قوة: 299</option>
            <option value="Golden Crown">👑 Golden Crown - قوة: 299</option>
            <option value="Starlight Compass">🧭 Starlight Compass - قوة: 299</option>
            <option value="Puppy Kisses">🐶 Puppy Kisses - قوة: 299</option>
            <option value="LIVE Ranking Crown">👑 LIVE Ranking Crown - قوة: 299</option>
            <option value="United Heart">💞 United Heart - قوة: 299</option>
            <option value="Budding Heart">💗 Budding Heart - قوة: 299</option>

            <!-- هدايا قوة 300-399 -->
            <option value="Birthday Cake">🎂 Birthday Cake - قوة: 300</option>
            <option value="Feather Mask">🎭 Feather Mask - قوة: 300</option>
            <option value="Forever Rosa">🌹 Forever Rosa - قوة: 399</option>
            <option value="Magic Rhythm">🎵 Magic Rhythm - قوة: 399</option>
            <option value="Relaxed Goose">🦢 Relaxed Goose - قوة: 399</option>
            <option value="Tom's Hug">🤗 Tom's Hug - قوة: 399</option>
            <option value="Rosie the Rose Bean">🌹 Rosie the Rose Bean - قوة: 399</option>
            <option value="Jollie the Joy Bean">😊 Jollie the Joy Bean - قوة: 399</option>
            <option value="Rocky the Rock Bean">🪨 Rocky the Rock Bean - قوة: 399</option>
            <option value="Sage the Smart Bean">🧠 Sage the Smart Bean - قوة: 399</option>
            <option value="Sage's Slash">⚔️ Sage's Slash - قوة: 399</option>
            <option value="Let butterfly dances">🦋 Let butterfly dances - قوة: 399</option>
            <option value="Kitten Kneading">🐱 Kitten Kneading - قوة: 399</option>
            <option value="Shoot the Apple">🍎 Shoot the Apple - قوة: 399</option>
            <option value="Alien Buddy">👽 Alien Buddy - قوة: 399</option>

            <!-- هدايا قوة 400-500 -->
            <option value="Crystal Dreams">💎 Crystal Dreams - قوة: 400</option>
            <option value="Wishing Cake">🎂 Wishing Cake - قوة: 400</option>
            <option value="Mic Champ">🎤 Mic Champ - قوة: 400</option>
            <option value="Beating Heart">💓 Beating Heart - قوة: 449</option>
            <option value="Fairy Mask">🧚 Fairy Mask - قوة: 450</option>
            <option value="Powerful Mind">🧠 Powerful Mind - قوة: 450</option>
            <option value="Hat of Joy">🎩 Hat of Joy - قوة: 450</option>
            <option value="Coral">🪸 Coral - قوة: 499</option>
            <option value="Hands Up">🙌 Hands Up - قوة: 499</option>
            <option value="Flower Show">🌺 Flower Show - قوة: 500</option>
            <option value="Money Gun">💰 Money Gun - قوة: 500</option>
          </select>
          <small style="color: #888; font-size: 11px; display: block; margin-top: 5px;">
            💡 تم إضافة جميع هدايا TikTok الحقيقية مع قيمها الصحيحة (133 هدية!)
          </small>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-success" onclick="saveNewCountry()">💾 حفظ الدولة</button>
        <button class="btn btn-secondary" onclick="closeModal()">❌ إلغاء</button>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // تركيز على حقل رمز الدولة
  setTimeout(() => {
    document.getElementById('newCountryCode').focus();
  }, 100);
}

/**
 * حفظ الدولة الجديدة
 */
function saveNewCountry() {
  const countryCode = document.getElementById('newCountryCode').value.trim().toUpperCase();
  const countryName = document.getElementById('newCountryName').value.trim();
  const countryFlag = document.getElementById('newCountryFlag').value.trim();
  const countryColor = document.getElementById('newCountryColor').value;
  const giftType = document.getElementById('newCountryGiftType').value;

  // التحقق من صحة البيانات
  if (!countryCode || countryCode.length !== 2) {
    showAlert('❌ يرجى إدخال رمز دولة صحيح (حرفان فقط)');
    return;
  }

  if (!countryName) {
    showAlert('❌ يرجى إدخال اسم الدولة');
    return;
  }

  if (!countryFlag) {
    showAlert('❌ يرجى إدخال إيموجي العلم');
    return;
  }

  // التحقق من عدم وجود الدولة مسبقاً
  if (defaultCountries[countryCode] || customCountries[countryCode]) {
    showAlert('❌ هذه الدولة موجودة بالفعل');
    return;
  }

  // إضافة الدولة الجديدة
  const newCountry = {
    name: countryName,
    flag: countryFlag,
    color: countryColor,
    isCustom: true
  };

  if (giftType) {
    newCountry.giftType = giftType;
  }

  // حفظ في الدول المخصصة
  customCountries[countryCode] = newCountry;

  // إضافة للدول الافتراضية مؤقتاً لعرضها
  defaultCountries[countryCode] = newCountry;

  // حفظ الإعدادات
  saveCustomCountries();

  // إعادة عرض قائمة الدول
  renderCustomCountries();

  // إغلاق النافذة
  closeModal();

  showAlert(`✅ تم إضافة ${countryName} بنجاح!`);
  console.log(`🆕 تم إضافة دولة جديدة: ${countryCode} - ${countryName}`);
}

/**
 * إغلاق النافذة المنبثقة
 */
function closeModal() {
  const modal = document.querySelector('.modal-overlay');
  if (modal) {
    modal.remove();
  }
}

/**
 * إعادة تعيين إعدادات الكاميرا
 */
function resetCameraSettings() {
  if (confirm('هل تريد إعادة تعيين إعدادات الكاميرا للقيم الافتراضية؟')) {
    // القيم الافتراضية
    const defaultCamera = {
      cameraControls: false,
      cameraX: 0,
      cameraY: 8,
      cameraZ: 15,
      cameraLookAtX: 0,
      cameraLookAtY: 0,
      cameraLookAtZ: 0
    };

    // تحديث الإعدادات
    currentSettings.cameraControls = defaultCamera.cameraControls;
    currentSettings.cameraX = defaultCamera.cameraX;
    currentSettings.cameraY = defaultCamera.cameraY;
    currentSettings.cameraZ = defaultCamera.cameraZ;
    currentSettings.cameraLookAtX = defaultCamera.cameraLookAtX;
    currentSettings.cameraLookAtY = defaultCamera.cameraLookAtY;
    currentSettings.cameraLookAtZ = defaultCamera.cameraLookAtZ;

    // تحديث الواجهة
    updateToggleSwitch(document.querySelector('[data-setting="cameraControls"]'), defaultCamera.cameraControls);

    const cameraXSlider = document.querySelector('[data-setting="cameraX"]');
    const cameraYSlider = document.querySelector('[data-setting="cameraY"]');
    const cameraZSlider = document.querySelector('[data-setting="cameraZ"]');
    const cameraLookAtXSlider = document.querySelector('[data-setting="cameraLookAtX"]');
    const cameraLookAtYSlider = document.querySelector('[data-setting="cameraLookAtY"]');
    const cameraLookAtZSlider = document.querySelector('[data-setting="cameraLookAtZ"]');

    if (cameraXSlider) {
      cameraXSlider.value = defaultCamera.cameraX;
      document.getElementById('cameraXValue').textContent = defaultCamera.cameraX;
    }

    if (cameraYSlider) {
      cameraYSlider.value = defaultCamera.cameraY;
      document.getElementById('cameraYValue').textContent = defaultCamera.cameraY;
    }

    if (cameraZSlider) {
      cameraZSlider.value = defaultCamera.cameraZ;
      document.getElementById('cameraZValue').textContent = defaultCamera.cameraZ;
    }

    if (cameraLookAtXSlider) {
      cameraLookAtXSlider.value = defaultCamera.cameraLookAtX;
      document.getElementById('cameraLookAtXValue').textContent = defaultCamera.cameraLookAtX;
    }

    if (cameraLookAtYSlider) {
      cameraLookAtYSlider.value = defaultCamera.cameraLookAtY;
      document.getElementById('cameraLookAtYValue').textContent = defaultCamera.cameraLookAtY;
    }

    if (cameraLookAtZSlider) {
      cameraLookAtZSlider.value = defaultCamera.cameraLookAtZ;
      document.getElementById('cameraLookAtZValue').textContent = defaultCamera.cameraLookAtZ;
    }

    showAlert('✅ تم إعادة تعيين إعدادات الكاميرا!');
  }
}

/**
 * تطبيق تغيير إعداد معين فوراً
 */
function applySettingChange(setting) {
  if (!window.opener || !window.opener.game) return;

  // إعدادات ترتيب المكعبات
  const cubeSettings = ['cubeArrangement', 'cubeSpacing', 'gridRows', 'gridCols'];
  if (cubeSettings.includes(setting)) {
    // تحديث الإعداد في اللعبة
    window.opener.game.gameSettings[setting] = currentSettings[setting];

    // تطبيق إعادة الترتيب
    if (window.opener.game.rearrangeCubes) {
      window.opener.game.rearrangeCubes();
      console.log(`🔄 تم تطبيق تغيير ${setting} فوراً`);
    }
  }

  // إعدادات الكاميرا (موضع واتجاه النظر)
  const cameraSettings = ['cameraX', 'cameraY', 'cameraZ', 'cameraLookAtX', 'cameraLookAtY', 'cameraLookAtZ', 'cameraControls'];
  if (cameraSettings.includes(setting)) {
    // تحديث الإعداد في اللعبة
    window.opener.game.gameSettings[setting] = currentSettings[setting];

    // تطبيق تحديث الكاميرا
    if (window.opener.game.updateCameraSettings) {
      window.opener.game.updateCameraSettings();
      console.log(`🎥 تم تطبيق تغيير ${setting} فوراً`);
    }
  }
}

console.log('🏁 تم تحميل إعدادات صراع الأعلام');
