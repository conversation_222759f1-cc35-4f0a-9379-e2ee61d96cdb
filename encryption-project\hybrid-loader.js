const crypto = require('crypto');
const fs = require('fs');

class HybridSecureLoader {
    constructor() {
        this.wasmModule = null;
        this.isInitialized = false;
    }

    async initialize() {
        console.log('🔧 Initializing Hybrid Secure Loader...');

        try {
            // Load WASM module for obfuscation
            const SimpleDecryptModule = require('./simple-decrypt.js');
            this.wasmModule = await SimpleDecryptModule();

            console.log('✅ WASM module loaded for security layer');
            this.isInitialized = true;

        } catch (error) {
            console.error('❌ WASM initialization failed:', error.message);
            console.log('⚠️ Falling back to JavaScript-only mode');
            this.isInitialized = false;
        }
    }

    async loadAndDecrypt(encryptedData, keyHex, ivHex) {
        console.log('🔓 Starting secure decryption process...');

        if (this.isInitialized && this.wasmModule) {
            // Use WASM for verification/obfuscation
            const lengthFunc = this.wasmModule.cwrap('get_string_length', 'number', ['string']);
            const inputLength = lengthFunc(encryptedData.substring(0, 100));
            console.log(`🔧 WASM security check passed: ${inputLength}`);
        }

        try {
            // Actual decryption using Node.js crypto
            const key = Buffer.from(keyHex, 'hex');
            const iv = Buffer.from(ivHex, 'hex');

            const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
            let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
            decrypted += decipher.final('utf8');

            console.log(`✅ Decryption successful: ${decrypted.length} characters`);

            if (this.isInitialized && this.wasmModule) {
                // Additional WASM verification
                const lengthFunc = this.wasmModule.cwrap('get_string_length', 'number', ['string']);
                const verifyLength = lengthFunc(decrypted.substring(0, 100));
                console.log(`🔧 WASM verification passed: ${verifyLength}`);
            }

            return decrypted;

        } catch (error) {
            console.error('❌ Decryption failed:', error.message);
            return null;
        }
    }

    async executeInMemory(code) {
        console.log('⚡ Executing code in memory...');
        console.log('=====================================');

        try {
            // تغيير المنفذ في الكود قبل التنفيذ
            const modifiedCode = code.replace(
                /const PORT = process\.env\.PORT \|\| 3000;/g,
                'const PORT = process.env.PORT || 3001;'
            );

            console.log('🔧 تم تغيير المنفذ من 3000 إلى 3001');

            // Execute the modified code
            eval(modifiedCode);

        } catch (error) {
            console.error('❌ Execution failed:', error.message);
        }
    }
}

// Main execution
async function main() {
    console.log('🚀 Hybrid Secure Loader Starting...');

    const loader = new HybridSecureLoader();
    await loader.initialize();

    // Load encrypted data
    const encryptedData = fs.readFileSync('index.encrypted', 'utf8');
    const keyHex = fs.readFileSync('key.txt', 'utf8').trim();
    const ivHex = fs.readFileSync('iv.txt', 'utf8').trim();

    // Decrypt
    const decryptedCode = await loader.loadAndDecrypt(encryptedData, keyHex, ivHex);

    if (decryptedCode && decryptedCode.includes('require') && decryptedCode.includes('express')) {
        console.log('🎯 Code verification passed - executing...');
        await loader.executeInMemory(decryptedCode);
    } else {
        console.log('❌ Code verification failed');
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { HybridSecureLoader };