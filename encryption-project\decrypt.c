#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <emscripten.h>

// Simple XOR decryption for testing (سنحسنه لاحقاً لـ AES)
EMSCRIPTEN_KEEPALIVE
char* simple_decrypt(const char* encrypted_data, const char* key) {
    if (!encrypted_data || !key) {
        return NULL;
    }
    
    int data_len = strlen(encrypted_data);
    int key_len = strlen(key);
    
    // Allocate memory for decrypted data
    char* decrypted = (char*)malloc(data_len + 1);
    if (!decrypted) {
        return NULL;
    }
    
    // Simple XOR decryption
    for (int i = 0; i < data_len; i++) {
        decrypted[i] = encrypted_data[i] ^ key[i % key_len];
    }
    decrypted[data_len] = '\0';
    
    return decrypted;
}

// Base64 decode function (مبسطة)
EMSCRIPTEN_KEEPALIVE
char* base64_decode(const char* encoded_data) {
    // For now, return as-is (سنضيف base64 decoding لاحقاً)
    int len = strlen(encoded_data);
    char* decoded = (char*)malloc(len + 1);
    strcpy(decoded, encoded_data);
    return decoded;
}

// Main decrypt function
EMSCRIPTEN_KEEPALIVE
char* decrypt_data(const char* encrypted_base64, const char* key_hex, const char* iv_hex) {
    printf("WASM: Starting decryption...\n");
    
    // For testing, we'll use simple decryption
    // Later we'll implement proper AES-256-CBC
    
    char* decoded = base64_decode(encrypted_base64);
    char* result = simple_decrypt(decoded, key_hex);
    
    free(decoded);
    
    printf("WASM: Decryption completed\n");
    return result;
}

// Free memory function
EMSCRIPTEN_KEEPALIVE
void free_memory(char* ptr) {
    if (ptr) {
        free(ptr);
    }
}