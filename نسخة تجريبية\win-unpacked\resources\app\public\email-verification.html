<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>تأكيد البريد الإلكتروني - StreamTok</title>
  <script src="/preload-theme.js"></script>
  <link rel="stylesheet" href="/dark-mode.css">
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    :root {
      color-scheme: light dark;
    }
    
    body {
      font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--bg-gradient);
      color: var(--text-color);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .verification-container {
      background: var(--section-bg);
      border-radius: 20px;
      padding: 40px;
      box-shadow: 0 20px 40px var(--shadow-color);
      width: 100%;
      max-width: 500px;
      margin: 20px;
      text-align: center;
    }

    .verification-icon {
      font-size: 4rem;
      margin-bottom: 20px;
      color: #ff3b5c;
    }

    .verification-title {
      color: var(--primary-color);
      font-size: 2rem;
      margin-bottom: 20px;
      font-weight: 700;
    }

    .verification-message {
      color: var(--text-secondary);
      font-size: 1.1rem;
      line-height: 1.6;
      margin-bottom: 30px;
    }

    .email-display {
      background: var(--input-bg);
      padding: 15px;
      border-radius: 10px;
      margin: 20px 0;
      font-weight: 600;
      color: var(--primary-color);
      border: 2px solid var(--border-color);
    }

    .verification-actions {
      display: flex;
      flex-direction: column;
      gap: 15px;
      margin-top: 30px;
    }

    .btn {
      padding: 15px 30px;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-block;
      text-align: center;
    }

    .btn-primary {
      background: var(--primary-gradient);
      color: white;
      box-shadow: 0 4px 10px rgba(255, 59, 92, 0.2);
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(255, 59, 92, 0.3);
    }

    .btn-secondary {
      background: var(--secondary-gradient);
      color: white;
    }

    .btn-secondary:hover {
      transform: translateY(-2px);
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .resend-info {
      margin-top: 20px;
      padding: 15px;
      background: rgba(255, 193, 7, 0.1);
      border: 1px solid rgba(255, 193, 7, 0.2);
      border-radius: 8px;
      color: #856404;
      font-size: 0.9rem;
    }

    .success-message {
      background: rgba(25, 135, 84, 0.1);
      color: #198754;
      padding: 12px;
      border-radius: 8px;
      margin: 20px 0;
      border: 1px solid rgba(25, 135, 84, 0.2);
      display: none;
    }

    .error-message {
      background: rgba(220, 53, 69, 0.1);
      color: #dc3545;
      padding: 12px;
      border-radius: 8px;
      margin: 20px 0;
      border: 1px solid rgba(220, 53, 69, 0.2);
      display: none;
    }

    .countdown {
      font-weight: bold;
      color: var(--primary-color);
    }

    .verification-steps {
      text-align: right;
      margin: 30px 0;
      padding: 20px;
      background: var(--input-bg);
      border-radius: 10px;
      border: 1px solid var(--border-color);
    }

    .verification-steps h4 {
      color: var(--primary-color);
      margin-bottom: 15px;
    }

    .verification-steps ol {
      margin: 0;
      padding-right: 20px;
    }

    .verification-steps li {
      margin-bottom: 10px;
      line-height: 1.5;
    }
  </style>
</head>
<body>
  <div class="verification-container">
    <div class="verification-icon">📧</div>
    <h1 class="verification-title">تأكيد البريد الإلكتروني</h1>
    
    <div class="verification-message">
      تم إرسال رسالة تأكيد إلى بريدك الإلكتروني
    </div>
    
    <div class="email-display" id="userEmail">
      <!-- سيتم عرض البريد الإلكتروني هنا -->
    </div>

    <div class="verification-steps">
      <h4>خطوات التأكيد:</h4>
      <ol>
        <li>تحقق من صندوق الوارد في بريدك الإلكتروني</li>
        <li>ابحث عن رسالة من StreamTok</li>
        <li>اضغط على رابط التأكيد في الرسالة</li>
        <li>ارجع إلى هذه الصفحة واضغط "تحقق من التأكيد"</li>
      </ol>
    </div>

    <div class="success-message" id="successMessage"></div>
    <div class="error-message" id="errorMessage"></div>

    <div class="verification-actions">
      <button class="btn btn-primary" id="checkVerificationBtn">
        تحقق من التأكيد
      </button>
      
      <button class="btn btn-secondary" id="resendBtn">
        إعادة إرسال رسالة التأكيد
      </button>
      
      <div class="resend-info" id="resendInfo" style="display: none;">
        يمكنك إعادة إرسال رسالة التأكيد بعد <span class="countdown" id="countdown">60</span> ثانية
      </div>
      
      <a href="/auth.html" class="btn btn-secondary">
        العودة لتسجيل الدخول
      </a>
    </div>
  </div>

  <script type="module" src="/js/firebase-config.js"></script>
  <script src="/dark-mode.js"></script>
  <script type="module">
    let resendCountdown = 0;
    let countdownInterval;
    let hasRedirected = false;

    window.addEventListener('load', () => {
      initializeVerificationPage();
    });

    function initializeVerificationPage() {
      // عرض البريد الإلكتروني للمستخدم الحالي
      window.addEventListener('authStateChanged', (event) => {
        if (event.detail.user) {
          document.getElementById('userEmail').textContent = event.detail.user.email;

          // إذا كان البريد مؤكد بالفعل، وجه للصفحة الرئيسية
          if (event.detail.user.emailVerified && !hasRedirected) {
            console.log('✅ Email already verified, redirecting to connection');
            hasRedirected = true;
            showSuccess('تم تأكيد البريد الإلكتروني بنجاح!');
            setTimeout(() => {
              window.location.href = '/connection.html';
            }, 1500);
          }
        } else {
          // إذا لم يكن هناك مستخدم، وجه لصفحة تسجيل الدخول
          if (!hasRedirected) {
            console.log('❌ No user, redirecting to auth');
            hasRedirected = true;
            window.location.href = '/auth.html';
          }
        }
      });

      // إضافة مستمعي الأحداث
      document.getElementById('checkVerificationBtn').addEventListener('click', checkEmailVerification);
      document.getElementById('resendBtn').addEventListener('click', resendVerificationEmail);
    }

    async function checkEmailVerification() {
      try {
        // إعادة تحميل بيانات المستخدم من الخادم
        await window.firebaseAuth.currentUser.reload();
        
        if (window.firebaseAuth.currentUser.emailVerified) {
          if (!hasRedirected) {
            hasRedirected = true;
            showSuccess('تم تأكيد البريد الإلكتروني بنجاح! جاري التوجيه...');
            setTimeout(() => {
              window.location.href = '/connection.html';
            }, 1500);
          }
        } else {
          showError('لم يتم تأكيد البريد الإلكتروني بعد. يرجى التحقق من بريدك الإلكتروني.');
        }
      } catch (error) {
        console.error('Error checking verification:', error);
        showError('حدث خطأ أثناء التحقق من تأكيد البريد الإلكتروني.');
      }
    }

    async function resendVerificationEmail() {
      if (resendCountdown > 0) return;

      try {
        const user = window.firebaseAuth.currentUser;
        if (user && !user.emailVerified) {
          await window.firebaseFunctions.sendEmailVerification(user);
          showSuccess('تم إرسال رسالة التأكيد مرة أخرى!');
          startResendCountdown();
        }
      } catch (error) {
        console.error('Error resending verification:', error);
        showError('حدث خطأ أثناء إرسال رسالة التأكيد.');
      }
    }

    function startResendCountdown() {
      resendCountdown = 60;
      const resendBtn = document.getElementById('resendBtn');
      const resendInfo = document.getElementById('resendInfo');
      const countdownSpan = document.getElementById('countdown');
      
      resendBtn.disabled = true;
      resendInfo.style.display = 'block';
      
      countdownInterval = setInterval(() => {
        resendCountdown--;
        countdownSpan.textContent = resendCountdown;
        
        if (resendCountdown <= 0) {
          clearInterval(countdownInterval);
          resendBtn.disabled = false;
          resendInfo.style.display = 'none';
        }
      }, 1000);
    }

    function showSuccess(message) {
      const successDiv = document.getElementById('successMessage');
      successDiv.textContent = message;
      successDiv.style.display = 'block';
      
      setTimeout(() => {
        successDiv.style.display = 'none';
      }, 5000);
    }

    function showError(message) {
      const errorDiv = document.getElementById('errorMessage');
      errorDiv.textContent = message;
      errorDiv.style.display = 'block';
      
      setTimeout(() => {
        errorDiv.style.display = 'none';
      }, 5000);
    }
  </script>
</body>
</html>
