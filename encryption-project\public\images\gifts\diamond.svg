<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <defs>
    <linearGradient id="diamondGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f4fd;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#74b9ff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0984e3;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#74b9ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e8f4fd;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="sparkle" cx="50%" cy="30%" r="40%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </radialGradient>
    <filter id="diamondGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- الماسة الرئيسية -->
  <g filter="url(#diamondGlow)">
    <!-- الجزء العلوي -->
    <polygon points="50,15 35,35 65,35" fill="url(#diamondGradient)" stroke="#0984e3" stroke-width="1"/>
    <!-- الجزء السفلي -->
    <polygon points="35,35 50,75 65,35" fill="url(#diamondGradient)" stroke="#0984e3" stroke-width="1"/>
    
    <!-- الوجوه الجانبية -->
    <polygon points="35,35 25,45 50,75" fill="#74b9ff" opacity="0.8"/>
    <polygon points="65,35 75,45 50,75" fill="#74b9ff" opacity="0.8"/>
    
    <!-- الوجه العلوي -->
    <polygon points="50,15 35,35 50,25 65,35" fill="url(#sparkle)" opacity="0.6"/>
  </g>
  
  <!-- خطوط اللمعان -->
  <g opacity="0.8">
    <line x1="50" y1="10" x2="50" y2="25" stroke="#ffffff" stroke-width="2" stroke-linecap="round"/>
    <line x1="42" y1="18" x2="58" y2="18" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round"/>
    <line x1="45" y1="30" x2="55" y2="30" stroke="#ffffff" stroke-width="1" stroke-linecap="round"/>
  </g>
  
  <!-- شرارات حول الماسة -->
  <g opacity="0.7">
    <polygon points="20,25 22,27 20,29 18,27" fill="#ffffff"/>
    <polygon points="80,30 82,32 80,34 78,32" fill="#ffffff"/>
    <polygon points="25,60 27,62 25,64 23,62" fill="#ffffff"/>
    <polygon points="75,55 77,57 75,59 73,57" fill="#ffffff"/>
    <polygon points="45,10 47,12 45,14 43,12" fill="#ffffff"/>
    <polygon points="55,80 57,82 55,84 53,82" fill="#ffffff"/>
  </g>
</svg>
