<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لعبة سباق السيارات</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: #222;
            font-family: Arial, sans-serif;
        }

        #gameContainer {
            position: relative;
            width: 1000px;
            height: 100vh;
            background: linear-gradient(to bottom, #1a1a4a, #2a2a5a);
            border: 3px solid #00ff00;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
        }

        #gameCanvas {
            display: block;
            width: 100%;
            height: 100%;
        }

        #score {
            position: absolute;
            top: 10px;
            right: 10px;
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        #gameOver {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            display: none;
        }

        #gameOver h2 {
            color: #ff0000;
            font-size: 36px;
            margin: 0 0 20px 0;
        }

        #gameOver p {
            color: white;
            font-size: 20px;
            margin: 10px 0;
        }

        #restartBtn {
            background: #00ff00;
            color: black;
            border: none;
            padding: 15px 30px;
            font-size: 20px;
            font-weight: bold;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s;
        }

        #restartBtn:hover {
            background: #00cc00;
            transform: scale(1.1);
        }

        #startScreen {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            padding: 40px;
            border-radius: 10px;
            text-align: center;
        }

        #startScreen h1 {
            color: #00ff00;
            font-size: 40px;
            margin: 0 0 30px 0;
            text-shadow: 2px 2px 4px rgba(0,255,0,0.5);
        }

        #startBtn {
            background: #00ff00;
            color: black;
            border: none;
            padding: 20px 40px;
            font-size: 24px;
            font-weight: bold;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }

        #startBtn:hover {
            background: #00cc00;
            transform: scale(1.1);
        }

        .controls {
            color: white;
            margin-top: 20px;
            font-size: 16px;
        }
        
        #settingsBtn {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: 2px solid #00ff00;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s;
            z-index: 100;
        }
        
        #settingsBtn:hover {
            background: rgba(0, 255, 0, 0.2);
        }
        
        #settingsPanel {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.95);
            border: 2px solid #00ff00;
            border-radius: 10px;
            padding: 30px;
            display: none;
            z-index: 200;
            min-width: 400px;
        }
        
        #settingsPanel h2 {
            color: #00ff00;
            text-align: center;
            margin-bottom: 20px;
            font-size: 28px;
        }
        
        .setting-item {
            margin: 15px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: white;
        }
        
        .setting-label {
            flex: 1;
            font-size: 16px;
        }
        
        .volume-slider {
            width: 200px;
            margin: 0 10px;
        }
        
        .volume-value {
            width: 40px;
            text-align: center;
            color: #00ff00;
            font-weight: bold;
        }
        
        input[type="range"] {
            -webkit-appearance: none;
            appearance: none;
            height: 8px;
            background: #333;
            border-radius: 4px;
            outline: none;
        }
        
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: #00ff00;
            border-radius: 50%;
            cursor: pointer;
        }
        
        input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: #00ff00;
            border-radius: 50%;
            cursor: pointer;
        }
        
        .settings-buttons {
            display: flex;
            justify-content: space-around;
            margin-top: 30px;
        }
        
        .settings-buttons button {
            background: #00ff00;
            color: black;
            border: none;
            padding: 10px 30px;
            font-size: 16px;
            font-weight: bold;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .settings-buttons button:hover {
            background: #00cc00;
            transform: scale(1.05);
        }
        
        #closeSettings {
            background: #ff3333 !important;
            color: white !important;
        }
        
        #closeSettings:hover {
            background: #cc0000 !important;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        <button id="settingsBtn">⚙️ الإعدادات</button>
        
        <div id="startScreen">
            <h1>🏎️ لعبة سباق السيارات 🏎️</h1>
            <button id="startBtn">ابدأ اللعب</button>
            <div class="controls">
                <h3>مفاتيح التحكم لكل لاعب:</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; text-align: right; margin-top: 20px;">
                    <div>
                        <p style="color: #00ff00;">اللاعب 1: ↑ تسريع، ↓ فرملة</p>
                        <p style="color: #ff0000;">اللاعب 2: W تسريع، S فرملة</p>
                        <p style="color: #0066ff;">اللاعب 3: E تسريع، D فرملة</p>
                        <p style="color: #ffff00;">اللاعب 4: R تسريع، F فرملة</p>
                    </div>
                    <div>
                        <p style="color: #00ffff;">اللاعب 5: T تسريع، G فرملة</p>
                        <p style="color: #ff8800;">اللاعب 6: Y تسريع، H فرملة</p>
                        <p style="color: #ff00ff;">اللاعب 7: U تسريع، J فرملة</p>
                    </div>
                </div>
                <p style="margin-top: 20px; font-size: 18px; color: #ffff00;">جميع السيارات تبدأ من نفس الخط!</p>
            </div>
        </div>
        
        <div id="gameOver">
            <h2>انتهت اللعبة!</h2>
            <p id="finalScore"></p>
            <button id="restartBtn">العب مرة أخرى</button>
        </div>
        
        <div id="settingsPanel">
            <h2>🎮 إعدادات اللعبة</h2>
            
            <div class="setting-item">
                <span class="setting-label">🔊 الصوت الرئيسي</span>
                <input type="range" id="masterVolume" class="volume-slider" min="0" max="100" value="50">
                <span class="volume-value" id="masterValue">50%</span>
            </div>
            
            <div class="setting-item">
                <span class="setting-label">🚗 صوت المحرك</span>
                <input type="range" id="engineVolume" class="volume-slider" min="0" max="100" value="100">
                <span class="volume-value" id="engineValue">100%</span>
            </div>
            
            <div class="setting-item">
                <span class="setting-label">🌧️ صوت المطر</span>
                <input type="range" id="rainVolume" class="volume-slider" min="0" max="100" value="100">
                <span class="volume-value" id="rainValue">100%</span>
            </div>
            
            <div class="setting-item">
                <span class="setting-label">💨 صوت الرياح</span>
                <input type="range" id="windVolume" class="volume-slider" min="0" max="100" value="100">
                <span class="volume-value" id="windValue">100%</span>
            </div>
            
            <div class="setting-item">
                <span class="setting-label">⚡ صوت الرعد</span>
                <input type="range" id="thunderVolume" class="volume-slider" min="0" max="100" value="100">
                <span class="volume-value" id="thunderValue">100%</span>
            </div>
            
            <div class="setting-item">
                <span class="setting-label">💥 صوت التصادم</span>
                <input type="range" id="collisionVolume" class="volume-slider" min="0" max="100" value="100">
                <span class="volume-value" id="collisionValue">100%</span>
            </div>
            
            <div class="setting-item">
                <span class="setting-label">🚦 أصوات الشارع</span>
                <input type="range" id="streetVolume" class="volume-slider" min="0" max="100" value="100">
                <span class="volume-value" id="streetValue">100%</span>
            </div>
            
            <div class="settings-buttons">
                <button id="resetSettings">إعادة تعيين</button>
                <button id="closeSettings">إغلاق</button>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // تعيين حجم الكانفاس ليوافق الارتفاع الكامل
        function resizeCanvas() {
            canvas.width = 1000;
            canvas.height = window.innerHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        // const scoreElement = document.getElementById('score'); // تم إلغاء النقاط
        const gameOverElement = document.getElementById('gameOver');
        const finalScoreElement = document.getElementById('finalScore');
        const restartBtn = document.getElementById('restartBtn');
        const startScreen = document.getElementById('startScreen');
        const startBtn = document.getElementById('startBtn');
        const settingsBtn = document.getElementById('settingsBtn');
        const settingsPanel = document.getElementById('settingsPanel');
        const closeSettingsBtn = document.getElementById('closeSettings');
        const resetSettingsBtn = document.getElementById('resetSettings');

        // Game variables
        let gameRunning = false;
        let score = 0;
        let speed = 3;
        let roadOffset = 0;
        let racePosition = 5; // موقع اللاعب في السباق
        const totalRacers = 7; // إجمالي عدد المتسابقين (تم تقليلهم إلى 7)
        let roadType = 0; // نوع الطريق الحالي
        let roadTimer = 0; // مؤقت تغيير الطريق
        let roadTransitionY = -1; // موضع الانتقال (-1 يعني لا يوجد انتقال)
        let nextRoadType = 0; // نوع الطريق التالي
        // تم إزالة cameraOffset لأننا نستخدم نظام كاميرا جديد
        
        // Weather system
        const weatherTypes = ['sunny', 'rain', 'snow', 'autumn', 'night', 'storm'];
        let currentWeather = 'sunny';
        let weatherIntensity = 0;
        let weatherTimer = 0;
        let particles = [];
        let lightning = { active: false, timer: 0 };
        
        // Sound system
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        let engineSound = null;
        let rainSound = null;
        let thunderSound = null;
        let windSound = null;
        let collisionSound = null;
        let streetSound = null;
        let sounds = {
            engineRunning: false,
            rainPlaying: false,
            windPlaying: false,
            streetPlaying: false
        };
        
        // Volume settings
        let volumeSettings = {
            master: 0.5,
            engine: 1.0,
            rain: 1.0,
            wind: 1.0,
            thunder: 1.0,
            collision: 1.0,
            street: 1.0
        };
        
        // Master gain node
        const masterGain = audioContext.createGain();
        masterGain.gain.value = volumeSettings.master;
        masterGain.connect(audioContext.destination);

        // Lane positions - 7 lanes only
        const lanes = [100, 220, 340, 460, 580, 700, 820];
        const laneWidth = 100;

        // Player car
        const playerCar = {
            x: lanes[0], // المسار الأول
            y: canvas.height - 220, // رفع موضع السيارة قليلاً لإتاحة مساحة للمرآة
            width: 60,
            height: 100,
            speed: 0, // السرعة تبدأ من صفر
            lane: 0,
            distance: 0,
            color: '#00ff00',
            name: 'اللاعب 1',
            keys: { up: 'arrowup', down: 'arrowdown' }, // مفاتيح التحكم
            screenY: canvas.height - 220 // الموضع الفعلي على الشاشة
        };

        // Racing cars (now controlled by players)
        let racingCars = [];
        const racerColors = ['#ff0000', '#0066ff', '#ffff00', '#ff00ff', '#00ffff', '#ff8800'];
        const racerKeys = [
            { up: 'w', down: 's' },      // السيارة الثانية
            { up: 'e', down: 'd' },      // السيارة الثالثة
            { up: 'r', down: 'f' },      // السيارة الرابعة
            { up: 't', down: 'g' },      // السيارة الخامسة
            { up: 'y', down: 'h' },      // السيارة السادسة
            { up: 'u', down: 'j' }       // السيارة السابعة
        ];

        // Controls
        const keys = {};

        // Event listeners
        document.addEventListener('keydown', (e) => {
            keys[e.key.toLowerCase()] = true;
        });

        document.addEventListener('keyup', (e) => {
            keys[e.key.toLowerCase()] = false;
        });

        startBtn.addEventListener('click', startGame);
        restartBtn.addEventListener('click', restartGame);
        settingsBtn.addEventListener('click', openSettings);
        closeSettingsBtn.addEventListener('click', closeSettings);
        resetSettingsBtn.addEventListener('click', resetSettings);
        
        // عرض مفاتيح التحكم
        function showControlsInfo() {
            const controlsInfo = `
                <h3>مفاتيح التحكم:</h3>
                <p>اللاعب 1 (أخضر): ↑ للتسريع، ↓ للفرملة</p>
                <p>اللاعب 2 (أحمر): W للتسريع، S للفرملة</p>
                <p>اللاعب 3 (أزرق): E للتسريع، D للفرملة</p>
                <p>اللاعب 4 (أصفر): R للتسريع، F للفرملة</p>
                <p>اللاعب 5 (سماوي): T للتسريع، G للفرملة</p>
                <p>اللاعب 6 (برتقالي): Y للتسريع، H للفرملة</p>
                <p>اللاعب 7 (بنفسجي): U للتسريع، J للفرملة</p>
            `;
            return controlsInfo;
        }
        
        
        // Initialize audio context on user interaction
        document.addEventListener('click', () => {
            if (audioContext.state === 'suspended') {
                audioContext.resume();
            }
        }, { once: true });
        
        // قار الوصول
        function getLeadCar() {
            const allCars = [playerCar, ...racingCars];
            allCars.sort((a, b) => b.distance - a.distance);
            return allCars[0];
        }
        
        // Volume sliders
        initializeVolumeControls();

        function startGame() {
            startScreen.style.display = 'none';
            initializeRacers();
            initializeSounds();
            gameRunning = true;
            gameLoop();
        }

        function initializeRacers() {
            racingCars = [];
            // إنشاء السيارات المتسابقة - كل سيارة في مسار منفصل
            for (let i = 0; i < totalRacers - 1; i++) {
                const lane = i + 1; // المسارات من 1 إلى 6 (المسار 0 للاعب الرئيسي)
                
                racingCars.push({
                    x: lanes[lane],
                    y: canvas.height - 220, // جميع السيارات في نفس المستوى
                    width: 60,
                    height: 100,
                    speed: 0, // جميع السيارات تبدأ بسرعة صفر
                    color: racerColors[i],
                    lane: lane,
                    distance: 0, // جميع السيارات تبدأ من نفس المسافة
                    name: `اللاعب ${i + 2}`,
                    keys: racerKeys[i], // مفاتيح التحكم لكل سيارة
                    screenY: canvas.height - 220 // الموضع الفعلي على الشاشة
                });
            }
        }

        function restartGame() {
            score = 0;
            speed = 3;
            racingCars = [];
            roadType = 0;
            roadTimer = 0;
            roadTransitionY = -1;
            nextRoadType = 0;
            playerCar.x = lanes[0];
            playerCar.lane = 0;
            playerCar.distance = 0;
            playerCar.speed = 0;
            playerCar.screenY = canvas.height - 220;
            racePosition = 5;
            gameOverElement.style.display = 'none';
            initializeRacers();
            gameRunning = true;
            gameLoop();
        }

        // دالة رسم المرآة الخلفية
        function drawRearviewMirror() {
            const mirrorWidth = 800;
            const mirrorHeight = 100;
            const mirrorX = (canvas.width - mirrorWidth) / 2;
            const mirrorY = canvas.height - mirrorHeight - 10;
            
            // إطار المرآة
            ctx.fillStyle = '#1a1a1a';
            ctx.fillRect(mirrorX - 5, mirrorY - 5, mirrorWidth + 10, mirrorHeight + 10);
            
            // خلفية المرآة
            ctx.fillStyle = '#2a2a2a';
            ctx.fillRect(mirrorX, mirrorY, mirrorWidth, mirrorHeight);
            
            // رسم المسارات في المرآة
            ctx.strokeStyle = '#444444';
            ctx.lineWidth = 1;
            for (let i = 0; i <= 7; i++) {
                const laneX = mirrorX + (i * mirrorWidth / 7);
                ctx.beginPath();
                ctx.moveTo(laneX, mirrorY);
                ctx.lineTo(laneX, mirrorY + mirrorHeight);
                ctx.stroke();
            }
            
            // جمع جميع السيارات وترتيبها حسب المسافة
            const allCars = [playerCar, ...racingCars];
            allCars.sort((a, b) => b.distance - a.distance);
            
            // رسم مواقع السيارات
            for (let car of allCars) {
                const carLane = car.lane;
                const carX = mirrorX + (carLane * mirrorWidth / 7) + (mirrorWidth / 14);
                const carRelativeDistance = car.distance - playerCar.distance;
                
                // حجم السيارة بناءً على المسافة
                let carSize = 20;
                if (carRelativeDistance > 0) {
                    // السيارات الأمامية (المتقدمة)
                    carSize = Math.min(30, 20 + (carRelativeDistance / 50));
                } else {
                    // السيارات الخلفية (المتأخرة)
                    carSize = Math.max(5, 20 - Math.abs(carRelativeDistance / 100));
                }
                
                // موضع السيارة في المرآة
                let carY = mirrorY + mirrorHeight / 2;
                if (carRelativeDistance > 0) {
                    // السيارات الأمامية (المتقدمة) تظهر في أسفل المرآة
                    carY = mirrorY + mirrorHeight - 20 - Math.min(40, carRelativeDistance / 20);
                } else if (carRelativeDistance < -500) {
                    // السيارات الخلفية البعيدة تظهر في أعلى المرآة
                    carY = mirrorY + 20;
                } else {
                    // السيارات القريبة
                    carY = mirrorY + mirrorHeight / 2 - (carRelativeDistance / 10);
                }
                
                // رسم السيارة
                ctx.fillStyle = car.color;
                ctx.fillRect(carX - carSize/2, carY - carSize/2, carSize, carSize);
                
                // رسم اسم السيارة
                ctx.fillStyle = '#ffffff';
                ctx.font = '10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(car.name, carX, carY - carSize/2 - 5);
                
                // إشارة المسافة
                if (car !== playerCar) {
                    const distanceText = carRelativeDistance > 0 ? 
                        `+${Math.floor(carRelativeDistance / 100)}م أمامك` : 
                        `${Math.floor(Math.abs(carRelativeDistance) / 100)}م خلفك`;
                    ctx.fillText(distanceText, carX, carY + carSize/2 + 12);
                }
            }
            
            // عنوان المرآة
            ctx.fillStyle = '#00ff00';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('المرآة الخلفية - مواقع جميع السيارات', canvas.width / 2, mirrorY - 10);
            ctx.textAlign = 'left';
            
            // إضافة تأثير انعكاس على المرآة
            const gradient = ctx.createLinearGradient(mirrorX, mirrorY, mirrorX, mirrorY + mirrorHeight);
            gradient.addColorStop(0, 'rgba(255, 255, 255, 0.1)');
            gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.05)');
            gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
            ctx.fillStyle = gradient;
            ctx.fillRect(mirrorX, mirrorY, mirrorWidth, mirrorHeight);
        }

function drawRoad(leadCar, cameraTargetY) {
            // Background based on weather
            drawBackground();
            
            // تحديث سرعة الطريق بناءً على السيارة الأولى
            // تحسين حركة الطريق لتتناسب مع السيارة الأسرع
            speed = Math.max(3, leadCar.speed * 0.8);
            
            // Update road type timer
            roadTimer++;
            if (roadTimer > 2400 && roadTransitionY === -1) { // تغيير نوع الطريق بعد 40 ثانية
                roadTimer = 0;
                nextRoadType = (roadType + 1) % 3;
                roadTransitionY = 0; // بدء الانتقال من أعلى الشاشة
            }
            
            // Update transition if active
            if (roadTransitionY !== -1) {
                roadTransitionY += speed;
                if (roadTransitionY >= canvas.height) {
                    roadType = nextRoadType;
                    roadTransitionY = -1; // إنهاء الانتقال
                }
            }
            
            // رسم المناظر الطبيعية في الخلفية أولاً
            drawScenery();

            // Road surface with texture
            let roadColor = '#2a2a2a';
            let roadTexture = null;
            switch(roadType) {
                case 0: // طريق عادي
                    roadColor = currentWeather === 'snow' ? '#e8e8e8' : '#2a2a2a';
                    break;
                case 1: // طريق غابات خضراء جميلة
                    roadColor = currentWeather === 'snow' ? '#c5d4c5' : '#2a4a2a';
                    // إضافة نسيج عشبي على جوانب الطريق
                    roadTexture = 'forest';
                    break;
                case 2: // طريق المدينة الحديثة
                    roadColor = currentWeather === 'snow' ? '#e0e0e0' : '#1a1a1a';
                    // طريق أكثر قتامة للمدينة
                    roadTexture = 'city';
                    break;
            }
            ctx.fillStyle = roadColor;
            ctx.fillRect(20, 0, canvas.width - 40, canvas.height);
            
            // إضافة تفاصيل إضافية للطرق
            if (roadTexture === 'forest') {
                // إضافة عشب ونباتات على جوانب الطريق
                for (let i = 0; i < 15; i++) {
                    const y = i * 50 + (roadOffset % 50);
                    // عشب على اليسار
                    ctx.fillStyle = '#2d5016';
                    ctx.fillRect(25, y, 15, 20);
                    ctx.fillStyle = '#3a6b1a';
                    ctx.fillRect(30, y + 5, 10, 10);
                    
                    // عشب على اليمين
                    ctx.fillStyle = '#2d5016';
                    ctx.fillRect(canvas.width - 40, y, 15, 20);
                    ctx.fillStyle = '#3a6b1a';
                    ctx.fillRect(canvas.width - 40, y + 5, 10, 10);
                }
                
                // إضافة أوراق متساقطة
                for (let i = 0; i < 5; i++) {
                    const leafY = (roadOffset * 2 + i * 100) % (canvas.height + 100) - 50;
                    const leafX = 50 + Math.sin(leafY * 0.01) * 20;
                    ctx.fillStyle = '#ff9500';
                    ctx.save();
                    ctx.translate(leafX, leafY);
                    ctx.rotate(leafY * 0.02);
                    ctx.beginPath();
                    ctx.ellipse(0, 0, 15, 10, 0, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                }
            } else if (roadTexture === 'city') {
                // إضافة خطوط ممر المشاة
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = 10;
                ctx.setLineDash([30, 10]);
                
                // ممر مشاة يسار
                for (let i = 0; i < 3; i++) {
                    const y = 150 + i * 200 + (roadOffset % 200);
                    ctx.beginPath();
                    ctx.moveTo(45, y);
                    ctx.lineTo(45, y + 40);
                    ctx.stroke();
                }
                
                // ممر مشاة يمين
                for (let i = 0; i < 3; i++) {
                    const y = 150 + i * 200 + (roadOffset % 200);
                    ctx.beginPath();
                    ctx.moveTo(canvas.width - 45, y);
                    ctx.lineTo(canvas.width - 45, y + 40);
                    ctx.stroke();
                }
                
                ctx.setLineDash([]);
                
                // إضافة فتحات صرف صحي
                ctx.fillStyle = '#1a1a1a';
                for (let i = 0; i < 4; i++) {
                    const y = 100 + i * 150 + (roadOffset * 0.5 % 150);
                    ctx.fillRect(200, y, 30, 20);
                    ctx.fillRect(canvas.width - 230, y, 30, 20);
                    
                    // شبكة على الفتحة
                    ctx.strokeStyle = '#444444';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(200, y);
                    ctx.lineTo(230, y);
                    ctx.moveTo(200, y + 10);
                    ctx.lineTo(230, y + 10);
                    ctx.moveTo(200, y + 20);
                    ctx.lineTo(230, y + 20);
                    ctx.stroke();
                }
            }
            
            // Draw transitioning road if active
            if (roadTransitionY !== -1) {
                let nextRoadColor = '#2a2a2a';
                switch(nextRoadType) {
                    case 0: // طريق عادي
                        nextRoadColor = currentWeather === 'snow' ? '#e8e8e8' : '#2a2a2a';
                        break;
                    case 1: // طريق غابات خضراء جميلة
                        nextRoadColor = currentWeather === 'snow' ? '#d4e6d4' : '#3a3a3a';
                        break;
                    case 2: // طريق المدينة الحديثة
                        nextRoadColor = currentWeather === 'snow' ? '#e0e0e0' : '#2f2f2f';
                        break;
                }
                ctx.fillStyle = nextRoadColor;
                ctx.fillRect(20, 0, canvas.width - 40, roadTransitionY);
            }

            // Road edges with racing stripes
            ctx.fillStyle = '#ff0000';
            ctx.fillRect(0, 0, 20, canvas.height);
            ctx.fillRect(canvas.width - 20, 0, 20, canvas.height);
            
            // White edge lines
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(20, 0, 5, canvas.height);
            ctx.fillRect(canvas.width - 25, 0, 5, canvas.height);

            // Road lines
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 3;
            ctx.setLineDash([40, 20]);
            ctx.lineDashOffset = -roadOffset;

            // Draw lane dividers
            for (let i = 1; i < lanes.length; i++) {
                ctx.beginPath();
                ctx.moveTo(lanes[i] - laneWidth/2 + 30, 0);
                ctx.lineTo(lanes[i] - laneWidth/2 + 30, canvas.height);
                ctx.stroke();
            }

            // رسم خط البداية
            const startLineDistance = 0;
            const startLineY = cameraTargetY + (leadCar.distance * 0.5);
            
            if (startLineY > -100 && startLineY < canvas.height && leadCar.distance < 1000) {
                // رسم خط البداية بنمط الشطرنج
                const squareSize = 20;
                for (let x = 20; x < canvas.width - 20; x += squareSize) {
                    for (let y = startLineY; y < startLineY + 40; y += squareSize) {
                        if ((Math.floor((x - 20) / squareSize) + Math.floor((y - startLineY) / squareSize)) % 2 === 0) {
                            ctx.fillStyle = '#ffffff';
                        } else {
                            ctx.fillStyle = '#000000';
                        }
                        ctx.fillRect(x, y, squareSize, squareSize);
                    }
                }
                
                // علامة START
                ctx.fillStyle = '#00ff00';
                ctx.fillRect(20, startLineY - 40, canvas.width - 40, 40);
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 30px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('خط البداية - START', canvas.width / 2, startLineY - 10);
                ctx.textAlign = 'left';
            }
            
            // رسم خط النهاية
            const finishLineDistance = 50000; // المسافة إلى خط النهاية (500 متر)
            const finishLineY = cameraTargetY + ((finishLineDistance - leadCar.distance) * 0.5);
            
            if (finishLineY > -100 && finishLineY < canvas.height + 100) {
                // رسم خط النهاية بنمط الشطرنج
                const squareSize = 20;
                for (let x = 20; x < canvas.width - 20; x += squareSize) {
                    for (let y = finishLineY; y < finishLineY + 60; y += squareSize) {
                        if ((Math.floor((x - 20) / squareSize) + Math.floor((y - finishLineY) / squareSize)) % 2 === 0) {
                            ctx.fillStyle = '#ffffff';
                        } else {
                            ctx.fillStyle = '#000000';
                        }
                        ctx.fillRect(x, y, squareSize, squareSize);
                    }
                }
                
                // علامة FINISH
                ctx.fillStyle = '#ff0000';
                ctx.fillRect(20, finishLineY - 40, canvas.width - 40, 40);
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 30px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('خط النهاية - FINISH', canvas.width / 2, finishLineY - 10);
                ctx.textAlign = 'left';
            }

            roadOffset += speed;
            if (roadOffset > 60) roadOffset = 0;
            
            // رسم عواميد الإنارة
            drawStreetLights();
        }
        
        // دالة رسم المناظر الطبيعية حسب نوع الطريق
        function drawScenery() {
            switch(roadType) {
                case 0:
                    // الطريق الأول - نتركه كما هو بدون مناظر إضافية
                    break;
                case 1:
                    // طريق الغابات الخضراء الجميلة
                    drawForestScenery();
                    break;
                case 2:
                    // طريق المدينة الحديثة
                    drawCityScenery();
                    break;
            }
        }
        
        // رسم مناظر الغابات الخضراء
        function drawForestScenery() {
            // رسم الأشجار على جانبي الطريق
            const treeSpacing = 120;
            const offset = (roadOffset * 0.8) % treeSpacing;
            
            for (let y = -200 - offset; y < canvas.height + 200; y += treeSpacing) {
                // أشجار على اليسار
                drawTree(-60, y, 'left');
                drawTree(-120, y + 40, 'left');
                drawTree(-90, y + 80, 'left');
                
                // أشجار على اليمين
                drawTree(canvas.width - 80, y, 'right');
                drawTree(canvas.width - 140, y + 40, 'right');
                drawTree(canvas.width - 110, y + 80, 'right');
            }
            
            // رسم الجبال في الخلفية
            drawMountains();
            
            // رسم الزهور والنباتات
            drawFlowers();
        }
        
        // رسم المدينة الحديثة
        function drawCityScenery() {
            // رسم ناطحات السحاب في الخلفية أولاً
            drawSkyscrapers();
            
            // رسم المباني على جانبي الطريق
            const buildingSpacing = 150;
            const offset = (roadOffset * 0.5) % buildingSpacing;
            
            for (let y = -300 - offset; y < canvas.height + 300; y += buildingSpacing) {
                // مباني على اليسار
                drawBuilding(-140, y, 100, 250, 'left');
                drawBuilding(-240, y + 80, 80, 180, 'left');
                drawBuilding(-320, y + 40, 60, 200, 'left');
                
                // مباني على اليمين
                drawBuilding(canvas.width - 60, y, 100, 250, 'right');
                drawBuilding(canvas.width + 40, y + 80, 80, 180, 'right');
                drawBuilding(canvas.width + 120, y + 40, 60, 200, 'right');
            }
            
            // إضافة إعلانات ولوحات
            drawCityDetails();
        }
        
        // دالة رسم الأشجار
        function drawTree(x, y, side) {
            // جذع الشجرة
            ctx.fillStyle = '#4a2c17';
            ctx.fillRect(x + 35, y + 70, 30, 60);
            
            // ظل الجذع
            ctx.fillStyle = '#3a1c07';
            ctx.fillRect(x + 40, y + 70, 25, 60);
            
            // أوراق الشجرة - طبقات متعددة
            ctx.fillStyle = '#0d5d0d';
            ctx.beginPath();
            ctx.arc(x + 50, y + 20, 50, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = '#1a7a1a';
            ctx.beginPath();
            ctx.arc(x + 30, y + 40, 45, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = '#248f24';
            ctx.beginPath();
            ctx.arc(x + 70, y + 40, 45, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = '#2eb82e';
            ctx.beginPath();
            ctx.arc(x + 50, y + 50, 40, 0, Math.PI * 2);
            ctx.fill();
            
            // ظل الشجرة
            ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
            ctx.beginPath();
            ctx.ellipse(x + 50, y + 130, 60, 15, 0, 0, Math.PI * 2);
            ctx.fill();
        }
        
        // دالة رسم الجبال
        function drawMountains() {
            ctx.save();
            
            // جبال بعيدة
            ctx.fillStyle = 'rgba(100, 100, 120, 0.5)';
            ctx.beginPath();
            ctx.moveTo(0, 300);
            ctx.lineTo(200, 150);
            ctx.lineTo(400, 250);
            ctx.lineTo(600, 100);
            ctx.lineTo(800, 200);
            ctx.lineTo(1000, 150);
            ctx.lineTo(1000, 300);
            ctx.closePath();
            ctx.fill();
            
            // جبال قريبة
            ctx.fillStyle = 'rgba(80, 80, 100, 0.7)';
            ctx.beginPath();
            ctx.moveTo(0, 350);
            ctx.lineTo(300, 200);
            ctx.lineTo(500, 280);
            ctx.lineTo(700, 180);
            ctx.lineTo(900, 250);
            ctx.lineTo(1000, 220);
            ctx.lineTo(1000, 350);
            ctx.closePath();
            ctx.fill();
            
            // ثلوج على قمم الجبال
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.beginPath();
            ctx.moveTo(180, 170);
            ctx.lineTo(200, 150);
            ctx.lineTo(220, 170);
            ctx.closePath();
            ctx.fill();
            
            ctx.beginPath();
            ctx.moveTo(580, 120);
            ctx.lineTo(600, 100);
            ctx.lineTo(620, 120);
            ctx.closePath();
            ctx.fill();
            
            ctx.restore();
        }
        
        // دالة رسم الزهور
        function drawFlowers() {
            const flowerPositions = [
                {x: 50, y: 500}, {x: 100, y: 600}, {x: 150, y: 550},
                {x: 850, y: 500}, {x: 900, y: 600}, {x: 950, y: 550}
            ];
            
            flowerPositions.forEach(pos => {
                const adjustedY = pos.y - (roadOffset * 0.5) % 700;
                if (adjustedY > -50 && adjustedY < canvas.height + 50) {
                    drawFlower(pos.x, adjustedY);
                }
            });
        }
        
        // دالة رسم زهرة واحدة
        function drawFlower(x, y) {
            // ساق الزهرة
            ctx.strokeStyle = '#228B22';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x, y - 20);
            ctx.stroke();
            
            // بتلات الزهرة
            const colors = ['#FF69B4', '#FFB6C1', '#FF1493', '#FFC0CB'];
            for (let i = 0; i < 5; i++) {
                const angle = (i * Math.PI * 2) / 5;
                const petalX = x + Math.cos(angle) * 8;
                const petalY = y - 20 + Math.sin(angle) * 8;
                
                ctx.fillStyle = colors[i % colors.length];
                ctx.beginPath();
                ctx.arc(petalX, petalY, 6, 0, Math.PI * 2);
                ctx.fill();
            }
            
            // مركز الزهرة
            ctx.fillStyle = '#FFD700';
            ctx.beginPath();
            ctx.arc(x, y - 20, 4, 0, Math.PI * 2);
            ctx.fill();
        }
        
        // دالة رسم المباني
        function drawBuilding(x, y, width, height, side) {
            // جسم المبنى
            const gradient = ctx.createLinearGradient(x, y, x, y + height);
            gradient.addColorStop(0, '#4682B4');
            gradient.addColorStop(0.5, '#5F9EA0');
            gradient.addColorStop(1, '#2F4F4F');
            ctx.fillStyle = gradient;
            ctx.fillRect(x, y, width, height);
            
            // النوافذ
            ctx.fillStyle = currentWeather === 'night' || currentWeather === 'storm' ? '#FFFF00' : '#87CEEB';
            for (let wy = y + 10; wy < y + height - 20; wy += 25) {
                for (let wx = x + 10; wx < x + width - 10; wx += 20) {
                    ctx.fillRect(wx, wy, 12, 15);
                }
            }
            
            // باب المبنى
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(x + width/2 - 10, y + height - 30, 20, 30);
            
            // سطح المبنى
            ctx.fillStyle = '#696969';
            ctx.fillRect(x - 5, y - 10, width + 10, 10);
        }
        
        // دالة رسم ناطحات السحاب في الخلفية
        function drawSkyscrapers() {
            ctx.save();
            
            // ناطحات سحاب بعيدة
            const skyscrapers = [
                {x: 100, width: 60, height: 300},
                {x: 200, width: 80, height: 350},
                {x: 300, width: 70, height: 280},
                {x: 450, width: 90, height: 400},
                {x: 600, width: 75, height: 320},
                {x: 750, width: 85, height: 380},
                {x: 850, width: 65, height: 290}
            ];
            
            skyscrapers.forEach(building => {
                const gradient = ctx.createLinearGradient(building.x, 100, building.x, 100 + building.height);
                gradient.addColorStop(0, 'rgba(70, 130, 180, 0.6)');
                gradient.addColorStop(1, 'rgba(25, 25, 112, 0.6)');
                ctx.fillStyle = gradient;
                ctx.fillRect(building.x, 400 - building.height, building.width, building.height);
                
                // نوافذ ناطحات السحاب
                ctx.fillStyle = currentWeather === 'night' || currentWeather === 'storm' ? 
                    'rgba(255, 255, 0, 0.8)' : 'rgba(135, 206, 235, 0.5)';
                for (let y = 410 - building.height; y < 390; y += 20) {
                    for (let x = building.x + 5; x < building.x + building.width - 5; x += 15) {
                        if (Math.random() > 0.3) { // بعض النوافذ مضاءة
                            ctx.fillRect(x, y, 10, 12);
                        }
                    }
                }
                
                // أضواء على قمة المبنى
                if (currentWeather === 'night' || currentWeather === 'storm') {
                    ctx.fillStyle = '#FF0000';
                    ctx.beginPath();
                    ctx.arc(building.x + building.width/2, 400 - building.height - 5, 3, 0, Math.PI * 2);
                    ctx.fill();
                }
            });
            
            ctx.restore();
        }
        
        // دالة رسم تفاصيل المدينة
        function drawCityDetails() {
            const offset = (roadOffset * 0.3) % 400;
            
            // رسم لوحات إعلانية
            for (let y = -100 - offset; y < canvas.height + 100; y += 400) {
                // لوحة إعلانية على اليسار
                ctx.fillStyle = '#222222';
                ctx.fillRect(-50, y, 40, 60);
                ctx.fillStyle = '#ff0000';
                ctx.fillRect(-48, y + 2, 36, 56);
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 12px Arial';
                ctx.fillText('إعلان', -40, y + 30);
                
                // عمود اللوحة
                ctx.fillStyle = '#666666';
                ctx.fillRect(-32, y + 60, 4, 40);
                
                // لوحة إعلانية على اليمين
                ctx.fillStyle = '#222222';
                ctx.fillRect(canvas.width + 10, y + 200, 40, 60);
                ctx.fillStyle = '#0066ff';
                ctx.fillRect(canvas.width + 12, y + 202, 36, 56);
                ctx.fillStyle = '#ffffff';
                ctx.fillText('مدينة', canvas.width + 18, y + 230);
                
                // عمود اللوحة
                ctx.fillStyle = '#666666';
                ctx.fillRect(canvas.width + 28, y + 260, 4, 40);
            }
            
            // رسم إشارات مرور
            for (let y = -200 - offset * 2; y < canvas.height + 200; y += 300) {
                // إشارة مرور على اليسار
                ctx.fillStyle = '#333333';
                ctx.fillRect(10, y, 6, 80);
                ctx.fillRect(0, y - 10, 26, 40);
                
                // الأضواء
                ctx.fillStyle = currentWeather === 'night' ? '#ff0000' : '#660000';
                ctx.beginPath();
                ctx.arc(13, y, 8, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = currentWeather === 'night' ? '#ffff00' : '#666600';
                ctx.beginPath();
                ctx.arc(13, y + 13, 8, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = currentWeather === 'night' ? '#00ff00' : '#006600';
                ctx.beginPath();
                ctx.arc(13, y + 26, 8, 0, Math.PI * 2);
                ctx.fill();
            }
        }

function drawStreetLights() {
            // رسم عواميد الإنارة على جانبي الطريق
            const lightSpacing = 150;
            const offset = (roadOffset * 2) % lightSpacing;
            
            for (let y = -100 - offset; y < canvas.height + 100; y += lightSpacing) {
                // عمود على اليسار
                drawStreetLight(5, y, true);
                // عمود على اليمين
                drawStreetLight(canvas.width - 25, y, false);
            }
        }
        
        function drawStreetLight(x, y, isLeft) {
            // رسم العمود
            ctx.fillStyle = '#666';
            ctx.fillRect(x + (isLeft ? 0 : 15), y, 10, 60);
            
            // رسم ذراع العمود
            ctx.fillStyle = '#555';
            if (isLeft) {
                ctx.fillRect(x + 10, y + 10, 30, 5);
            } else {
                ctx.fillRect(x - 15, y + 10, 30, 5);
            }
            
            // رسم المصباح
            ctx.fillStyle = currentWeather === 'night' || currentWeather === 'storm' ? '#ffff00' : '#666';
            if (isLeft) {
                ctx.fillRect(x + 35, y + 5, 15, 15);
            } else {
                ctx.fillRect(x - 20, y + 5, 15, 15);
            }
            
            // رسم الضوء في الليل
            if (currentWeather === 'night' || currentWeather === 'storm') {
                const gradient = ctx.createRadialGradient(
                    isLeft ? x + 42 : x - 12, y + 12, 0,
                    isLeft ? x + 42 : x - 12, y + 12, 50
                );
                gradient.addColorStop(0, 'rgba(255, 255, 0, 0.6)');
                gradient.addColorStop(1, 'rgba(255, 255, 0, 0)');
                ctx.fillStyle = gradient;
                ctx.fillRect(
                    isLeft ? x + 35 : x - 55, y - 30,
                    60, 80
                );
            }
        }

        function drawBackground() {
            switch(currentWeather) {
                case 'sunny':
                    // Sunny day gradient
                    const sunnyGradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
                    sunnyGradient.addColorStop(0, '#87CEEB');
                    sunnyGradient.addColorStop(1, '#98D8C8');
                    ctx.fillStyle = sunnyGradient;
                    break;
                case 'rain':
                case 'storm':
                    // Rainy/stormy gradient
                    const rainGradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
                    rainGradient.addColorStop(0, '#4a4a4a');
                    rainGradient.addColorStop(1, '#2a2a2a');
                    ctx.fillStyle = rainGradient;
                    break;
                case 'snow':
                    // Snowy gradient
                    const snowGradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
                    snowGradient.addColorStop(0, '#d6d6d6');
                    snowGradient.addColorStop(1, '#a8a8a8');
                    ctx.fillStyle = snowGradient;
                    break;
                case 'autumn':
                    // Autumn gradient
                    const autumnGradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
                    autumnGradient.addColorStop(0, '#ff9966');
                    autumnGradient.addColorStop(0.5, '#ff6633');
                    autumnGradient.addColorStop(1, '#cc6600');
                    ctx.fillStyle = autumnGradient;
                    break;
                case 'night':
                    // Night gradient
                    const nightGradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
                    nightGradient.addColorStop(0, '#0c1445');
                    nightGradient.addColorStop(1, '#1a237e');
                    ctx.fillStyle = nightGradient;
                    break;
            }
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        function drawCar(x, y, width, height, color, isPlayer = false) {
            ctx.save();
            
            // Shadow
            ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
            ctx.shadowBlur = 10;
            ctx.shadowOffsetX = 3;
            ctx.shadowOffsetY = 3;
            
            // Car body with gradient
            const gradient = ctx.createLinearGradient(x, y, x, y + height);
            gradient.addColorStop(0, color);
            gradient.addColorStop(0.5, shadeColor(color, 20));
            gradient.addColorStop(1, shadeColor(color, -20));
            ctx.fillStyle = gradient;
            
            // Rounded car body
            ctx.beginPath();
            ctx.moveTo(x + 10, y);
            ctx.lineTo(x + width - 10, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + 10);
            ctx.lineTo(x + width, y + height - 10);
            ctx.quadraticCurveTo(x + width, y + height, x + width - 10, y + height);
            ctx.lineTo(x + 10, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - 10);
            ctx.lineTo(x, y + 10);
            ctx.quadraticCurveTo(x, y, x + 10, y);
            ctx.closePath();
            ctx.fill();
            
            // Reset shadow for details
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
            
            // Car outline
            ctx.strokeStyle = isPlayer ? '#00ffff' : shadeColor(color, -40);
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // Front windshield
            const windshieldGradient = ctx.createLinearGradient(x + 15, y + 10, x + 15, y + 35);
            windshieldGradient.addColorStop(0, 'rgba(135, 206, 235, 0.8)');
            windshieldGradient.addColorStop(1, 'rgba(70, 130, 180, 0.6)');
            ctx.fillStyle = windshieldGradient;
            ctx.beginPath();
            ctx.moveTo(x + 15, y + 15);
            ctx.lineTo(x + width - 15, y + 15);
            ctx.lineTo(x + width - 10, y + 35);
            ctx.lineTo(x + 10, y + 35);
            ctx.closePath();
            ctx.fill();
            ctx.strokeStyle = '#1a1a1a';
            ctx.lineWidth = 1;
            ctx.stroke();
            
            // Rear windshield
            ctx.fillStyle = 'rgba(70, 130, 180, 0.6)';
            ctx.beginPath();
            ctx.moveTo(x + 10, y + 65);
            ctx.lineTo(x + width - 10, y + 65);
            ctx.lineTo(x + width - 15, y + 80);
            ctx.lineTo(x + 15, y + 80);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            
            // Side windows
            ctx.fillStyle = 'rgba(100, 149, 237, 0.5)';
            ctx.fillRect(x + 5, y + 40, 10, 20);
            ctx.fillRect(x + width - 15, y + 40, 10, 20);
            
            // Hood details
            ctx.strokeStyle = shadeColor(color, -30);
            ctx.lineWidth = 1.5;
            ctx.beginPath();
            ctx.moveTo(x + width / 2, y + 5);
            ctx.lineTo(x + width / 2, y + 25);
            ctx.stroke();
            
            // Door lines
            ctx.beginPath();
            ctx.moveTo(x + 5, y + 45);
            ctx.lineTo(x + width - 5, y + 45);
            ctx.stroke();
            
            // Wheels
            ctx.fillStyle = '#1a1a1a';
            ctx.fillRect(x - 5, y + 15, 8, 20);
            ctx.fillRect(x - 5, y + height - 35, 8, 20);
            ctx.fillRect(x + width - 3, y + 15, 8, 20);
            ctx.fillRect(x + width - 3, y + height - 35, 8, 20);
            
            // Headlights - only at night or storm
            if (currentWeather === 'night' || currentWeather === 'storm') {
                // Front lights with strong glow
                ctx.shadowColor = '#ffff00';
                ctx.shadowBlur = 25;
                ctx.fillStyle = '#ffff00';
                ctx.beginPath();
                ctx.ellipse(x + 12, y + 5, 10, 6, 0, 0, 2 * Math.PI);
                ctx.fill();
                ctx.beginPath();
                ctx.ellipse(x + width - 12, y + 5, 10, 6, 0, 0, 2 * Math.PI);
                ctx.fill();
                
                // Realistic cone-shaped light beams
                ctx.shadowBlur = 0; // Reset shadow for beams
                const beamLength = 200;
                
                // Left beam - cone shape
                const leftBeamGradient = ctx.createLinearGradient(x + 12, y, x + 12, y - beamLength);
                leftBeamGradient.addColorStop(0, 'rgba(255, 255, 200, 0.6)');
                leftBeamGradient.addColorStop(0.2, 'rgba(255, 255, 150, 0.4)');
                leftBeamGradient.addColorStop(0.5, 'rgba(255, 255, 100, 0.2)');
                leftBeamGradient.addColorStop(0.8, 'rgba(255, 255, 50, 0.05)');
                leftBeamGradient.addColorStop(1, 'rgba(255, 255, 0, 0)');
                
                ctx.fillStyle = leftBeamGradient;
                ctx.beginPath();
                ctx.moveTo(x + 12, y);
                ctx.lineTo(x + 2, y - beamLength);
                ctx.lineTo(x + 22, y - beamLength);
                ctx.closePath();
                ctx.fill();
                
                // Right beam - cone shape
                const rightBeamGradient = ctx.createLinearGradient(x + width - 12, y, x + width - 12, y - beamLength);
                rightBeamGradient.addColorStop(0, 'rgba(255, 255, 200, 0.6)');
                rightBeamGradient.addColorStop(0.2, 'rgba(255, 255, 150, 0.4)');
                rightBeamGradient.addColorStop(0.5, 'rgba(255, 255, 100, 0.2)');
                rightBeamGradient.addColorStop(0.8, 'rgba(255, 255, 50, 0.05)');
                rightBeamGradient.addColorStop(1, 'rgba(255, 255, 0, 0)');
                
                ctx.fillStyle = rightBeamGradient;
                ctx.beginPath();
                ctx.moveTo(x + width - 12, y);
                ctx.lineTo(x + width - 22, y - beamLength);
                ctx.lineTo(x + width - 2, y - beamLength);
                ctx.closePath();
                ctx.fill();
                
                // Tail lights with strong glow
                ctx.shadowColor = '#ff0000';
                ctx.shadowBlur = 20;
                ctx.fillStyle = '#ff0000';
                ctx.fillRect(x + 8, y + height - 8, 14, 8);
                ctx.fillRect(x + width - 22, y + height - 8, 14, 8);
                
                // Reset shadow blur
                ctx.shadowBlur = 0;
                
                // Racing stripes for player
                if (isPlayer) {
                    ctx.shadowBlur = 0;
                    ctx.strokeStyle = '#ffffff';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(x + width / 2 - 5, y + 10);
                    ctx.lineTo(x + width / 2 - 5, y + height - 10);
                    ctx.stroke();
                    ctx.beginPath();
                    ctx.moveTo(x + width / 2 + 5, y + 10);
                    ctx.lineTo(x + width / 2 + 5, y + height - 10);
                    ctx.stroke();
                }
            } else {
                // كشافات مطفأة في النهار
                ctx.shadowBlur = 0;
                // الكشافات الأمامية مطفأة
                ctx.fillStyle = '#cccccc';
                ctx.beginPath();
                ctx.ellipse(x + 12, y + 5, 8, 5, 0, 0, 2 * Math.PI);
                ctx.fill();
                ctx.beginPath();
                ctx.ellipse(x + width - 12, y + 5, 8, 5, 0, 0, 2 * Math.PI);
                ctx.fill();
                
                // الأضواء الخلفية مطفأة
                ctx.fillStyle = '#8b0000';
                ctx.fillRect(x + 8, y + height - 8, 12, 6);
                ctx.fillRect(x + width - 20, y + height - 8, 12, 6);
                
                // Racing stripes للسيارة اللاعب فقط
                if (isPlayer) {
                    ctx.strokeStyle = '#ffffff';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(x + width / 2 - 5, y + 10);
                    ctx.lineTo(x + width / 2 - 5, y + height - 10);
                    ctx.stroke();
                    ctx.beginPath();
                    ctx.moveTo(x + width / 2 + 5, y + 10);
                    ctx.lineTo(x + width / 2 + 5, y + height - 10);
                    ctx.stroke();
                }
            }
            
            // Side mirrors
            ctx.fillStyle = color;
            ctx.fillRect(x - 8, y + 25, 6, 8);
            ctx.fillRect(x + width + 2, y + 25, 6, 8);
            
            ctx.restore();
        }
        
        // Helper function to shade colors
        function shadeColor(color, percent) {
            const num = parseInt(color.replace("#",""), 16);
            const amt = Math.round(2.55 * percent);
            const R = (num >> 16) + amt;
            const G = (num >> 8 & 0x00FF) + amt;
            const B = (num & 0x0000FF) + amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        }


        function updatePlayer() {
            // التحكم في السرعة باستخدام المفاتيح
            if (keys[playerCar.keys.up.toLowerCase()]) {
                playerCar.speed = Math.min(playerCar.speed + 0.2, 10); // زيادة السرعة حتى 10
            } else if (keys[playerCar.keys.down.toLowerCase()]) {
                playerCar.speed = Math.max(playerCar.speed - 0.3, 0); // تقليل السرعة حتى 0
            } else {
                // تباطؤ تدريجي إذا لم يتم الضغط على أي مفتاح
                playerCar.speed = Math.max(playerCar.speed - 0.05, 0);
            }
            
            // تحديث المسافة المقطوعة بناءً على سرعة السيارة
            playerCar.distance += playerCar.speed;
            
            // لا نغير موضع السيارة - ستبقى ثابتة
            
            // التحقق من الوصول لخط النهاية
            if (playerCar.distance >= 50000) {
                finishRace();
            }
            
            // لا نحدث سرعة الطريق هنا
            
            // تحديث صوت المحرك
            updateEngineSound();
        }


        function updateRacingCars() {
            // تحديث السيارات المتسابقة
            for (let racer of racingCars) {
                // التحكم في سرعة كل سيارة باستخدام مفاتيحها الخاصة
                if (keys[racer.keys.up.toLowerCase()]) {
                    racer.speed = Math.min(racer.speed + 0.2, 10); // زيادة السرعة
                } else if (keys[racer.keys.down.toLowerCase()]) {
                    racer.speed = Math.max(racer.speed - 0.3, 0); // تقليل السرعة
                } else {
                    // تباطؤ تدريجي
                    racer.speed = Math.max(racer.speed - 0.05, 0);
                }
                
                // تحديث المسافة بناءً على السرعة
                racer.distance += racer.speed;
                
                // لا نغير موضع السيارة - ستبقى ثابتة
                
                // التحقق من وصول السيارات لخط النهاية
                if (racer.distance >= 50000 && !racer.finished) {
                    racer.finished = true;
                    racer.finishTime = Date.now();
                }
            }
            
            // حساب موقع اللاعب في السباق
            updateRacePosition();
        }

        function updateRacePosition() {
            // حساب ترتيب جميع السيارات
            const allCars = [playerCar, ...racingCars];
            allCars.sort((a, b) => b.distance - a.distance);
            
            // تحديث مركز كل سيارة
            for (let i = 0; i < allCars.length; i++) {
                if (allCars[i] === playerCar) {
                    racePosition = i + 1;
                    break;
                }
            }
        }




        function checkCollisions() {
            // التصادم مع السيارات المتسابقة (يبطئ السرعة فقط)
            for (let racer of racingCars) {
                if (playerCar.x < racer.x + racer.width - 20 &&
                    playerCar.x + playerCar.width - 20 > racer.x &&
                    playerCar.y < racer.y + racer.height - 20 &&
                    playerCar.y + playerCar.height - 20 > racer.y) {
                    speed = Math.max(speed - 0.5, 2);
                    playCollisionSound();
                }
            }
        }

        function gameOver() {
            gameRunning = false;
            finalScoreElement.innerHTML = `
                <div>نقاطك النهائية: ${score}</div>
                <div>المركز في السباق: ${racePosition} من ${totalRacers}</div>
                <div>المسافة المقطوعة: ${Math.floor(playerCar.distance / 100)} متر</div>
            `;
            gameOverElement.style.display = 'block';
        }
        
        function finishRace() {
            gameRunning = false;
            // حساب المركز النهائي
            updateRacePosition();
            
            // عرض رسالة الفوز
            let message = '';
            if (racePosition === 1) {
                message = '🏆 مبروك! أنت الفائز! 🏆';
            } else if (racePosition <= 3) {
                message = '🥈 أحسنت! مركز ممتاز!';
            } else {
                message = 'أحسنت! لقد أكملت السباق!';
            }
            
            finalScoreElement.innerHTML = `
                <h2 style="color: #00ff00;">${message}</h2>
                <div>نقاطك النهائية: ${score}</div>
                <div>المركز في السباق: ${racePosition} من ${totalRacers}</div>
                <div>المسافة المقطوعة: 500 متر</div>
            `;
            gameOverElement.style.display = 'block';
        }

        function drawRaceInfo() {
            // Get lead car for distance to finish calculation
            const leadCar = getLeadCar();
            const distanceToFinish = Math.max(0, 50000 - leadCar.distance);
            const distanceToFinishMeters = Math.floor(distanceToFinish / 100);
            
            // رسم معلومات السباق بشكل أفقي
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(10, 10, canvas.width - 20, 60);
            
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 24px Arial';
            
            // عرض المسافة المتبقية لخط النهاية والطقس فقط
            ctx.fillText(`المسافة لخط النهاية: ${distanceToFinishMeters}م`, 30, 40);
            
            ctx.fillStyle = '#00ff00';
            ctx.fillText(`${getWeatherName()}`, canvas.width - 200, 40);
        }

        function getWeatherName() {
            switch(currentWeather) {
                case 'sunny': return 'مشمس ☀️';
                case 'rain': return 'ممطر 🌧️';
                case 'snow': return 'ثلجي ❄️';
                case 'autumn': return 'خريف 🍂';
                case 'night': return 'ليلي 🌙';
                case 'storm': return 'عاصف ⛈️';
                default: return 'عادي';
            }
        }

        function updateWeather() {
            weatherTimer++;
            
            // تغيير الطقس كل 30 ثانية (1800 إطار عند 60 إطار في الثانية)
            if (weatherTimer > 1800) {
                weatherTimer = 0;
                currentWeather = weatherTypes[Math.floor(Math.random() * weatherTypes.length)];
                weatherIntensity = 0;
                updateWeatherSounds();
            }
            
            // زيادة شدة الطقس تدريجياً
            if (weatherIntensity < 1) {
                weatherIntensity += 0.02;
            }
            
            // تحديث الجزيئات
            updateParticles();
            
            // البرق في العواصف
            if (currentWeather === 'storm' && Math.random() < 0.01) {
                lightning.active = true;
                lightning.timer = 5;
                playThunderSound();
            }
            
            if (lightning.active && lightning.timer > 0) {
                lightning.timer--;
            } else {
                lightning.active = false;
            }
        }

        function updateParticles() {
            // إضافة جزيئات جديدة حسب الطقس
            if (currentWeather === 'rain' || currentWeather === 'storm') {
                for (let i = 0; i < 5 * weatherIntensity; i++) {
                    particles.push({
                        x: Math.random() * canvas.width,
                        y: -10,
                        speedY: 10 + Math.random() * 5,
                        speedX: -2 + Math.random() * 4,
                        size: 2 + Math.random() * 2,
                        type: 'rain'
                    });
                }
            } else if (currentWeather === 'snow') {
                for (let i = 0; i < 3 * weatherIntensity; i++) {
                    particles.push({
                        x: Math.random() * canvas.width,
                        y: -10,
                        speedY: 2 + Math.random() * 2,
                        speedX: -1 + Math.random() * 2,
                        size: 3 + Math.random() * 4,
                        type: 'snow'
                    });
                }
            } else if (currentWeather === 'autumn') {
                if (Math.random() < 0.1 * weatherIntensity) {
                    particles.push({
                        x: Math.random() * canvas.width,
                        y: -10,
                        speedY: 1 + Math.random() * 2,
                        speedX: -2 + Math.random() * 4,
                        size: 15 + Math.random() * 10,
                        rotation: Math.random() * Math.PI * 2,
                        rotationSpeed: (Math.random() - 0.5) * 0.1,
                        type: 'leaf',
                        color: ['#ff6600', '#cc3300', '#ff9900', '#ffcc00'][Math.floor(Math.random() * 4)]
                    });
                }
            }
            
            // تحديث وإزالة الجزيئات
            for (let i = particles.length - 1; i >= 0; i--) {
                const p = particles[i];
                p.x += p.speedX;
                p.y += p.speedY;
                
                if (p.type === 'leaf') {
                    p.rotation += p.rotationSpeed;
                }
                
                if (p.y > canvas.height || p.x < -50 || p.x > canvas.width + 50) {
                    particles.splice(i, 1);
                }
            }
            
            // الحد من عدد الجزيئات
            if (particles.length > 200) {
                particles = particles.slice(-200);
            }
        }

        function drawWeatherEffects() {
            // رسم تأثيرات الطقس
            for (let p of particles) {
                ctx.save();
                
                if (p.type === 'rain') {
                    ctx.strokeStyle = 'rgba(174, 194, 224, 0.6)';
                    ctx.lineWidth = p.size;
                    ctx.beginPath();
                    ctx.moveTo(p.x, p.y);
                    ctx.lineTo(p.x - p.speedX, p.y - p.speedY);
                    ctx.stroke();
                } else if (p.type === 'snow') {
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                    ctx.beginPath();
                    ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2);
                    ctx.fill();
                } else if (p.type === 'leaf') {
                    ctx.translate(p.x, p.y);
                    ctx.rotate(p.rotation);
                    ctx.fillStyle = p.color;
                    ctx.beginPath();
                    ctx.ellipse(0, 0, p.size, p.size * 0.7, 0, 0, Math.PI * 2);
                    ctx.fill();
                }
                
                ctx.restore();
            }
            
            // رسم البرق
            if (lightning.active) {
                ctx.fillStyle = `rgba(255, 255, 255, ${lightning.timer / 5})`;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
            }
            
            // ضباب في الليل
            if (currentWeather === 'night') {
                ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
            }
            
            // رسم الشمس في الطقس المشمس
            if (currentWeather === 'sunny' && weatherIntensity > 0.5) {
                ctx.save();
                const sunGradient = ctx.createRadialGradient(canvas.width - 100, 100, 0, canvas.width - 100, 100, 50);
                sunGradient.addColorStop(0, 'rgba(255, 255, 0, 0.8)');
                sunGradient.addColorStop(1, 'rgba(255, 255, 0, 0)');
                ctx.fillStyle = sunGradient;
                ctx.fillRect(canvas.width - 200, 0, 200, 200);
                ctx.restore();
            }
        }

        function gameLoop() {
            if (!gameRunning) return;

            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Update weather
            updateWeather();

            // Update and draw racing cars
            updatePlayer();
            updateRacingCars();
            // الحصول على السيارة في المقدمة
            const leadCar = getLeadCar();
            // الموضع المستهدف للسيارة الأولى على الشاشة
            const cameraTargetY = canvas.height / 2; // وضع السيارة في منتصف الشاشة
            
            // Draw game elements
            drawRoad(leadCar, cameraTargetY);
            
            // رسم جميع السيارات
            const allCars = [playerCar, ...racingCars];
            // ترتيب السيارات من الأقرب للأبعد لرسمها بالترتيب الصحيح
            allCars.sort((a, b) => a.distance - b.distance);
            
            for (let car of allCars) {
                // حساب الموضع النسبي بناءً على الفرق في المسافة من السيارة الأولى
                const distanceDiff = car.distance - leadCar.distance; // الفرق بين السيارة الحالية والأولى
                // الموضع النهائي: السيارة المتقدمة تظهر في الأمام (أعلى الشاشة)
                // تقليل المعامل لعرض جميع السيارات بشكل متقارب
                const carY = cameraTargetY - (distanceDiff * 0.5); // معامل أصغر لعرض مزيد من السيارات
                
                // رسم السيارة فقط إذا كانت مرئية ولم تنهي السباق
                if (carY > -200 && carY < canvas.height + 200 && !car.finished) {
                    drawCar(car.x, carY, car.width, car.height, car.color, car === playerCar);
                    
                    // رسم اسم المتسابق
                    ctx.fillStyle = '#ffffff';
                    ctx.font = 'bold 12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(car.name, car.x + car.width/2, carY - 10);
                }
            }


            
            ctx.textAlign = 'left';


            // Draw weather effects (before UI)
            drawWeatherEffects();

            // Draw race info
            drawRaceInfo();
            
            // Draw rearview mirror
            drawRearviewMirror();

            // Update score display - removed since we don't use points anymore
            // scoreElement.textContent = `النقاط: ${score}`;
            
            // زيادة النقاط بناءً على المسافة والسرعة
            if (Math.floor(playerCar.distance) % 100 === 0) {
                score += Math.floor(speed);
            }

            // Continue game loop
            requestAnimationFrame(gameLoop);
        }
        
        // Sound Functions
        function initializeSounds() {
            // Create engine sound
            createEngineSound();
            
            // Create rain sound
            createRainSound();
            
            // Create wind sound
            createWindSound();
            
            // Create street sound
            createStreetSound();
            
            // Start engine sound
            if (engineSound) {
                engineSound.start(0);
                sounds.engineRunning = true;
            }
            
            // Start street ambient sound
            if (streetSound) {
                streetSound.start();
                sounds.streetPlaying = true;
            }
            
            // Update sounds based on weather
            updateWeatherSounds();
        }
        
        function createEngineSound() {
            // Create oscillators for engine sound
            const oscillator1 = audioContext.createOscillator();
            const oscillator2 = audioContext.createOscillator();
            const oscillator3 = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            const filter1 = audioContext.createBiquadFilter();
            const filter2 = audioContext.createBiquadFilter();
            
            // Configure oscillators for smoother sound
            oscillator1.type = 'sine';
            oscillator1.frequency.value = 40;
            
            oscillator2.type = 'triangle';
            oscillator2.frequency.value = 80;
            
            oscillator3.type = 'sine';
            oscillator3.frequency.value = 120;
            
            // Configure filters for warmer tone
            filter1.type = 'lowpass';
            filter1.frequency.value = 400;
            filter1.Q.value = 0.5;
            
            filter2.type = 'bandpass';
            filter2.frequency.value = 300;
            filter2.Q.value = 0.3;
            
            // Configure gain (normal volume)
            gainNode.gain.value = 0.08;
            
            // Create sub-gains for mixing
            const gain1 = audioContext.createGain();
            const gain2 = audioContext.createGain();
            const gain3 = audioContext.createGain();
            
            gain1.gain.value = 0.6;
            gain2.gain.value = 0.3;
            gain3.gain.value = 0.1;
            
            // Connect nodes
            oscillator1.connect(gain1);
            oscillator2.connect(gain2);
            oscillator3.connect(gain3);
            
            gain1.connect(filter1);
            gain2.connect(filter1);
            gain3.connect(filter2);
            
            filter1.connect(gainNode);
            filter2.connect(gainNode);
            gainNode.connect(masterGain);
            
            // Store for later use
            engineSound = {
                oscillator1: oscillator1,
                oscillator2: oscillator2,
                oscillator3: oscillator3,
                gainNode: gainNode,
                filter1: filter1,
                filter2: filter2,
                start: function() {
                    oscillator1.start();
                    oscillator2.start();
                    oscillator3.start();
                }
            };
        }
        
        // إزالة دالة updateGlobalSpeed القديمة
        
        function updateEngineSound() {
            if (!engineSound || !sounds.engineRunning) return;
            
            // Update engine frequency based on player car speed
            const speedFactor = playerCar.speed / 10;
            
            // Smoother frequency changes
            engineSound.oscillator1.frequency.setTargetAtTime(
                40 + (speedFactor * 30), 
                audioContext.currentTime, 
                0.2
            );
            engineSound.oscillator2.frequency.setTargetAtTime(
                80 + (speedFactor * 40), 
                audioContext.currentTime, 
                0.2
            );
            engineSound.oscillator3.frequency.setTargetAtTime(
                120 + (speedFactor * 50), 
                audioContext.currentTime, 
                0.2
            );
            
            // Update filters for more realistic sound
            engineSound.filter1.frequency.setTargetAtTime(
                400 + (speedFactor * 600), 
                audioContext.currentTime, 
                0.15
            );
            engineSound.filter2.frequency.setTargetAtTime(
                300 + (speedFactor * 400), 
                audioContext.currentTime, 
                0.15
            );
            
            // Update volume based on speed (normal)
            const baseVolume = 0.08 * volumeSettings.engine;
            engineSound.gainNode.gain.setTargetAtTime(
                baseVolume + (speedFactor * 0.04 * volumeSettings.engine), 
                audioContext.currentTime, 
                0.1
            );
        }
        
        function createRainSound() {
            // Create white noise for rain
            const bufferSize = 2 * audioContext.sampleRate;
            const noiseBuffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
            const output = noiseBuffer.getChannelData(0);
            
            for (let i = 0; i < bufferSize; i++) {
                output[i] = Math.random() * 2 - 1;
            }
            
            const whiteNoise = audioContext.createBufferSource();
            whiteNoise.buffer = noiseBuffer;
            whiteNoise.loop = true;
            
            const filter = audioContext.createBiquadFilter();
            filter.type = 'bandpass';
            filter.frequency.value = 1000;
            
            const gainNode = audioContext.createGain();
            gainNode.gain.value = 0;
            
            whiteNoise.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            rainSound = {
                source: whiteNoise,
                gainNode: gainNode,
                start: function() {
                    whiteNoise.start(0);
                }
            };
        }
        
 function createStreetSound() {
            // Create a simple noise for street ambiance
            const bufferSize = 2 * audioContext.sampleRate;
            const noiseBuffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
            const output = noiseBuffer.getChannelData(0);

            for (let i = 0; i < bufferSize; i++) {
                output[i] = Math.random() * 2 - 1;
            }

            const whiteNoise = audioContext.createBufferSource();
            whiteNoise.buffer = noiseBuffer;
            whiteNoise.loop = true;

            const filter = audioContext.createBiquadFilter();
            filter.type = 'bandpass';
            filter.frequency.value = 400;

            const gainNode = audioContext.createGain();
            gainNode.gain.value = 0.05;

            whiteNoise.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(masterGain);

            streetSound = {
                source: whiteNoise,
                gainNode: gainNode,
                start: function() {
                    whiteNoise.start(0);
                }
            };
        }
        
        function createWindSound() {
            // Create filtered noise for wind
            const bufferSize = 2 * audioContext.sampleRate;
            const noiseBuffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
            const output = noiseBuffer.getChannelData(0);
            
            for (let i = 0; i < bufferSize; i++) {
                output[i] = Math.random() * 2 - 1;
            }
            
            const whiteNoise = audioContext.createBufferSource();
            whiteNoise.buffer = noiseBuffer;
            whiteNoise.loop = true;
            
            const filter = audioContext.createBiquadFilter();
            filter.type = 'lowpass';
            filter.frequency.value = 400;
            
            const gainNode = audioContext.createGain();
            gainNode.gain.value = 0;
            
            whiteNoise.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(masterGain);
            
            windSound = {
                source: whiteNoise,
                gainNode: gainNode,
                filter: filter,
                start: function() {
                    whiteNoise.start(0);
                }
            };
        }
        
        function updateWeatherSounds() {
            // Rain sound
            if (rainSound) {
                if (currentWeather === 'rain' || currentWeather === 'storm') {
                    rainSound.gainNode.gain.setTargetAtTime(0.3 * volumeSettings.rain, audioContext.currentTime, 0.5);
                    if (!sounds.rainPlaying) {
                        rainSound.start();
                        sounds.rainPlaying = true;
                    }
                } else {
                    rainSound.gainNode.gain.setTargetAtTime(0, audioContext.currentTime, 0.5);
                }
            }
            
            // Wind sound
            if (windSound) {
                if (currentWeather === 'snow' || currentWeather === 'autumn' || currentWeather === 'storm') {
                    const baseWindVolume = currentWeather === 'storm' ? 0.4 : 0.2;
                    windSound.gainNode.gain.setTargetAtTime(baseWindVolume * volumeSettings.wind, audioContext.currentTime, 0.5);
                    windSound.filter.frequency.setTargetAtTime(
                        currentWeather === 'storm' ? 600 : 400, 
                        audioContext.currentTime, 
                        0.5
                    );
                    if (!sounds.windPlaying) {
                        windSound.start();
                        sounds.windPlaying = true;
                    }
                } else {
                    windSound.gainNode.gain.setTargetAtTime(0, audioContext.currentTime, 0.5);
                }
            }
        }
        
        function playThunderSound() {
            // Create more realistic thunder sound with multiple layers
            const duration = 3;
            
            // Layer 1: Deep rumble
            const rumbleOsc = audioContext.createOscillator();
            const rumbleGain = audioContext.createGain();
            const rumbleFilter = audioContext.createBiquadFilter();
            
            rumbleOsc.type = 'sine';
            rumbleOsc.frequency.value = 20;
            rumbleFilter.type = 'lowpass';
            rumbleFilter.frequency.value = 100;
            rumbleFilter.Q.value = 2;
            
            // Layer 2: Mid crack
            const crackOsc = audioContext.createOscillator();
            const crackGain = audioContext.createGain();
            const crackFilter = audioContext.createBiquadFilter();
            
            crackOsc.type = 'sawtooth';
            crackOsc.frequency.value = 60;
            crackFilter.type = 'bandpass';
            crackFilter.frequency.value = 300;
            crackFilter.Q.value = 1;
            
            // Layer 3: High frequency noise
            const noiseBuffer = audioContext.createBuffer(1, audioContext.sampleRate * duration, audioContext.sampleRate);
            const noiseData = noiseBuffer.getChannelData(0);
            for (let i = 0; i < noiseData.length; i++) {
                noiseData[i] = (Math.random() * 2 - 1) * 0.3;
            }
            const noise = audioContext.createBufferSource();
            noise.buffer = noiseBuffer;
            const noiseGain = audioContext.createGain();
            const noiseFilter = audioContext.createBiquadFilter();
            noiseFilter.type = 'highpass';
            noiseFilter.frequency.value = 1000;
            
            // Thunder envelope
            const now = audioContext.currentTime;
            
            // Rumble envelope
            rumbleGain.gain.setValueAtTime(0, now);
            rumbleGain.gain.linearRampToValueAtTime(0.3 * volumeSettings.thunder, now + 0.05);
            rumbleGain.gain.linearRampToValueAtTime(0.2 * volumeSettings.thunder, now + 0.5);
            rumbleGain.gain.exponentialRampToValueAtTime(0.01, now + duration);
            
            // Crack envelope
            crackGain.gain.setValueAtTime(0, now);
            crackGain.gain.linearRampToValueAtTime(0.6 * volumeSettings.thunder, now + 0.02);
            crackGain.gain.linearRampToValueAtTime(0.1 * volumeSettings.thunder, now + 0.1);
            crackGain.gain.exponentialRampToValueAtTime(0.01, now + duration - 0.5);
            
            // Noise envelope
            noiseGain.gain.setValueAtTime(0, now);
            noiseGain.gain.linearRampToValueAtTime(0.4 * volumeSettings.thunder, now + 0.01);
            noiseGain.gain.linearRampToValueAtTime(0.1 * volumeSettings.thunder, now + 0.2);
            noiseGain.gain.exponentialRampToValueAtTime(0.01, now + duration - 1);
            
            // Frequency modulation
            rumbleOsc.frequency.setValueAtTime(30, now);
            rumbleOsc.frequency.exponentialRampToValueAtTime(15, now + duration);
            
            crackOsc.frequency.setValueAtTime(100, now);
            crackOsc.frequency.linearRampToValueAtTime(40, now + 0.1);
            crackOsc.frequency.exponentialRampToValueAtTime(20, now + duration - 0.5);
            
            // Connect nodes
            rumbleOsc.connect(rumbleFilter);
            rumbleFilter.connect(rumbleGain);
            rumbleGain.connect(masterGain);
            
            crackOsc.connect(crackFilter);
            crackFilter.connect(crackGain);
            crackGain.connect(masterGain);
            
            noise.connect(noiseFilter);
            noiseFilter.connect(noiseGain);
            noiseGain.connect(masterGain);
            
            // Start sounds
            rumbleOsc.start(now);
            rumbleOsc.stop(now + duration);
            crackOsc.start(now);
            crackOsc.stop(now + duration - 0.5);
            noise.start(now);
            noise.stop(now + duration - 1);
        }
        
        function playCollisionSound() {
            // Create short impact sound
            const duration = 0.1;
            const osc = audioContext.createOscillator();
            const gain = audioContext.createGain();
            
            osc.type = 'square';
            osc.frequency.value = 150;
            
            gain.gain.setValueAtTime(0.2 * volumeSettings.collision, audioContext.currentTime);
            gain.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
            
            osc.connect(gain);
            gain.connect(masterGain);
            
            osc.start(audioContext.currentTime);
            osc.stop(audioContext.currentTime + duration);
            
            // Add some noise for impact
            const noise = audioContext.createBufferSource();
            const noiseBuffer = audioContext.createBuffer(1, audioContext.sampleRate * duration, audioContext.sampleRate);
            const noiseData = noiseBuffer.getChannelData(0);
            
            for (let i = 0; i < noiseData.length; i++) {
                noiseData[i] = (Math.random() * 2 - 1) * 0.2;
            }
            
            noise.buffer = noiseBuffer;
            const noiseGain = audioContext.createGain();
            noiseGain.gain.setValueAtTime(0.3 * volumeSettings.collision, audioContext.currentTime);
            noiseGain.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
            
            noise.connect(noiseGain);
            noiseGain.connect(masterGain);
            noise.start(audioContext.currentTime);
        }
        
        // Settings Functions
        function openSettings() {
            settingsPanel.style.display = 'block';
            gameRunning = false;
        }
        
        function closeSettings() {
            settingsPanel.style.display = 'none';
            if (!gameOverElement.style.display || gameOverElement.style.display === 'none') {
                gameRunning = true;
            }
        }
        
        function resetSettings() {
            // Reset all volumes to default
            document.getElementById('masterVolume').value = 50;
            document.getElementById('engineVolume').value = 100;
            document.getElementById('rainVolume').value = 100;
            document.getElementById('windVolume').value = 100;
            document.getElementById('thunderVolume').value = 100;
            document.getElementById('collisionVolume').value = 100;
            document.getElementById('streetVolume').value = 100;
            
            // Update values display
            document.getElementById('masterValue').textContent = '50%';
            document.getElementById('engineValue').textContent = '100%';
            document.getElementById('rainValue').textContent = '100%';
            document.getElementById('windValue').textContent = '100%';
            document.getElementById('thunderValue').textContent = '100%';
            document.getElementById('collisionValue').textContent = '100%';
            document.getElementById('streetValue').textContent = '100%';
            
            // Apply default values
            volumeSettings.master = 0.5;
            volumeSettings.engine = 1.0;
            volumeSettings.rain = 1.0;
            volumeSettings.wind = 1.0;
            volumeSettings.thunder = 1.0;
            volumeSettings.collision = 1.0;
            volumeSettings.street = 1.0;
            
            // Update master volume
            masterGain.gain.value = volumeSettings.master;
            
            // Update active sounds
            updateEngineSound();
            updateWeatherSounds();
        }
        
        function initializeVolumeControls() {
            // Master volume
            const masterSlider = document.getElementById('masterVolume');
            const masterValue = document.getElementById('masterValue');
            masterSlider.addEventListener('input', function() {
                volumeSettings.master = this.value / 100;
                masterValue.textContent = this.value + '%';
                masterGain.gain.value = volumeSettings.master;
            });
            
            // Engine volume
            const engineSlider = document.getElementById('engineVolume');
            const engineValue = document.getElementById('engineValue');
            engineSlider.addEventListener('input', function() {
                volumeSettings.engine = this.value / 100;
                engineValue.textContent = this.value + '%';
                updateEngineSound();
            });
            
            // Rain volume
            const rainSlider = document.getElementById('rainVolume');
            const rainValue = document.getElementById('rainValue');
            rainSlider.addEventListener('input', function() {
                volumeSettings.rain = this.value / 100;
                rainValue.textContent = this.value + '%';
                updateWeatherSounds();
            });
            
            // Wind volume
            const windSlider = document.getElementById('windVolume');
            const windValue = document.getElementById('windValue');
            windSlider.addEventListener('input', function() {
                volumeSettings.wind = this.value / 100;
                windValue.textContent = this.value + '%';
                updateWeatherSounds();
            });
            
            // Thunder volume
            const thunderSlider = document.getElementById('thunderVolume');
            const thunderValue = document.getElementById('thunderValue');
            thunderSlider.addEventListener('input', function() {
                volumeSettings.thunder = this.value / 100;
                thunderValue.textContent = this.value + '%';
            });
            
            // Collision volume
            const collisionSlider = document.getElementById('collisionVolume');
            const collisionValue = document.getElementById('collisionValue');
            collisionSlider.addEventListener('input', function() {
                volumeSettings.collision = this.value / 100;
                collisionValue.textContent = this.value + '%';
            });
            
            // Street volume
            const streetSlider = document.getElementById('streetVolume');
            const streetValue = document.getElementById('streetValue');
            streetSlider.addEventListener('input', function() {
                volumeSettings.street = this.value / 100;
                streetValue.textContent = this.value + '%';
                if (streetSound && streetSound.gainNode) {
                    streetSound.gainNode.gain.value = 0.05 * volumeSettings.street;
                }
            });
        }
    </script>
</body>
</html>
