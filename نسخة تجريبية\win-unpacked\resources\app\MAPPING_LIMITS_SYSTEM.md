# نظام تحديد التعيينات للمستخدمين المجانيين

## 🎯 الهدف:
تحديد عدد التعيينات المسموحة للمستخدمين المجانيين بـ **5 تعيينات فقط**، مع السماح بتعيينات غير محدودة للمشتركين.

## ⚙️ التغييرات المطبقة:

### 1. **إضافة Firebase Integration:**
```javascript
// Firebase Integration
<script type="module" src="/js/firebase-config.js"></script>

// متغيرات عامة لحالة المستخدم والاشتراك
let currentUser = null;
let userSubscription = null;
let hasActiveSubscription = false;
```

### 2. **دالة التحقق من حالة الاشتراك:**
```javascript
async function loadUserSubscription() {
  try {
    if (window.firebaseHelpers && currentUser) {
      userSubscription = await window.firebaseHelpers.getUserSubscription(currentUser.uid);
      hasActiveSubscription = userSubscription && 
        userSubscription.status === 'active' && 
        new Date() < (userSubscription.endDate?.toDate ? userSubscription.endDate.toDate() : new Date(userSubscription.endDate));
      
      console.log('User subscription status:', hasActiveSubscription ? 'Active' : 'None');
      updateMappingLimitUI();
    }
  } catch (error) {
    console.error('Error loading user subscription:', error);
    hasActiveSubscription = false;
    updateMappingLimitUI();
  }
}
```

### 3. **دالة التحقق من حد التعيينات:**
```javascript
function checkMappingLimit() {
  // إذا كان لديه اشتراك نشط، لا يوجد حد
  if (hasActiveSubscription) {
    return true;
  }
  
  // عد التعيينات الحالية
  const currentMappingsCount = $('.gift-mappings tbody tr').length;
  const freeLimit = 5;
  
  if (currentMappingsCount >= freeLimit) {
    showMappingLimitDialog();
    return false;
  }
  
  return true;
}
```

### 4. **نافذة تجاوز الحد:**
```javascript
function showMappingLimitDialog() {
  // عرض نافذة منبثقة تشرح الحد وتدعو للاشتراك
  // تتضمن:
  // - رسالة واضحة عن الحد (5 تعيينات)
  // - عدد التعيينات الحالية
  // - مميزات الاشتراك
  // - رابط للاشتراك ($10/شهر)
}
```

### 5. **مؤشر الحد في الواجهة:**
```javascript
function updateMappingLimitUI() {
  const currentCount = $('.gift-mappings tbody tr').length;
  const freeLimit = 5;
  
  if (!hasActiveSubscription) {
    // للمستخدمين المجانيين
    const limitIndicator = `
      <div class="mapping-limit-indicator">
        <strong>الوضع المجاني:</strong> ${currentCount}/${freeLimit} تعيينات مستخدمة
        ${currentCount >= freeLimit ? 
          'تم الوصول للحد الأقصى. اشترك للحصول على تعيينات غير محدودة' : 
          'متبقي ' + (freeLimit - currentCount) + ' تعيينات'
        }
      </div>
    `;
    
    // تعطيل زر الإضافة إذا تم الوصول للحد
    if (currentCount >= freeLimit) {
      $('#add-mapping-btn').prop('disabled', true).text('تم الوصول للحد الأقصى');
    }
  } else {
    // للمستخدمين المشتركين
    const premiumIndicator = `
      <div class="mapping-limit-indicator">
        <strong>⚡ الباقة الشاملة:</strong> تعيينات غير محدودة (${currentCount} تعيين حالياً)
      </div>
    `;
  }
}
```

### 6. **التحقق عند إضافة تعيين جديد:**
```javascript
// في معالج زر "إضافة ربط جديد"
$('#add-mapping-btn').click(function() {
  // التحقق من حد التعيينات للمستخدمين المجانيين
  if (!checkMappingLimit()) {
    return; // إيقاف العملية إذا تم تجاوز الحد
  }
  
  // متابعة إضافة التعيين...
});
```

### 7. **التحقق عند حفظ التعيين:**
```javascript
function saveMapping() {
  try {
    // التحقق من حد التعيينات للمستخدمين المجانيين (فقط للتعيينات الجديدة)
    if (!currentEditingId && !checkMappingLimit()) {
      return; // إيقاف العملية إذا تم تجاوز الحد
    }
    
    // متابعة حفظ التعيين...
  }
}
```

### 8. **تحديث المؤشر عند الحذف:**
```javascript
// عند حذف تعيين
socket.once('mappingDeleted', function(response) {
  console.log('تم حذف التعيين بنجاح:', response);
  showAlert('تم حذف التعيين بنجاح', 'success');
  
  // تحديث قائمة التعيينات
  socket.emit('getGiftMappings');
  
  // تحديث مؤشر الحد
  setTimeout(updateMappingLimitUI, 100);
});
```

## 🎨 واجهة المستخدم:

### **للمستخدمين المجانيين:**
- **مؤشر أصفر:** "الوضع المجاني: 3/5 تعيينات مستخدمة - متبقي 2 تعيينات"
- **مؤشر أحمر:** "الوضع المجاني: 5/5 تعيينات مستخدمة - تم الوصول للحد الأقصى"
- **زر معطل:** "تم الوصول للحد الأقصى" (بدلاً من "إضافة ربط جديد")

### **للمستخدمين المشتركين:**
- **مؤشر أخضر:** "⚡ الباقة الشاملة: تعيينات غير محدودة (8 تعيين حالياً)"
- **زر نشط:** "إضافة ربط جديد"

### **نافذة تجاوز الحد:**
- **العنوان:** "🔒 تم الوصول للحد الأقصى"
- **الرسالة:** "الوضع المجاني يسمح بـ 5 تعيينات فقط"
- **العداد:** "لديك حالياً 5 تعيينات من أصل 5 مسموحة"
- **المميزات:** قائمة بمميزات الاشتراك
- **الأزرار:** "إلغاء" و "اشترك الآن - $10/شهر"

## 🔄 تدفق العمل:

### **مستخدم مجاني جديد:**
1. يدخل صفحة التعيينات
2. يرى مؤشر "الوضع المجاني: 0/5 تعيينات مستخدمة"
3. يمكنه إضافة حتى 5 تعيينات
4. عند المحاولة السادسة، تظهر نافذة تجاوز الحد

### **مستخدم وصل للحد:**
1. يرى مؤشر أحمر "تم الوصول للحد الأقصى"
2. زر "إضافة ربط جديد" معطل
3. عند النقر، تظهر نافذة الاشتراك
4. يمكنه حذف تعيينات موجودة لإضافة جديدة

### **مستخدم مشترك:**
1. يرى مؤشر أخضر "تعيينات غير محدودة"
2. لا يوجد حد على عدد التعيينات
3. جميع الميزات متاحة

## 🧪 اختبار النظام:

### **اختبار الحد المجاني:**
1. سجل دخول بحساب بدون اشتراك
2. أضف 5 تعيينات
3. حاول إضافة السادس - يجب أن تظهر نافذة التحديد
4. احذف تعيين - يجب أن يصبح زر الإضافة نشط مرة أخرى

### **اختبار الاشتراك:**
1. سجل دخول بحساب مشترك
2. يجب أن ترى "تعيينات غير محدودة"
3. يمكن إضافة أي عدد من التعيينات

### **اختبار التحديث:**
1. المؤشر يتحدث فوراً عند الإضافة/الحذف
2. حالة الزر تتغير حسب العدد
3. النوافذ تظهر المعلومات الصحيحة

## 🚀 النتيجة النهائية:

- ✅ **حد واضح:** 5 تعيينات للمستخدمين المجانيين
- ✅ **واجهة بديهية:** مؤشرات ملونة وواضحة
- ✅ **دعوة للاشتراك:** نوافذ جذابة تشرح المميزات
- ✅ **تجربة سلسة:** تحديث فوري للواجهة
- ✅ **حماية كاملة:** منع الإضافة في جميع النقاط

**النظام الآن يحفز المستخدمين على الاشتراك مع توفير تجربة مجانية محدودة! 💰**
