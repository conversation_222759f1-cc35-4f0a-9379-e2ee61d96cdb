const https = require('https');

/**
 * هذه الوحدة مسؤولة عن استرداد بيانات الهدايا من TikTok API
 */

// عنوان API للهدايا
const TIKTOK_GIFT_API = 'https://webcast.tiktok.com/webcast/gift/list/?aid=1988';

/**
 * استرداد قائمة الهدايا من TikTok API
 * @returns {Promise<Array>} قائمة الهدايا مع معلوماتها
 */
async function fetchGiftsList() {
  return new Promise((resolve, reject) => {
    https.get(TIKTOK_GIFT_API, (res) => {
      let data = '';

      // تجميع البيانات
      res.on('data', (chunk) => {
        data += chunk;
      });

      // معالجة البيانات عند الانتهاء
      res.on('end', () => {
        try {
          const giftData = JSON.parse(data);
          if (giftData.status_code === 0) {
            // معالجة بيانات الهدايا من جميع الصفحات المتاحة
            let allGifts = [];
            
            if (giftData.data && Array.isArray(giftData.data)) {
              // معالجة قائمة الصفحات
              giftData.data.forEach(page => {
                if (page.gifts && Array.isArray(page.gifts)) {
                  allGifts = allGifts.concat(page.gifts);
                }
              });
            } else if (giftData.data && giftData.data.gifts && Array.isArray(giftData.data.gifts)) {
              // الحصول على الهدايا مباشرة
              allGifts = giftData.data.gifts;
            }
            
            // معالجة الهدايا وتحويلها إلى تنسيق موحد
            const processedGifts = processGiftData(allGifts);
            resolve(processedGifts);
          } else {
            reject(new Error('Failed to fetch gifts data: invalid status code ' + giftData.status_code));
          }
        } catch (error) {
          reject(error);
        }
      });
    }).on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * معالجة بيانات الهدايا وتحويلها إلى تنسيق أبسط
 * @param {Array} rawGifts الهدايا الخام من API
 * @returns {Array} قائمة الهدايا المعالجة
 */
function processGiftData(rawGifts) {
  // التأكد من أن rawGifts هو مصفوفة
  if (!Array.isArray(rawGifts)) {
    console.error('بيانات الهدايا ليست في تنسيق مصفوفة:', typeof rawGifts);
    return [];
  }
  
  console.log(`معالجة ${rawGifts.length} هدية من API`);
  
  // معالجة كل هدية
  return rawGifts.map(gift => {
    // استخراج عنوان URL للصورة من البيانات
    let imageUrl = '';
    
    // محاولة استخراج الصورة من أماكن مختلفة في البيانات
    if (gift.image && gift.image.url_list && gift.image.url_list.length > 0) {
      imageUrl = gift.image.url_list[0];
    } else if (gift.icon && gift.icon.url_list && gift.icon.url_list.length > 0) {
      imageUrl = gift.icon.url_list[0];
    } else if (gift.image && gift.image.uri) {
      // استخراج الصورة من URI مباشرة
      const uriBase = gift.image.uri;
      if (uriBase.includes('/')) {
        // بناء URL محتمل
        imageUrl = `https://p16-webcast.tiktokcdn.com/img/maliva/${uriBase}~tplv-obj.webp`;
      }
    }
    
    // إنشاء كائن الهدية المعالج
    return {
      id: gift.id,
      name: gift.name,
      description: gift.describe || '',
      diamondCost: gift.diamond_count,
      imageUrl: imageUrl
    };
  });
}

module.exports = {
  fetchGiftsList
}; 