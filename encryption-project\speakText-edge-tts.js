const { EdgeTTS } = require('node-edge-tts');
const fs = require('fs-extra');
const path = require('path');
const { exec } = require('child_process');
const { v4: uuidv4 } = require('uuid');
const os = require('os');
const Sound = require('sound-play');

// استيراد إعدادات TTS من index.js
let ttsSettings = null;

// الحصول على إعدادات TTS من index.js
function getTtsSettings() {
  if (!ttsSettings) {
    try {
      // محاولة الحصول على إعدادات TTS من index.js
      const indexModule = require('./index.js');
      if (indexModule.ttsSettings) {
        ttsSettings = indexModule.ttsSettings;
      } else {
        // إعدادات افتراضية إذا لم يتم العثور على إعدادات TTS في index.js
        ttsSettings = {
          enabled: true,
          voice: "ar-EG-SalmaNeural", // النموذج الافتراضي هو صوت سلمى باللغة العربية
          speed: 1.0,
          volume: 50,
          minLength: 5,
          maxLength: 500,
          readRate: 2, // قراءة تعليق واحد من كل X تعليقات
          blockedWords: []
        };
      }
    } catch (error) {
      console.error('خطأ في الحصول على إعدادات TTS من index.js:', error);
      // إعدادات افتراضية في حالة الخطأ
      ttsSettings = {
        enabled: true,
        voice: "ar-EG-SalmaNeural",
        speed: 1.0,
        volume: 50,
        minLength: 5,
        maxLength: 500,
        readRate: 2,
        blockedWords: []
      };
    }
  }
  return ttsSettings;
}

// وظيفة قراءة نص باستخدام Microsoft Edge TTS
async function speakText(text, callback) {
  // تأكد من أن callback هو دالة أو تعيين دالة فارغة
  const callbackFn = typeof callback === 'function' ? callback : () => {};

  // الحصول على إعدادات TTS الحالية
  const settings = getTtsSettings();

  if (!settings.enabled || !text || text.trim() === '') {
    callbackFn();
    return;
  }

  // التحقق من الطول
  if (text.length < settings.minLength || text.length > settings.maxLength) {
    callbackFn();
    return;
  }

  console.log(`قراءة النص: ${text}`);

  try {
    // استخدام Microsoft Edge TTS
    const voiceModel = settings.voice || "ar-EG-SalmaNeural";
    const tempAudioFile = path.join(os.tmpdir(), `tts-output-${uuidv4()}.mp3`);

    console.log(`جاري توليد الصوت باستخدام Microsoft Edge TTS...`);
    console.log(`الصوت المستخدم: ${voiceModel}`);
    console.log(`السرعة: ${settings.speed}, مستوى الصوت: ${settings.volume}%`);

    // حساب السرعة (من 0.5 إلى 2.0 → من -50% إلى +100%)
    const speedPercentage = Math.round((settings.speed - 1) * 100);
    const rateString = speedPercentage >= 0 ? `+${speedPercentage}%` : `${speedPercentage}%`;

    // حساب مستوى الصوت (من 1 إلى 100 → من -98% إلى +100%)
    const volumePercentage = Math.round(((settings.volume - 50) / 50) * 100);
    const volumeString = volumePercentage >= 0 ? `+${volumePercentage}%` : `${volumePercentage}%`;

    // إنشاء مثيل EdgeTTS مع الإعدادات المناسبة
    const tts = new EdgeTTS({
      voice: voiceModel,
      rate: rateString,
      volume: volumeString
    });

    console.log(`إعدادات EdgeTTS: voice=${voiceModel}, rate=${rateString}, volume=${volumeString}`);

    // توليد الصوت وحفظه في ملف مؤقت
    await tts.ttsPromise(text, tempAudioFile);

    console.log(`جاري تشغيل الملف الصوتي: ${tempAudioFile}`);

    try {
      // حساب مستوى الصوت (من 0 إلى 1)
      const volumeLevel = settings.volume / 100;
      console.log(`مستوى الصوت: ${settings.volume}%`);

      // استخدام مكتبة sound-play لتشغيل الملف الصوتي داخل البرنامج مع مستوى الصوت
      await Sound.play(tempAudioFile, volumeLevel);
      console.log(`تم تشغيل الصوت بنجاح بمستوى صوت: ${settings.volume}%`);

      // حذف الملف المؤقت بعد التشغيل
      setTimeout(() => {
        fs.unlink(tempAudioFile, (unlinkErr) => {
          if (unlinkErr) console.error(`خطأ في حذف الملف المؤقت: ${unlinkErr}`);
        });
        callbackFn();
      }, 1000); // انتظار ثانية واحدة للتأكد من انتهاء التشغيل
    } catch (err) {
      console.error(`خطأ في تشغيل الصوت: ${err}`);

      // محاولة بديلة باستخدام الطرق الأصلية
      try {
        // حساب مستوى الصوت (من 0 إلى 1)
        const volumeLevel = settings.volume / 100;

        if (process.platform === 'win32') {
          // Windows - لا يمكن تحديد مستوى الصوت مباشرة مع SoundPlayer
          exec(`powershell -c (New-Object Media.SoundPlayer "${tempAudioFile}").PlaySync()`);
        } else if (process.platform === 'darwin') {
          // macOS - يمكن تحديد مستوى الصوت مع afplay (من 0 إلى 1)
          exec(`afplay -v ${volumeLevel} "${tempAudioFile}"`);
        } else {
          // Linux - يمكن تحديد مستوى الصوت مع aplay
          exec(`aplay "${tempAudioFile}"`);
        }

        // حذف الملف المؤقت بعد التشغيل
        setTimeout(() => {
          fs.unlink(tempAudioFile, (unlinkErr) => {
            if (unlinkErr) console.error(`خطأ في حذف الملف المؤقت: ${unlinkErr}`);
          });
          callbackFn();
        }, 1000);
      } catch (fallbackError) {
        console.error(`خطأ في الطريقة البديلة لتشغيل الصوت: ${fallbackError}`);
        fs.unlink(tempAudioFile, (err) => {
          if (err) console.error(`خطأ في حذف الملف المؤقت: ${err}`);
        });
        callbackFn();
      }
    }
  } catch (e) {
    console.error(`خطأ عام في وظيفة قراءة النص: ${e}`);
    callbackFn();
  }
}

// وظيفة لتحديث إعدادات TTS
function updateTtsSettings(settings) {
  if (settings) {
    // الحصول على إعدادات TTS الحالية
    const currentSettings = getTtsSettings();

    // تحديث الإعدادات الفردية
    if (typeof settings.enabled !== 'undefined') currentSettings.enabled = Boolean(settings.enabled);
    if (settings.voice) currentSettings.voice = settings.voice;
    if (typeof settings.speed !== 'undefined') currentSettings.speed = parseFloat(settings.speed) || 1.0;
    if (typeof settings.volume !== 'undefined') currentSettings.volume = parseInt(settings.volume) || 50;
    if (typeof settings.minLength !== 'undefined') currentSettings.minLength = parseInt(settings.minLength) || 5;
    if (typeof settings.maxLength !== 'undefined') currentSettings.maxLength = parseInt(settings.maxLength) || 500;
    if (typeof settings.readRate !== 'undefined') currentSettings.readRate = parseInt(settings.readRate) || 2;
    if (Array.isArray(settings.blockedWords)) currentSettings.blockedWords = settings.blockedWords;

    // تحديث المتغير ttsSettings في ملف index.js
    try {
      const indexModule = require('./index.js');
      if (indexModule && indexModule.ttsSettings) {
        // نسخ الإعدادات المحدثة إلى متغير ttsSettings في index.js
        Object.assign(indexModule.ttsSettings, currentSettings);
      }
    } catch (error) {
      console.error('خطأ في تحديث إعدادات TTS في index.js:', error);
    }
  }
}

// تصدير الوظائف
module.exports = {
  speakText,
  updateTtsSettings
};
