/**
 * smooth-nav.js
 * سكريبت للتنقل السلس بين الصفحات
 */

document.addEventListener('DOMContentLoaded', function() {
  // إضافة فئة للشريط الجانبي لتفعيل تأثير النبض
  const sidebar = document.querySelector('.sidebar');
  if (sidebar) {
    sidebar.classList.add('sidebar-loaded');
  }
  
  // إعداد روابط التنقل
  setupNavigationLinks();
  
  // تحديث الروابط النشطة
  updateActiveLinks(window.location.pathname);
});

/**
 * إعداد روابط التنقل
 */
function setupNavigationLinks() {
  // تحديد جميع روابط التنقل في الشريط الجانبي
  const navLinks = document.querySelectorAll('.nav-menu a');
  
  navLinks.forEach(link => {
    // تجاهل الروابط التي تفتح في نافذة جديدة
    if (link.getAttribute('target') === '_blank') {
      return;
    }
    
    // إضافة مستمع أحداث للنقر
    link.addEventListener('click', handleLinkClick);
  });
}

/**
 * معالجة نقر الرابط
 * @param {Event} e - حدث النقر
 */
function handleLinkClick(e) {
  // الحصول على عنوان URL المستهدف
  const href = this.getAttribute('href');

  // تجاهل الروابط الخارجية أو الروابط التي تبدأ بـ #
  if (!href || href.startsWith('#') || href.startsWith('http') || href.startsWith('//')) {
    return;
  }

  // منع السلوك الافتراضي للرابط
  e.preventDefault();

  // تحديث الروابط النشطة
  updateActiveLinks(href);

  // تطبيق تأثير انتقال بسيط
  applyPageTransition(href);
}

/**
 * تطبيق تأثير انتقال بسيط
 * @param {string} href - الرابط المستهدف
 */
function applyPageTransition(href) {
  const mainContent = document.querySelector('.main-content');

  if (mainContent) {
    // تطبيق تأثير fade out على المحتوى الرئيسي فقط
    mainContent.style.transition = 'opacity 0.15s linear';
    mainContent.style.opacity = '0';
  }

  // الانتقال للصفحة الجديدة بعد التأثير
  setTimeout(() => {
    window.location.href = href;
  }, 150);
}

/**
 * تحديث الروابط النشطة في الشريط الجانبي
 * @param {string} url - عنوان URL النشط
 */
function updateActiveLinks(url) {
  // تحديد جميع روابط التنقل
  const navLinks = document.querySelectorAll('.nav-menu a');
  
  // إزالة الفئة النشطة من جميع الروابط
  navLinks.forEach(link => {
    link.classList.remove('active');
  });
  
  // إضافة الفئة النشطة للرابط المطابق
  navLinks.forEach(link => {
    const href = link.getAttribute('href');
    
    if (href === url || 
        (url === '/' && href === '/index.html') ||
        (url === '/connection.html' && href === '/') ||
        (href !== '/' && url.includes(href))) {
      link.classList.add('active');
    }
  });
}

// إضافة تأثير ظهور للصفحة عند تحميل الصفحة
window.addEventListener('load', function() {
  const mainContent = document.querySelector('.main-content');

  if (mainContent) {
    // تطبيق تأثير fade in على المحتوى الرئيسي فقط
    mainContent.style.opacity = '0';
    mainContent.style.transition = 'opacity 0.15s linear';

    // إظهار المحتوى تدريجياً
    setTimeout(() => {
      mainContent.style.opacity = '1';
    }, 50);

    // إزالة التأثيرات بعد الانتهاء
    setTimeout(() => {
      mainContent.style.transition = '';
    }, 300);
  }
});
