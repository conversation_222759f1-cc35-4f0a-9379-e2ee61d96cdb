// تطبيق وضع الليل/النهار المحفوظ فوراً قبل تحميل الصفحة
(function() {
  // التحقق من وجود تفضيل محفوظ للوضع
  const currentTheme = localStorage.getItem('theme') || 'light';
  document.documentElement.setAttribute('data-theme', currentTheme);
})();

// Dark Mode Toggle Script
document.addEventListener('DOMContentLoaded', function() {
  // Create dark mode toggle button
  const darkModeToggle = document.createElement('div');
  darkModeToggle.className = 'dark-mode-toggle';
  darkModeToggle.innerHTML = `
    <svg class="moon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
      <path d="M12,22C6.5,22,2,17.5,2,12S6.5,2,12,2s10,4.5,10,10S17.5,22,12,22z M12,4c-4.4,0-8,3.6-8,8s3.6,8,8,8s8-3.6,8-8S16.4,4,12,4z M12,17c-2.8,0-5-2.2-5-5s2.2-5,5-5s5,2.2,5,5S14.8,17,12,17z"/>
    </svg>
    <svg class="sun" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
      <path d="M12,17c-2.8,0-5-2.2-5-5s2.2-5,5-5s5,2.2,5,5S14.8,17,12,17z M12,9c-1.7,0-3,1.3-3,3s1.3,3,3,3s3-1.3,3-3S13.7,9,12,9z M12,4V2C12,1.4,11.6,1,11,1s-1,0.4-1,1v2c0,0.6,0.4,1,1,1S12,4.6,12,4z M12,20v2c0,0.6,0.4,1,1,1s1-0.4,1-1v-2c0-0.6-0.4-1-1-1S12,19.4,12,20z M5.6,6.6L4.2,5.2C3.8,4.8,3.2,4.8,2.8,5.2s-0.4,1,0,1.4l1.4,1.4c0.2,0.2,0.5,0.3,0.7,0.3s0.5-0.1,0.7-0.3C6,7.6,6,7,5.6,6.6z M19.8,18.8l-1.4-1.4c-0.4-0.4-1-0.4-1.4,0s-0.4,1,0,1.4l1.4,1.4c0.2,0.2,0.5,0.3,0.7,0.3s0.5-0.1,0.7-0.3C20.2,19.8,20.2,19.2,19.8,18.8z M3,13H1c-0.6,0-1,0.4-1,1s0.4,1,1,1h2c0.6,0,1-0.4,1-1S3.6,13,3,13z M23,13h-2c-0.6,0-1,0.4-1,1s0.4,1,1,1h2c0.6,0,1-0.4,1-1S23.6,13,23,13z M4.2,18.8l1.4-1.4c0.4-0.4,0.4-1,0-1.4s-1-0.4-1.4,0l-1.4,1.4c-0.4,0.4-0.4,1,0,1.4C3,19.2,3.8,19.2,4.2,18.8z M18.4,6.6l1.4-1.4c0.4-0.4,0.4-1,0-1.4s-1-0.4-1.4,0l-1.4,1.4c-0.4,0.4-0.4,1,0,1.4C17.4,7,18,7,18.4,6.6z"/>
    </svg>
  `;
  document.body.appendChild(darkModeToggle);

  // تحديث حالة الزر بناءً على الوضع الحالي
  const currentTheme = document.documentElement.getAttribute('data-theme');
  if (currentTheme === 'dark') {
    darkModeToggle.classList.add('active');
  }

  // Toggle theme when button is clicked
  darkModeToggle.addEventListener('click', function() {
    let theme = document.documentElement.getAttribute('data-theme');
    let newTheme = theme === 'light' ? 'dark' : 'light';

    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);

    // تحديث حالة الزر
    if (newTheme === 'dark') {
      darkModeToggle.classList.add('active');
    } else {
      darkModeToggle.classList.remove('active');
    }
  });
});
