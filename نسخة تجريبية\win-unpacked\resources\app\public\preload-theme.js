// تطبيق الوضع المحفوظ فوراً قبل تحميل أي شيء آخر
(function() {
  // التحقق من وجود تفضيل محفوظ للوضع
  const currentTheme = localStorage.getItem('theme') || 'light';
  document.documentElement.setAttribute('data-theme', currentTheme);
  
  // إضافة كود CSS مباشرة لمنع الوميض
  const style = document.createElement('style');
  style.textContent = `
    body {
      transition: none !important;
    }
    
    body.theme-ready {
      transition: background-color 0.3s ease, color 0.3s ease !important;
    }
  `;
  document.head.appendChild(style);
  
  // تعيين علامة الجاهزية بعد تحميل الصفحة
  window.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
      document.body.classList.add('theme-ready');
    }, 50);
  });
})();
