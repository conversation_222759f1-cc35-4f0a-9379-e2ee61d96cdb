<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>إدارة الاشتراكات</title>
  <!-- تحميل سكريبت الوضع المظلم قبل أي شيء آخر لمنع الوميض -->
  <script src="/preload-theme.js"></script>
  <link rel="stylesheet" href="/dark-mode.css">
  <link rel="stylesheet" href="/css/loading-indicator.css">
  <link rel="stylesheet" href="/css/sidebar-enhanced.css">
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">


  <style>
    :root {
      color-scheme: light dark;
    }
    body {
      font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--bg-gradient);
      color: var(--text-color);
      transition: background 0.3s ease, color 0.3s ease;
      min-height: 100vh;
      display: flex;
    }

    .sidebar {
      width: 250px;
      background: var(--sidebar-bg);
      box-shadow: 3px 0 15px var(--shadow-color);
      padding: 0;
      position: fixed;
      height: 100%;
      right: 0;
      top: 0;
      overflow-y: auto;
      z-index: 1000;
      border-left: 1px solid var(--sidebar-border);
      border-top-left-radius: 15px;
      border-bottom-left-radius: 15px;
      transition: background 0.3s ease, box-shadow 0.3s ease;
      opacity: 0.95 !important;
    }

    .logo {
      text-align: center;
      padding: 25px 15px;
      margin-bottom: 10px;
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      box-shadow: 0 4px 10px rgba(255, 59, 92, 0.2);
      position: relative;
      border-top-left-radius: 15px;
    }

    .logo h3 {
      color: white;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .logo::after {
      content: '';
      position: absolute;
      bottom: -10px;
      right: 50%;
      transform: translateX(50%);
      width: 20px;
      height: 20px;
      background: #ff3b5c;
      transform: rotate(45deg) translateX(50%);
      box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
      z-index: -1;
    }

    .nav-menu {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 20px 15px;
    }

    .nav-menu a {
      display: flex;
      align-items: center;
      padding: 14px 18px;
      color: var(--text-color);
      text-decoration: none;
      border-radius: 12px;
      transition: all 0.3s ease;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
      border: 1px solid var(--border-color);
    }

    .nav-menu a::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 3px;
      height: 100%;
      background: #ff3b5c;
      transform: scaleY(0);
      transition: transform 0.3s ease;
    }

    .nav-menu a:hover {
      background-color: var(--hover-bg);
      color: #ff3b5c;
      transform: translateY(-2px);
      box-shadow: 0 5px 10px var(--shadow-color);
    }

    .nav-menu a:hover::before {
      transform: scaleY(1);
    }

    .nav-menu a.active {
      background: linear-gradient(45deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      box-shadow: 0 5px 15px rgba(255, 59, 92, 0.25);
      border: none;
    }

    .nav-menu a.active::before {
      transform: scaleY(0);
    }

    .nav-icon {
      margin-left: 15px;
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .nav-menu a:hover .nav-icon {
      background-color: rgba(255, 59, 92, 0.1);
      transform: rotate(5deg);
    }

    .nav-menu a.active .nav-icon {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .main-content {
      flex: 1;
      margin-right: 250px;
      padding: 20px;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: var(--section-bg);
      border-radius: 15px;
      padding: 30px;
      box-shadow: 0 10px 30px var(--shadow-color);
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
      margin-top: 30px;
      margin-bottom: 30px;
      opacity: 0.95 !important;
    }

    h1 {
      color: var(--primary-color);
      text-align: center;
      font-size: 2.2rem;
      margin-bottom: 30px;
      font-weight: 700;
      border-bottom: 2px solid var(--border-color);
      padding-bottom: 15px;
    }

    .section {
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid var(--border-color);
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-secondary);
    }

    input, select, textarea {
      width: 100%;
      padding: 12px;
      border: 1px solid var(--input-border);
      border-radius: 8px;
      background-color: var(--input-bg);
      color: var(--text-color);
      transition: all 0.3s ease;
    }

    input:focus, select:focus, textarea:focus {
      border-color: #ff3b5c;
      outline: none;
      box-shadow: 0 0 0 3px rgba(255, 59, 92, 0.2);
    }

    button {
      background: var(--primary-gradient);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 12px 24px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 10px rgba(255, 59, 92, 0.2);
    }

    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(255, 59, 92, 0.3);
    }

    button:active {
      transform: translateY(0);
    }

    .btn-secondary {
      background: var(--secondary-gradient);
    }

    .subscription-card {
      background-color: var(--card-bg);
      border-radius: 10px;
      padding: 15px;
      margin-bottom: 15px;
      box-shadow: 0 4px 10px var(--shadow-color);
      transition: all 0.3s ease;
      border: 1px solid var(--border-color);
    }

    .subscription-card:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 15px var(--shadow-color);
    }

    .subscription-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }

    .subscription-title {
      font-weight: 600;
      font-size: 1.1rem;
      color: var(--text-color);
    }

    .subscription-price {
      background: linear-gradient(45deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      padding: 5px 12px;
      border-radius: 20px;
      font-weight: 500;
    }

    .subscription-details {
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px solid var(--border-color);
    }

    .subscription-feature {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
    }

    .feature-icon {
      margin-left: 8px;
      color: #28a745;
      font-weight: bold;
      font-size: 1.1em;
      text-shadow: 0 1px 2px rgba(40, 167, 69, 0.3);
    }

    .subscription-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 15px;
    }

    .status-badge {
      padding: 4px 10px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .status-active {
      background: linear-gradient(45deg, rgba(25, 135, 84, 0.3), rgba(40, 167, 69, 0.3));
      color: #198754;
      border: 2px solid #28a745;
      box-shadow: 0 0 15px rgba(40, 167, 69, 0.4);
      animation: glow-active 2s ease-in-out infinite alternate;
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    @keyframes glow-active {
      from {
        box-shadow: 0 0 15px rgba(40, 167, 69, 0.4);
      }
      to {
        box-shadow: 0 0 25px rgba(40, 167, 69, 0.6);
      }
    }

    .status-expired {
      background-color: rgba(220, 53, 69, 0.2);
      color: #dc3545;
    }

    .status-pending {
      background-color: rgba(255, 193, 7, 0.2);
      color: #ffc107;
    }



    .payment-methods {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin: 15px auto;
      max-width: 400px;
      align-items: center;
    }

    .payment-method {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      background: #f8f9fa;
      margin-bottom: 8px;
      width: 100%;
      max-width: 350px;
      justify-content: flex-start;
    }

    .payment-method:hover {
      border-color: #0070f3;
      background: #f0f8ff;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 112, 243, 0.1);
    }

    .payment-method.selected {
      border-color: #0070f3;
      background: #f0f8ff;
      box-shadow: 0 2px 8px rgba(0, 112, 243, 0.15);
    }

    .payment-method input[type="radio"] {
      margin: 0;
      width: 16px;
      height: 16px;
    }

    .payment-method-info {
      flex: 1;
    }

    .payment-method-name {
      font-weight: 600;
      font-size: 0.95rem;
      color: #2c3e50;
      margin-bottom: 2px;
    }

    .payment-method-desc {
      font-size: 0.8rem;
      color: #6c757d;
      line-height: 1.2;
    }

    .payment-processing {
      text-align: center;
      padding: 15px;
      background: rgba(0, 123, 255, 0.1);
      border-radius: 6px;
      margin: 15px auto;
      max-width: 350px;
      display: none;
    }

    .payment-success {
      text-align: center;
      padding: 15px;
      background: rgba(40, 167, 69, 0.1);
      border-radius: 6px;
      margin: 15px auto;
      max-width: 350px;
      display: none;
    }

    .payment-error {
      text-align: center;
      padding: 15px;
      background: rgba(220, 53, 69, 0.1);
      border-radius: 6px;
      margin: 15px auto;
      max-width: 350px;
      display: none;
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="logo">
      <h3>StreamTok</h3>
    </div>
    <div class="nav-menu">
      <a href="/">
        <span class="nav-icon">🔌</span>
        الاتصال بـ TikTok Live
      </a>
      <a href="/mappings.html">
        <span class="nav-icon">🛠️</span>
        ربط الهدايا بالإجراءات
      </a>
      <a href="/tts-comments.html">
        <span class="nav-icon">🔊</span>
        قراءة التعليقات
      </a>
      <a href="/games.html">
        <span class="nav-icon">🎯</span>
        الألعاب
      </a>
      <a href="/settings.html">
        <span class="nav-icon">⚙️</span>
        الإعدادات
      </a>
      <a href="/profiles.html">
        <span class="nav-icon">👤</span>
        الملفات الشخصية
      </a>
      <a href="/subscriptions.html" class="active">
        <span class="nav-icon">⚡</span>
        الاشتراكات
      </a>
      <a href="/contact.html">
        <span class="nav-icon">📞</span>
        اتصل بنا
      </a>
      <a href="/overlay.html" target="_blank">
        <span class="nav-icon">🖥️</span>
        فتح Overlay
      </a>
      <a href="/overlay-bar.html">
        <span class="nav-icon">🎮</span>
        Overlay Bar
      </a>
    </div>
  </div>

  <div class="main-content">
    <div class="container">
      <h1>إدارة الاشتراكات</h1>

      <!-- مؤشر التحميل -->
      <div class="loading-indicator" style="display: none; text-align: center; padding: 40px;">
        <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #ff3b5c; border-radius: 50%; animation: spin 1s linear infinite;"></div>
        <p style="margin-top: 15px; color: var(--text-secondary);">جاري تحميل بيانات الاشتراك...</p>
      </div>

      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>

      <div class="section">
        <h3>حالة الاشتراك الحالي</h3>
        <div class="subscription-card">
          <div class="subscription-header">
            <div class="subscription-title">لا يوجد اشتراك نشط</div>
            <div class="status-badge status-expired">غير مشترك</div>
          </div>
          <div>يرجى اختيار باقة اشتراك</div>
          <div class="subscription-details">
            <div class="subscription-feature">
              <span class="feature-icon">❌</span>
              <span>الميزات الأساسية فقط</span>
            </div>
          </div>
          <div class="subscription-actions">
            <!-- PayPal Subscription Button Container -->
            <div id="paypal-subscription-container" style="margin: 20px 0;">
              <div id="paypal-button-container" style="max-width: 400px; margin: 0 auto;"></div>
            </div>

            <div class="payment-methods">
              <div class="payment-method selected" data-method="paypal">
                <input type="radio" name="payment-current" value="paypal" checked>
                <div class="payment-method-info">
                  <div class="payment-method-name">💳 PayPal</div>
                  <div class="payment-method-desc">دفع آمن عبر PayPal - اشتراك شهري</div>
                </div>
              </div>
            </div>
            <button class="btn btn-primary w-100 mt-3" onclick="purchaseSubscription('comprehensive')" style="display: none;">
              اشترك الآن - $10.00
            </button>
            <div class="payment-processing" id="payment-processing-current">
              <div>🔄 جاري معالجة الدفع...</div>
            </div>
            <div class="payment-success" id="payment-success-current">
              <div>✅ تم الدفع بنجاح! جاري تفعيل الاشتراك...</div>
            </div>
            <div class="payment-error" id="payment-error-current">
              <div>❌ فشل في معالجة الدفع. يرجى المحاولة مرة أخرى.</div>
            </div>
          </div>
        </div>
      </div>

      <div class="section">
        <h3>باقة الاشتراك المتاحة</h3>

        <div class="subscription-card">
          <div class="subscription-header">
            <div class="subscription-title">الباقة الشاملة</div>
            <div class="subscription-price">$10.00 / شهريًا</div>
          </div>
          <div class="subscription-details">
            <div class="subscription-feature">
              <span class="feature-icon">✓</span>
              <span>اتصال بـ TikTok Live</span>
            </div>
            <div class="subscription-feature">
              <span class="feature-icon">✓</span>
              <span>عرض التعليقات والهدايا</span>
            </div>
            <div class="subscription-feature">
              <span class="feature-icon">✓</span>
              <span>دعم الألعاب والتفاعلات المتقدمة</span>
            </div>
            <div class="subscription-feature">
              <span class="feature-icon">✓</span>
              <span>تخصيص كامل للواجهة</span>
            </div>
            <div class="subscription-feature">
              <span class="feature-icon">✓</span>
              <span>دعم فني على مدار الساعة</span>
            </div>
          </div>
          <div class="subscription-actions">
            <div class="payment-methods">
              <div class="payment-method selected" data-method="paypal">
                <input type="radio" name="payment-new" value="paypal" checked>
                <div class="payment-method-info">
                  <div class="payment-method-name">💳 PayPal</div>
                  <div class="payment-method-desc">دفع آمن عبر PayPal - اشتراك شهري</div>
                </div>
              </div>
            </div>
            <!-- PayPal Subscription Button Container -->
            <div id="paypal-subscription-container-new" style="margin: 20px 0;">
              <div id="paypal-button-container-new" style="max-width: 400px; margin: 0 auto;"></div>
            </div>

            <button class="btn btn-primary w-100 mt-3" onclick="purchaseSubscription('comprehensive')" style="display: none;">
              اشترك الآن - $10.00
            </button>
            <div class="payment-processing" id="payment-processing-new">
              <div>🔄 جاري معالجة الدفع...</div>
            </div>
            <div class="payment-success" id="payment-success-new">
              <div>✅ تم الدفع بنجاح! جاري تفعيل الاشتراك...</div>
            </div>
            <div class="payment-error" id="payment-error-new">
              <div>❌ فشل في معالجة الدفع. يرجى المحاولة مرة أخرى.</div>
            </div>
          </div>
        </div>
      </div>

      <div class="section">
        <h3>سجل الاشتراكات</h3>
        <div id="subscription-history-loading" class="text-center" style="display: none;">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
          </div>
          <p class="mt-2">جاري تحميل سجل الاشتراكات...</p>
        </div>
        <div id="subscription-history-content">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>الباقة</th>
                <th>طريقة الدفع</th>
                <th>تاريخ البدء</th>
                <th>تاريخ الانتهاء</th>
                <th>المبلغ</th>
                <th>الحالة</th>
                <th>التفاصيل</th>
              </tr>
            </thead>
            <tbody id="subscription-history-tbody">
              <tr>
                <td colspan="7" class="text-center text-muted">
                  <i class="fas fa-inbox"></i>
                  <p class="mt-2">لا توجد اشتراكات بعد</p>
                  <small>ستظهر اشتراكاتك هنا بعد الدفع</small>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="/dark-mode.js"></script>

  <script type="module" src="/js/firebase-config.js"></script>
  <!-- تم إزالة auth-guard لحل مشكلة التضارب -->
  <script type="module">
    // نظام حماية بسيط للصفحة
    let authCheckDone = false;
    let hasRedirected = false;

    document.addEventListener('DOMContentLoaded', function() {
      // انتظار تحميل Firebase
      setTimeout(initializeSubscriptionsPage, 500);
    });

    // التحقق من المصادقة
    window.addEventListener('authStateChanged', (event) => {
      if (authCheckDone || hasRedirected) return;

      const user = event.detail.user;
      console.log('🔐 Subscriptions page - Auth state:', user?.email);

      if (!user) {
        console.log('❌ No user, redirecting to auth');
        hasRedirected = true;
        window.location.href = '/auth.html';
      } else if (!user.emailVerified) {
        console.log('❌ Email not verified, redirecting to verification');
        hasRedirected = true;
        window.location.href = '/email-verification.html';
      } else {
        console.log('✅ User authenticated and verified');
        authCheckDone = true;
        // المتابعة مع تحميل الصفحة
        addLogoutButton();

        // تحميل بيانات الاشتراك وسجل الاشتراكات
        setTimeout(() => {
          fetchSubscriptionData();
          fetchSubscriptionHistory();
        }, 500);
      }
    });

    function initializeSubscriptionsPage() {
      // تحديد الرابط النشط في القائمة الجانبية
      const currentPath = window.location.pathname;
      const navLinks = document.querySelectorAll('.nav-menu a');

      navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
          link.classList.add('active');
        } else {
          link.classList.remove('active');
        }
      });

      // التحقق من تسجيل الدخول
      if (!window.firebaseAuth?.currentUser) {
        showLoginRequired();
        return;
      }

      // بيانات الاشتراك سيتم تحميلها من خلال authStateChanged



      // تم نقل الاستماع لتغييرات المصادقة إلى أعلى الصفحة
    }

    // استرجاع بيانات الاشتراك من Firebase
    async function fetchSubscriptionData() {
      console.log('🔄 fetchSubscriptionData started');

      try {
        const currentUser = window.firebaseAuth?.currentUser;
        if (!currentUser) {
          console.log('❌ No current user');
          showLoginRequired();
          return;
        }

        console.log('✅ Current user:', currentUser.email);

        // عرض مؤشر التحميل
        showLoading(true);

        // استرجاع بيانات الاشتراك من Firebase
        console.log('🔄 Getting user subscription...');
        const subscription = await window.firebaseHelpers.getUserSubscription(currentUser.uid);

        if (subscription) {
          console.log('✅ Subscription found:', subscription);
          updateSubscriptionUI(subscription);
        } else {
          console.log('ℹ️ No subscription found, creating user profile...');
          // لا يوجد اشتراك نشط - إنشاء ملف المستخدم إذا لم يكن موجود
          await ensureUserProfile(currentUser);

          updateSubscriptionUI({
            status: 'none',
            plan: 'none',
            expiry_date: null,
            features: []
          });
        }

        console.log('✅ fetchSubscriptionData completed successfully');

      } catch (error) {
        console.error('❌ خطأ في استرجاع بيانات الاشتراك:', error);

        if (error.message.includes('index') || error.message.includes('requires an index')) {
          showError(`
            خطأ في قاعدة البيانات: يتطلب إنشاء فهرس.<br>
            <a href="/setup-firestore.html" target="_blank">اضغط هنا لإعداد قاعدة البيانات</a><br>
            أو اتبع الرابط في Console المتصفح لإنشاء الفهرس تلقائياً.
          `);
        } else if (error.message.includes('permission') || error.message.includes('insufficient')) {
          showError(`
            خطأ في الأذونات: يرجى التحقق من قواعد Firestore.<br>
            <a href="/setup-firestore.html" target="_blank">اضغط هنا لإعداد قاعدة البيانات</a>
          `);
        } else {
          showError('حدث خطأ أثناء استرجاع بيانات الاشتراك. يرجى المحاولة مرة أخرى لاحقًا.');
        }
      } finally {
        // التأكد من إخفاء مؤشر التحميل في جميع الحالات
        console.log('🔄 Hiding loading indicator');
        showLoading(false);
      }
    }

    // تحديث واجهة المستخدم بناءً على بيانات الاشتراك
    function updateSubscriptionUI(data) {
      const currentSubscription = document.querySelector('.section .subscription-card');
      if (!currentSubscription) return;

      const titleElement = currentSubscription.querySelector('.subscription-title');
      const statusElement = currentSubscription.querySelector('.status-badge');
      const expiryElement = currentSubscription.querySelector('div:nth-child(2)');
      const featuresContainer = currentSubscription.querySelector('.subscription-details');

      if (data.status === 'none' || !data.plan || data.plan === 'none') {
        // لا يوجد اشتراك
        titleElement.textContent = 'لا يوجد اشتراك نشط';
        statusElement.textContent = 'غير مشترك';
        statusElement.className = 'status-badge status-expired';
        expiryElement.textContent = 'يرجى اختيار باقة اشتراك';

        featuresContainer.innerHTML = `
          <div class="subscription-feature">
            <span class="feature-icon">❌</span>
            <span>الميزات الأساسية فقط</span>
          </div>
        `;
      } else {
        // يوجد اشتراك نشط
        if (data.plan === 'comprehensive' || data.plan === 'premium') {
          titleElement.textContent = '✨ الباقة الشاملة';
          titleElement.style.color = '#28a745';
          titleElement.style.fontWeight = '700';
          titleElement.style.textShadow = '0 1px 3px rgba(40, 167, 69, 0.3)';
        } else {
          // للتوافق مع البيانات القديمة
          titleElement.textContent = '✨ الباقة الشاملة';
          titleElement.style.color = '#28a745';
          titleElement.style.fontWeight = '700';
          titleElement.style.textShadow = '0 1px 3px rgba(40, 167, 69, 0.3)';
        }

        // تحديث حالة الاشتراك
        const isActive = data.status === 'active' && new Date() < (data.endDate?.toDate ? data.endDate.toDate() : new Date(data.endDate));
        statusElement.textContent = isActive ? 'نشط' : 'منتهي';
        statusElement.className = 'status-badge ' + (isActive ? 'status-active' : 'status-expired');

        // تحديث تاريخ الانتهاء
        if (data.endDate) {
          const expiryDate = data.endDate.toDate ? data.endDate.toDate() : new Date(data.endDate);
          expiryElement.textContent = `تاريخ الانتهاء: ${expiryDate.toLocaleDateString('ar-EG')}`;
        }

        // تحديث الميزات
        if (data.features && Array.isArray(data.features)) {
          featuresContainer.innerHTML = '';
          data.features.forEach(feature => {
            featuresContainer.innerHTML += `
              <div class="subscription-feature">
                <span class="feature-icon" style="color: #28a745; font-weight: bold;">✓</span>
                <span>${feature}</span>
              </div>
            `;
          });
        }

        // إخفاء خيارات الدفع للمشتركين النشطين وإضافة تأثير مضيء
        if (isActive) {
          hidePaymentOptions();
          // إضافة تأثير مضيء لبطاقة الاشتراك النشط
          const subscriptionCard = document.querySelector('.section .subscription-card');
          if (subscriptionCard) {
            subscriptionCard.style.border = '2px solid #28a745';
            subscriptionCard.style.boxShadow = '0 0 20px rgba(40, 167, 69, 0.3)';
            subscriptionCard.style.background = 'linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05))';
          }
        } else {
          showPaymentOptions();
          // إزالة التأثير المضيء للمستخدمين غير المشتركين
          const subscriptionCard = document.querySelector('.section .subscription-card');
          if (subscriptionCard) {
            subscriptionCard.style.border = '';
            subscriptionCard.style.boxShadow = '';
            subscriptionCard.style.background = '';
          }
        }
      }
    }

    // إخفاء خيارات الدفع من قسم "حالة الاشتراك الحالي" فقط
    function hidePaymentOptions() {
      const currentPaymentMethods = document.querySelector('.section:first-child .payment-methods');
      const currentPaypalContainer = document.querySelector('#paypal-subscription-container');
      const currentPurchaseButton = document.querySelector('.section:first-child button[onclick*="purchaseSubscription"]');

      if (currentPaymentMethods) currentPaymentMethods.style.display = 'none';
      if (currentPaypalContainer) currentPaypalContainer.style.display = 'none';
      if (currentPurchaseButton) currentPurchaseButton.style.display = 'none';
    }

    // إظهار خيارات الدفع في قسم "حالة الاشتراك الحالي"
    function showPaymentOptions() {
      const currentPaymentMethods = document.querySelector('.section:first-child .payment-methods');
      const currentPaypalContainer = document.querySelector('#paypal-subscription-container');
      const currentPurchaseButton = document.querySelector('.section:first-child button[onclick*="purchaseSubscription"]');

      if (currentPaymentMethods) currentPaymentMethods.style.display = 'flex';
      if (currentPaypalContainer) currentPaypalContainer.style.display = 'block';
      if (currentPurchaseButton) currentPurchaseButton.style.display = 'block';
    }

    // تحميل سجل الاشتراكات من Firebase
    async function fetchSubscriptionHistory() {
      console.log('🔄 fetchSubscriptionHistory started');

      try {
        const currentUser = window.firebaseAuth?.currentUser;
        if (!currentUser) {
          console.log('❌ No current user for subscription history');
          return;
        }

        // عرض مؤشر التحميل
        showSubscriptionHistoryLoading(true);

        // استرجاع جميع اشتراكات المستخدم من Firebase
        console.log('🔄 Getting user subscription history...');
        const subscriptions = await window.firebaseHelpers.getUserSubscriptionHistory(currentUser.uid);

        if (subscriptions && subscriptions.length > 0) {
          console.log('✅ Subscription history found:', subscriptions);
          updateSubscriptionHistoryUI(subscriptions);
        } else {
          console.log('ℹ️ No subscription history found');
          updateSubscriptionHistoryUI([]);
        }

        console.log('✅ fetchSubscriptionHistory completed successfully');

      } catch (error) {
        console.error('❌ خطأ في استرجاع سجل الاشتراكات:', error);
        showSubscriptionHistoryError('حدث خطأ أثناء تحميل سجل الاشتراكات');
      } finally {
        showSubscriptionHistoryLoading(false);
      }
    }

    // تحديث واجهة سجل الاشتراكات
    function updateSubscriptionHistoryUI(subscriptions) {
      const tbody = document.getElementById('subscription-history-tbody');
      if (!tbody) return;

      if (!subscriptions || subscriptions.length === 0) {
        tbody.innerHTML = `
          <tr>
            <td colspan="7" class="text-center text-muted">
              <i class="fas fa-inbox"></i>
              <p class="mt-2">لا توجد اشتراكات بعد</p>
              <small>ستظهر اشتراكاتك هنا بعد الدفع</small>
            </td>
          </tr>
        `;
        return;
      }

      // ترتيب الاشتراكات حسب التاريخ (الأحدث أولاً)
      subscriptions.sort((a, b) => {
        const dateA = a.createdAt?.toDate ? a.createdAt.toDate() : new Date(a.createdAt);
        const dateB = b.createdAt?.toDate ? b.createdAt.toDate() : new Date(b.createdAt);
        return dateB - dateA;
      });

      tbody.innerHTML = '';
      subscriptions.forEach(subscription => {
        const row = createSubscriptionHistoryRow(subscription);
        tbody.appendChild(row);
      });
    }

    // إنشاء صف في جدول سجل الاشتراكات
    function createSubscriptionHistoryRow(subscription) {
      const row = document.createElement('tr');

      // تحديد اسم الباقة
      const planName = subscription.plan === 'comprehensive' ? 'الباقة الشاملة' : subscription.plan || 'غير محدد';

      // تحديد طريقة الدفع مع أيقونة
      let paymentMethod = 'دفع يدوي';
      let paymentIcon = '💰';

      // تحديد التواريخ
      const startDate = subscription.startDate?.toDate ?
        subscription.startDate.toDate().toLocaleDateString('ar-EG') :
        (subscription.startDate ? new Date(subscription.startDate).toLocaleDateString('ar-EG') : 'غير محدد');

      const endDate = subscription.endDate?.toDate ?
        subscription.endDate.toDate().toLocaleDateString('ar-EG') :
        (subscription.endDate ? new Date(subscription.endDate).toLocaleDateString('ar-EG') : 'غير محدد');

      // تحديد المبلغ
      const price = subscription.price || '$10.00';

      // تحديد الحالة
      const isActive = subscription.status === 'active' &&
        new Date() < (subscription.endDate?.toDate ? subscription.endDate.toDate() : new Date(subscription.endDate));
      const statusText = isActive ? 'نشط' : (subscription.status === 'active' ? 'منتهي' : subscription.status || 'غير محدد');
      const statusClass = isActive ? 'status-active' : 'status-expired';

      // إنشاء زر التفاصيل
      const detailsButton = `
        <button class="btn btn-sm btn-outline-primary" onclick="showSubscriptionDetails('${subscription.id || 'unknown'}')">
          <i class="fas fa-eye"></i> عرض
        </button>
      `;

      row.innerHTML = `
        <td><strong>${planName}</strong></td>
        <td>${paymentIcon} ${paymentMethod}</td>
        <td>${startDate}</td>
        <td>${endDate}</td>
        <td><strong>${price}</strong></td>
        <td><span class="status-badge ${statusClass}">${statusText}</span></td>
        <td>${detailsButton}</td>
      `;

      return row;
    }

    // عرض تفاصيل اشتراك معين
    function showSubscriptionDetails(subscriptionId) {
      // البحث عن الاشتراك في البيانات المحملة
      const currentUser = window.firebaseAuth?.currentUser;
      if (!currentUser) return;

      // هنا يمكن إضافة modal أو صفحة منفصلة لعرض التفاصيل
      console.log('عرض تفاصيل الاشتراك:', subscriptionId);

      // مؤقتاً: عرض alert بالمعلومات الأساسية
      alert(`تفاصيل الاشتراك:\nمعرف الاشتراك: ${subscriptionId}\n\nسيتم إضافة المزيد من التفاصيل قريباً.`);
    }

    // عرض/إخفاء مؤشر تحميل سجل الاشتراكات
    function showSubscriptionHistoryLoading(show) {
      const loadingElement = document.getElementById('subscription-history-loading');
      const contentElement = document.getElementById('subscription-history-content');

      if (loadingElement && contentElement) {
        loadingElement.style.display = show ? 'block' : 'none';
        contentElement.style.display = show ? 'none' : 'block';
      }
    }

    // عرض خطأ في سجل الاشتراكات
    function showSubscriptionHistoryError(message) {
      const tbody = document.getElementById('subscription-history-tbody');
      if (tbody) {
        tbody.innerHTML = `
          <tr>
            <td colspan="7" class="text-center text-danger">
              <i class="fas fa-exclamation-triangle"></i>
              <p class="mt-2">${message}</p>
              <button class="btn btn-sm btn-outline-primary" onclick="fetchSubscriptionHistory()">
                <i class="fas fa-redo"></i> إعادة المحاولة
              </button>
            </td>
          </tr>
        `;
      }
    }











    // عرض حالة معالجة الدفع
    function showPaymentProcessing(type, show) {
      const element = document.getElementById(`payment-processing-${type}`);
      if (element) {
        element.style.display = show ? 'block' : 'none';
      }
    }

    // عرض نجاح الدفع
    function showPaymentSuccess(type, show) {
      const element = document.getElementById(`payment-success-${type}`);
      if (element) {
        element.style.display = show ? 'block' : 'none';
      }
    }

    // عرض خطأ الدفع
    function showPaymentError(type, message) {
      const element = document.getElementById(`payment-error-${type}`);
      if (element) {
        element.querySelector('div').textContent = `❌ ${message}`;
        element.style.display = 'block';

        // إخفاء الرسالة بعد 5 ثوان
        setTimeout(() => {
          element.style.display = 'none';
        }, 5000);
      }

      // إخفاء حالة المعالجة
      showPaymentProcessing(type, false);
    }

    // إرسال طلب شراء اشتراك (للتوافق مع الكود القديم)
    async function purchaseSubscription(plan) {
      try {
        const currentUser = window.firebaseAuth?.currentUser;
        if (!currentUser) {
          showError('يجب تسجيل الدخول أولاً');
          return;
        }

        // تأكيد الشراء
        const planDetails = getPlanDetails(plan);
        if (!confirm(`هل تريد شراء ${planDetails.name} مقابل ${planDetails.price}؟`)) {
          return;
        }

        showLoading(true);

        // إنشاء الاشتراك في Firebase
        const subscriptionData = await window.firebaseHelpers.createSubscription(currentUser.uid, planDetails);

        if (subscriptionData) {
          showSuccess(`تم شراء ${planDetails.name} بنجاح!`);

          // تحديث بيانات الاشتراك
          setTimeout(() => {
            fetchSubscriptionData();
            showLoading(false);
          }, 1500);
        } else {
          throw new Error('فشل في إنشاء الاشتراك');
        }

      } catch (error) {
        console.error('خطأ في شراء الاشتراك:', error);
        showLoading(false);
        showError('حدث خطأ أثناء معالجة طلب الاشتراك. يرجى المحاولة مرة أخرى لاحقًا.');
      }
    }

    // الحصول على تفاصيل الباقة
    function getPlanDetails(plan) {
      const plans = {
        comprehensive: {
          name: 'الباقة الشاملة',
          price: '$10.00 / شهرياً',
          durationDays: 30,
          features: [
            'اتصال بـ TikTok Live',
            'عرض التعليقات والهدايا',
            'دعم الألعاب والتفاعلات المتقدمة',
            'تخصيص كامل للواجهة',
            'دعم فني على مدار الساعة'
          ]
        }
      };

      // إذا لم يتم تحديد نوع الباقة، استخدم الباقة الشاملة كافتراضي
      const selectedPlan = plan || 'comprehensive';
      return { plan: selectedPlan, ...plans[selectedPlan] };
    }

    // دوال مساعدة للواجهة
    function showLoading(show) {
      console.log('🔄 showLoading called with:', show);

      // البحث عن مؤشرات التحميل الموجودة
      const loadingElements = document.querySelectorAll('.loading-indicator, .loading');
      loadingElements.forEach(el => {
        el.style.display = show ? 'block' : 'none';
      });



      // إخفاء/إظهار محتوى الصفحة
      const sections = document.querySelectorAll('.section');
      sections.forEach(section => {
        if (show) {
          section.style.opacity = '0.5';
          section.style.pointerEvents = 'none';
        } else {
          section.style.opacity = '1';
          section.style.pointerEvents = 'auto';
        }
      });
    }

    function showError(message) {
      // إنشاء عنصر رسالة الخطأ إذا لم يكن موجوداً
      let errorDiv = document.querySelector('.error-message');
      if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.cssText = `
          background: rgba(220, 53, 69, 0.1);
          color: #dc3545;
          padding: 12px;
          border-radius: 8px;
          margin: 20px 0;
          border: 1px solid rgba(220, 53, 69, 0.2);
          text-align: center;
        `;
        document.querySelector('.container').insertBefore(errorDiv, document.querySelector('.section'));
      }

      errorDiv.innerHTML = message;
      errorDiv.style.display = 'block';

      // إخفاء الرسالة بعد 5 ثوان
      setTimeout(() => {
        errorDiv.style.display = 'none';
      }, 5000);
    }

    function showSuccess(message) {
      // إنشاء عنصر رسالة النجاح إذا لم يكن موجوداً
      let successDiv = document.querySelector('.success-message');
      if (!successDiv) {
        successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.style.cssText = `
          background: rgba(25, 135, 84, 0.1);
          color: #198754;
          padding: 12px;
          border-radius: 8px;
          margin: 20px 0;
          border: 1px solid rgba(25, 135, 84, 0.2);
          text-align: center;
        `;
        document.querySelector('.container').insertBefore(successDiv, document.querySelector('.section'));
      }

      successDiv.textContent = message;
      successDiv.style.display = 'block';

      // إخفاء الرسالة بعد 3 ثوان
      setTimeout(() => {
        successDiv.style.display = 'none';
      }, 3000);
    }

    function showLoginRequired() {
      const container = document.querySelector('.container');
      container.innerHTML = `
        <h1>تسجيل الدخول مطلوب</h1>
        <div style="text-align: center; padding: 40px;">
          <p style="font-size: 1.2rem; margin-bottom: 30px;">
            يجب تسجيل الدخول للوصول إلى صفحة الاشتراكات
          </p>
          <a href="/auth.html" style="
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(45deg, #ff3b5c 0%, #ff6b87 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
          ">تسجيل الدخول</a>
        </div>
      `;
    }

    // التأكد من وجود ملف المستخدم
    async function ensureUserProfile(user) {
      try {
        const userData = await window.firebaseHelpers.getUserData(user.uid);
        if (!userData) {
          // إنشاء ملف المستخدم إذا لم يكن موجود
          await window.firebaseHelpers.createUserProfile(user.uid, {
            name: user.displayName || user.email,
            email: user.email,
            subscriptionStatus: 'none',
            emailVerified: user.emailVerified
          });
          console.log('✅ تم إنشاء ملف المستخدم');
        }
      } catch (error) {
        console.error('Error ensuring user profile:', error);
      }
    }

    // إضافة زر تسجيل الخروج
    function addLogoutButton() {
      const sidebar = document.querySelector('.nav-menu');
      if (sidebar && !sidebar.querySelector('.logout-link')) {
        const logoutLink = document.createElement('a');
        logoutLink.href = '#';
        logoutLink.className = 'logout-link';
        logoutLink.innerHTML = `
          <span class="nav-icon">🚪</span>
          تسجيل الخروج
        `;
        logoutLink.addEventListener('click', async (e) => {
          e.preventDefault();
          try {
            await window.firebaseFunctions.signOut(window.firebaseAuth);
            window.location.href = '/auth.html';
          } catch (error) {
            console.error('Logout error:', error);
          }
        });
        sidebar.appendChild(logoutLink);
      }
    }
  </script>

  <!-- PayPal Integration -->
  <script>
    // PayPal Configuration
    let paypalConfig = null;
    let currentUser = null;

    // Load PayPal configuration from server
    async function loadPayPalConfig() {
      try {
        const response = await fetch('/api/paypal/config');
        const data = await response.json();

        if (data.success) {
          paypalConfig = data;
          console.log('PayPal config loaded:', paypalConfig);

          // Load PayPal SDK dynamically
          loadPayPalSDK();
        } else {
          console.error('Failed to load PayPal config:', data.error);
        }
      } catch (error) {
        console.error('Error loading PayPal config:', error);
      }
    }

    // Load PayPal SDK
    function loadPayPalSDK() {
      if (!paypalConfig || !paypalConfig.client_id) {
        console.error('PayPal config not available');
        return;
      }

      const script = document.createElement('script');
      script.src = `https://www.paypal.com/sdk/js?client-id=${paypalConfig.client_id}&vault=true&intent=subscription`;
      script.onload = initializePayPalButtons;
      script.onerror = () => {
        console.error('Failed to load PayPal SDK');
      };
      document.head.appendChild(script);
    }

    // Initialize PayPal buttons
    function initializePayPalButtons() {
      if (!window.paypal || !paypalConfig) {
        console.error('PayPal SDK or config not available');
        return;
      }

      console.log('Initializing PayPal buttons...');

      window.paypal.Buttons({
        style: {
          shape: 'rect',
          color: 'gold',
          layout: 'vertical',
          label: 'subscribe',
          height: 40
        },
        createSubscription: function(data, actions) {
          console.log('Creating subscription with plan ID:', paypalConfig.plan_id);
          return actions.subscription.create({
            plan_id: paypalConfig.plan_id
          });
        },
        onApprove: async function(data, actions) {
          console.log('Subscription approved:', data);

          try {
            // Show loading
            showPaymentProcessing('current', true);

            // Verify subscription with our server
            const verifyResponse = await fetch('/api/paypal/verify-subscription', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                subscriptionId: data.subscriptionID,
                userId: currentUser?.uid
              })
            });

            const verifyData = await verifyResponse.json();

            if (verifyData.success) {
              // Save subscription to Firebase
              await saveSubscriptionToFirebase(data.subscriptionID, verifyData.subscription);

              // Show success message
              showPaymentSuccess('current', true);

              // Reload subscription status
              setTimeout(() => {
                loadUserSubscription();
              }, 2000);

            } else {
              throw new Error(verifyData.error || 'Failed to verify subscription');
            }

          } catch (error) {
            console.error('Error processing subscription:', error);
            showPaymentError('current', error.message);
          }
        },
        onError: function(err) {
          console.error('PayPal error:', err);
          showPaymentError('current', 'حدث خطأ في معالجة الدفع. يرجى المحاولة مرة أخرى.');
        },
        onCancel: function(data) {
          console.log('Payment cancelled:', data);
          showPaymentError('current', 'تم إلغاء عملية الدفع.');
        }
      }).render('#paypal-button-container');

      // Render PayPal button for new subscription section too
      if (document.getElementById('paypal-button-container-new')) {
        window.paypal.Buttons({
          style: {
            shape: 'rect',
            color: 'blue',
            layout: 'vertical',
            label: 'subscribe',
            height: 40
          },
          createSubscription: function(data, actions) {
            console.log('Creating subscription with plan ID:', paypalConfig.plan_id);
            return actions.subscription.create({
              plan_id: paypalConfig.plan_id
            });
          },
          onApprove: async function(data, actions) {
            console.log('Subscription approved:', data);

            try {
              // Show loading
              showPaymentProcessing('new', true);

              // Verify subscription with our server
              const verifyResponse = await fetch('/api/paypal/verify-subscription', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  subscriptionId: data.subscriptionID,
                  userId: currentUser?.uid
                })
              });

              const verifyData = await verifyResponse.json();

              if (verifyData.success) {
                // Save subscription to Firebase
                await saveSubscriptionToFirebase(data.subscriptionID, verifyData.subscription);

                // Show success message
                showPaymentSuccess('new', true);

                // Reload subscription status
                setTimeout(() => {
                  loadUserSubscription();
                }, 2000);

              } else {
                throw new Error(verifyData.error || 'Failed to verify subscription');
              }

            } catch (error) {
              console.error('Error processing subscription:', error);
              showPaymentError('new', error.message);
            }
          },
          onError: function(err) {
            console.error('PayPal error:', err);
            showPaymentError('new', 'حدث خطأ في معالجة الدفع. يرجى المحاولة مرة أخرى.');
          },
          onCancel: function(data) {
            console.log('Payment cancelled:', data);
            showPaymentError('new', 'تم إلغاء عملية الدفع.');
          }
        }).render('#paypal-button-container-new');
      }
    }

    // Save subscription to Firebase
    async function saveSubscriptionToFirebase(subscriptionId, subscriptionData) {
      if (!currentUser || !window.firebaseHelpers) {
        throw new Error('User not authenticated or Firebase not available');
      }

      const subscriptionInfo = {
        plan: 'premium',
        status: 'active',
        startDate: new Date(),
        endDate: new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)), // 30 days
        features: ['unlimited_mappings', 'games', 'advanced_settings', 'priority_support'],
        price: '$10.00',
        paymentMethod: 'paypal',
        paymentDetails: {
          subscriptionId: subscriptionId,
          planId: subscriptionData.plan_id
        }
      };

      const result = await window.firebaseHelpers.createSubscription(currentUser.uid, subscriptionInfo);

      if (!result) {
        throw new Error('Failed to save subscription to database');
      }

      console.log('Subscription saved to Firebase:', result);
      return result;
    }

    // Show payment processing (updated to work with existing functions)
    function showPaymentProcessing(type = 'current', show = true) {
      const element = document.getElementById(`payment-processing-${type}`);
      if (element) {
        element.style.display = show ? 'block' : 'none';
        if (show) {
          element.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; gap: 10px;">
              <div style="width: 20px; height: 20px; border: 2px solid #007bff; border-top: 2px solid transparent; border-radius: 50%; animation: spin 1s linear infinite;"></div>
              <span>جاري معالجة الاشتراك...</span>
            </div>
          `;
        }
      }
      // Hide other messages
      hidePaymentMessages(type, ['success', 'error']);
    }

    // Show payment success (updated to work with existing functions)
    function showPaymentSuccess(type = 'current', show = true) {
      const element = document.getElementById(`payment-success-${type}`);
      if (element) {
        element.style.display = show ? 'block' : 'none';
        if (show) {
          element.innerHTML = `
            <div style="color: #28a745;">
              <strong>✅ تم تفعيل الاشتراك بنجاح!</strong><br>
              <small>سيتم تحديث الصفحة خلال ثوانٍ...</small>
            </div>
          `;
        }
      }
      // Hide other messages
      hidePaymentMessages(type, ['processing', 'error']);
    }

    // Show payment error (updated to work with existing functions)
    function showPaymentError(type = 'current', message = 'حدث خطأ في الدفع') {
      // If type is actually the message (backward compatibility)
      if (typeof type === 'string' && !message) {
        message = type;
        type = 'current';
      }

      const element = document.getElementById(`payment-error-${type}`);
      if (element) {
        element.style.display = 'block';
        element.innerHTML = `
          <div style="color: #dc3545;">
            <strong>❌ خطأ في الدفع</strong><br>
            <small>${message}</small>
          </div>
        `;

        // Hide error after 5 seconds
        setTimeout(() => {
          element.style.display = 'none';
        }, 5000);
      }
      // Hide other messages
      hidePaymentMessages(type, ['processing', 'success']);
    }

    // Hide specific payment messages
    function hidePaymentMessages(type, messageTypes) {
      messageTypes.forEach(msgType => {
        const element = document.getElementById(`payment-${msgType}-${type}`);
        if (element) element.style.display = 'none';
      });
    }

    // Initialize when user is authenticated
    window.addEventListener('authStateChanged', (event) => {
      currentUser = event.detail.user;
      if (currentUser && currentUser.emailVerified) {
        loadPayPalConfig();
      }
    });

    // Load config if user is already authenticated
    if (window.firebaseAuth?.currentUser?.emailVerified) {
      currentUser = window.firebaseAuth.currentUser;
      loadPayPalConfig();
    }
  </script>

  <script src="/dark-mode.js"></script>
  <script src="/js/translation.js"></script>
  <script src="/js/smooth-nav.js"></script>
  <script src="/js/background-manager.js"></script>
</body>
</html>