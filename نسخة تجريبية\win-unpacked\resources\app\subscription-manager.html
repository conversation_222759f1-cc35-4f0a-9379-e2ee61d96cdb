<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة إدارة الاشتراكات - StreamTok</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #ff3b5c;
            box-shadow: 0 0 0 3px rgba(255, 59, 92, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 15px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 59, 92, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #868e96 100%);
        }

        .status {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .hidden {
            display: none;
        }

        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feature-item input[type="checkbox"] {
            width: auto;
        }

        .user-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .user-info h3 {
            color: #ff3b5c;
            margin-bottom: 15px;
        }

        .user-detail {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .user-detail:last-child {
            border-bottom: none;
        }

        .subscription-info {
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 أداة إدارة الاشتراكات</h1>
            <p>تفعيل وإدارة اشتراكات StreamTok بسهولة</p>
        </div>

        <div class="content">
            <!-- حالة الاتصال -->
            <div id="connectionStatus" class="status info">
                🔄 جاري الاتصال بـ Firebase...
            </div>

            <!-- نموذج تسجيل الدخول -->
            <div id="loginForm" class="hidden">
                <div class="status info">
                    🔐 يرجى تسجيل الدخول للوصول إلى أداة إدارة الاشتراكات
                </div>

                <div class="form-group">
                    <label for="adminEmail">📧 البريد الإلكتروني للمدير:</label>
                    <input type="email" id="adminEmail" placeholder="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="adminPassword">🔒 كلمة المرور:</label>
                    <input type="password" id="adminPassword" placeholder="كلمة المرور">
                </div>

                <button class="btn" onclick="loginAdmin()">🔑 تسجيل الدخول</button>
            </div>

            <!-- معلومات المدير المسجل -->
            <div id="adminInfo" class="user-info hidden">
                <h3>👨‍💼 مرحباً بك في لوحة الإدارة</h3>
                <div id="adminDetails"></div>
                <button class="btn btn-secondary" onclick="logoutAdmin()">🚪 تسجيل الخروج</button>
            </div>

            <!-- المحتوى الرئيسي (مخفي حتى تسجيل الدخول) -->
            <div id="mainContent" class="hidden">

            <!-- نموذج البحث عن المستخدم -->
            <div class="form-group">
                <label for="userId">🔍 معرف المستخدم (User ID):</label>
                <input type="text" id="userId" placeholder="مثال: iIUXaKylIkeh8ogBtIDYtJ66ehZ2">
            </div>

            <button class="btn" onclick="searchUser()">البحث عن المستخدم</button>

            <!-- معلومات المستخدم -->
            <div id="userInfo" class="user-info hidden">
                <h3>📋 معلومات المستخدم</h3>
                <div id="userDetails"></div>
            </div>

            <!-- نموذج تفعيل الاشتراك -->
            <div id="subscriptionForm" class="hidden">
                <div class="form-group">
                    <label for="planType">📦 نوع الخطة:</label>
                    <select id="planType">
                        <option value="premium">Premium - شامل</option>
                        <option value="basic">Basic - أساسي</option>
                        <option value="pro">Pro - احترافي</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="duration">⏰ مدة الاشتراك:</label>
                    <select id="duration">
                        <option value="30">30 يوم</option>
                        <option value="90">90 يوم (3 أشهر)</option>
                        <option value="180">180 يوم (6 أشهر)</option>
                        <option value="365">365 يوم (سنة كاملة)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="price">💰 السعر (بالدولار):</label>
                    <input type="number" id="price" value="10" min="0" step="0.01">
                </div>

                <div class="form-group">
                    <label>🎯 الميزات المتاحة:</label>
                    <div class="features-list">
                        <div class="feature-item">
                            <input type="checkbox" id="feature1" value="unlimited_profiles" checked>
                            <label for="feature1">ملفات شخصية غير محدودة</label>
                        </div>
                        <div class="feature-item">
                            <input type="checkbox" id="feature2" value="video_backgrounds" checked>
                            <label for="feature2">خلفيات فيديو</label>
                        </div>
                        <div class="feature-item">
                            <input type="checkbox" id="feature3" value="advanced_tts" checked>
                            <label for="feature3">TTS متقدم</label>
                        </div>
                        <div class="feature-item">
                            <input type="checkbox" id="feature4" value="priority_support" checked>
                            <label for="feature4">دعم أولوية</label>
                        </div>
                        <div class="feature-item">
                            <input type="checkbox" id="feature5" value="custom_themes">
                            <label for="feature5">ثيمات مخصصة</label>
                        </div>
                        <div class="feature-item">
                            <input type="checkbox" id="feature6" value="analytics">
                            <label for="feature6">تحليلات متقدمة</label>
                        </div>
                    </div>
                </div>

                <button class="btn" onclick="activateSubscription()">✅ تفعيل الاشتراك</button>
                <button class="btn btn-secondary" onclick="deactivateSubscription()">❌ إلغاء الاشتراك</button>
            </div>

            <!-- معلومات الاشتراك الحالي -->
            <div id="currentSubscription" class="hidden"></div>

            </div> <!-- نهاية mainContent -->
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js';
        import { getFirestore, doc, getDoc, updateDoc, collection, addDoc, query, where, getDocs, deleteDoc } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js';
        import { getAuth, signInWithEmailAndPassword, onAuthStateChanged, signOut } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDAG1ZwOOND0gyZSRcbUF1Qoh2SYN9R4Hs",
            authDomain: "streamtok-c6830.firebaseapp.com",
            projectId: "streamtok-c6830",
            storageBucket: "streamtok-c6830.firebasestorage.app",
            messagingSenderId: "123501258730",
            appId: "1:123501258730:web:690222fb0e45b217fbb493"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);
        const auth = getAuth(app);

        // Make Firebase available globally
        window.db = db;
        window.auth = auth;
        window.firestore = { doc, getDoc, updateDoc, collection, addDoc, query, where, getDocs, deleteDoc };
        window.firebaseAuth = { signInWithEmailAndPassword, onAuthStateChanged, signOut };

        // Update connection status
        document.getElementById('connectionStatus').innerHTML = '✅ تم الاتصال بـ Firebase بنجاح!';
        document.getElementById('connectionStatus').className = 'status success';

        // مراقبة حالة المصادقة
        window.firebaseAuth.onAuthStateChanged(window.auth, (user) => {
            if (user) {
                // المستخدم مسجل دخول
                showMainContent(user);
            } else {
                // المستخدم غير مسجل دخول
                showLoginForm();
            }
        });
    </script>

    <script>
        let currentUserData = null;
        let currentSubscriptionData = null;
        let currentAdmin = null;

        // عرض نموذج تسجيل الدخول
        function showLoginForm() {
            document.getElementById('loginForm').classList.remove('hidden');
            document.getElementById('adminInfo').classList.add('hidden');
            document.getElementById('mainContent').classList.add('hidden');
        }

        // عرض المحتوى الرئيسي بعد تسجيل الدخول
        function showMainContent(user) {
            currentAdmin = user;

            // إخفاء نموذج تسجيل الدخول
            document.getElementById('loginForm').classList.add('hidden');

            // عرض معلومات المدير
            document.getElementById('adminDetails').innerHTML = `
                <div class="user-detail">
                    <strong>📧 البريد الإلكتروني:</strong>
                    <span>${user.email}</span>
                </div>
                <div class="user-detail">
                    <strong>🆔 معرف المدير:</strong>
                    <span>${user.uid}</span>
                </div>
                <div class="user-detail">
                    <strong>⏰ وقت تسجيل الدخول:</strong>
                    <span>${new Date().toLocaleString('ar-EG')}</span>
                </div>
            `;

            document.getElementById('adminInfo').classList.remove('hidden');
            document.getElementById('mainContent').classList.remove('hidden');
        }

        // تسجيل دخول المدير
        async function loginAdmin() {
            const email = document.getElementById('adminEmail').value.trim();
            const password = document.getElementById('adminPassword').value.trim();

            if (!email || !password) {
                showStatus('❌ يرجى إدخال البريد الإلكتروني وكلمة المرور', 'error');
                return;
            }

            showStatus('🔄 جاري تسجيل الدخول...', 'info');

            try {
                const userCredential = await window.firebaseAuth.signInWithEmailAndPassword(window.auth, email, password);
                const user = userCredential.user;

                showStatus('✅ تم تسجيل الدخول بنجاح!', 'success');

                // مسح الحقول
                document.getElementById('adminEmail').value = '';
                document.getElementById('adminPassword').value = '';

            } catch (error) {
                console.error('Login error:', error);
                let errorMessage = 'خطأ في تسجيل الدخول';

                switch (error.code) {
                    case 'auth/user-not-found':
                        errorMessage = 'المستخدم غير موجود';
                        break;
                    case 'auth/wrong-password':
                        errorMessage = 'كلمة المرور غير صحيحة';
                        break;
                    case 'auth/invalid-email':
                        errorMessage = 'البريد الإلكتروني غير صحيح';
                        break;
                    case 'auth/too-many-requests':
                        errorMessage = 'محاولات كثيرة، يرجى المحاولة لاحقاً';
                        break;
                    default:
                        errorMessage = error.message;
                }

                showStatus('❌ ' + errorMessage, 'error');
            }
        }

        // تسجيل خروج المدير
        async function logoutAdmin() {
            try {
                await window.firebaseAuth.signOut(window.auth);
                showStatus('✅ تم تسجيل الخروج بنجاح', 'success');

                // مسح البيانات المحلية
                currentUserData = null;
                currentSubscriptionData = null;
                currentAdmin = null;

            } catch (error) {
                console.error('Logout error:', error);
                showStatus('❌ خطأ في تسجيل الخروج: ' + error.message, 'error');
            }
        }

        // البحث عن المستخدم
        async function searchUser() {
            const userId = document.getElementById('userId').value.trim();

            if (!userId) {
                showStatus('يرجى إدخال معرف المستخدم', 'error');
                return;
            }

            showStatus('🔍 جاري البحث عن المستخدم...', 'info');

            try {
                // البحث في مجموعة المستخدمين
                const userDoc = await window.firestore.getDoc(window.firestore.doc(window.db, 'users', userId));

                if (!userDoc.exists()) {
                    showStatus('❌ المستخدم غير موجود', 'error');
                    hideUserInfo();
                    return;
                }

                currentUserData = { id: userDoc.id, ...userDoc.data() };
                displayUserInfo(currentUserData);

                // البحث عن الاشتراك الحالي
                await searchCurrentSubscription(userId);

                showStatus('✅ تم العثور على المستخدم', 'success');

            } catch (error) {
                console.error('Error searching user:', error);
                showStatus('❌ خطأ في البحث: ' + error.message, 'error');
                hideUserInfo();
            }
        }

        // البحث عن الاشتراك الحالي
        async function searchCurrentSubscription(userId) {
            try {
                console.log('🔍 Searching subscription for userId:', userId);

                const subscriptionQuery = window.firestore.query(
                    window.firestore.collection(window.db, 'subscriptions'),
                    window.firestore.where('userId', '==', userId)
                );

                const querySnapshot = await window.firestore.getDocs(subscriptionQuery);
                console.log('🔍 Found subscriptions:', querySnapshot.size);

                if (!querySnapshot.empty) {
                    // البحث عن الاشتراك النشط
                    let activeSubscription = null;
                    querySnapshot.docs.forEach(doc => {
                        const data = doc.data();
                        console.log('📄 Subscription doc:', doc.id, data);
                        if (data.status === 'active') {
                            activeSubscription = { id: doc.id, ...data };
                            console.log('✅ Found active subscription:', doc.id);
                        }
                    });

                    if (activeSubscription) {
                        currentSubscriptionData = activeSubscription;
                        displayCurrentSubscription(activeSubscription);
                    } else {
                        document.getElementById('currentSubscription').innerHTML =
                            '<div class="status info">📋 لا يوجد اشتراك نشط حالياً</div>';
                        document.getElementById('currentSubscription').classList.remove('hidden');
                    }
                } else {
                    console.log('❌ No subscriptions found for userId:', userId);
                    document.getElementById('currentSubscription').innerHTML =
                        '<div class="status info">📋 لا يوجد اشتراكات مسجلة</div>';
                    document.getElementById('currentSubscription').classList.remove('hidden');
                }
            } catch (error) {
                console.error('❌ Error searching subscription:', error);
                showStatus('❌ خطأ في البحث عن الاشتراك: ' + error.message, 'error');
            }
        }

        // عرض معلومات المستخدم
        function displayUserInfo(userData) {
            const userDetails = document.getElementById('userDetails');
            const createdAt = userData.createdAt?.toDate ? userData.createdAt.toDate().toLocaleString('ar-EG') : 'غير محدد';
            const updatedAt = userData.updatedAt?.toDate ? userData.updatedAt.toDate().toLocaleString('ar-EG') : 'غير محدد';

            userDetails.innerHTML = `
                <div class="user-detail">
                    <strong>📧 البريد الإلكتروني:</strong>
                    <span>${userData.email || 'غير محدد'}</span>
                </div>
                <div class="user-detail">
                    <strong>👤 الاسم:</strong>
                    <span>${userData.name || 'غير محدد'}</span>
                </div>
                <div class="user-detail">
                    <strong>✅ تأكيد البريد:</strong>
                    <span>${userData.emailVerified ? 'مؤكد' : 'غير مؤكد'}</span>
                </div>
                <div class="user-detail">
                    <strong>💳 حالة الاشتراك:</strong>
                    <span>${userData.subscriptionStatus || 'none'}</span>
                </div>
                <div class="user-detail">
                    <strong>📅 تاريخ الإنشاء:</strong>
                    <span>${createdAt}</span>
                </div>
                <div class="user-detail">
                    <strong>🔄 آخر تحديث:</strong>
                    <span>${updatedAt}</span>
                </div>
            `;

            document.getElementById('userInfo').classList.remove('hidden');
            document.getElementById('subscriptionForm').classList.remove('hidden');
        }

        // عرض الاشتراك الحالي
        function displayCurrentSubscription(subscription) {
            const startDate = subscription.startDate?.toDate ? subscription.startDate.toDate().toLocaleDateString('ar-EG') : 'غير محدد';
            const endDate = subscription.endDate?.toDate ? subscription.endDate.toDate().toLocaleDateString('ar-EG') : 'غير محدد';
            const features = subscription.features ? subscription.features.join(', ') : 'لا توجد ميزات';

            document.getElementById('currentSubscription').innerHTML = `
                <div class="subscription-info">
                    <h3>💎 الاشتراك النشط الحالي</h3>
                    <div class="user-detail">
                        <strong>📦 نوع الخطة:</strong>
                        <span>${subscription.plan || 'غير محدد'}</span>
                    </div>
                    <div class="user-detail">
                        <strong>📅 تاريخ البداية:</strong>
                        <span>${startDate}</span>
                    </div>
                    <div class="user-detail">
                        <strong>📅 تاريخ الانتهاء:</strong>
                        <span>${endDate}</span>
                    </div>
                    <div class="user-detail">
                        <strong>💰 السعر:</strong>
                        <span>$${subscription.price || 0}</span>
                    </div>
                    <div class="user-detail">
                        <strong>🎯 الميزات:</strong>
                        <span>${features}</span>
                    </div>
                </div>
            `;
            document.getElementById('currentSubscription').classList.remove('hidden');
        }

        // إخفاء معلومات المستخدم
        function hideUserInfo() {
            document.getElementById('userInfo').classList.add('hidden');
            document.getElementById('subscriptionForm').classList.add('hidden');
            document.getElementById('currentSubscription').classList.add('hidden');
            currentUserData = null;
            currentSubscriptionData = null;
        }

        // عرض حالة العملية
        function showStatus(message, type) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.innerHTML = message;
            statusElement.className = `status ${type}`;
        }

        // تفعيل الاشتراك
        async function activateSubscription() {
            if (!currentUserData) {
                showStatus('❌ يرجى البحث عن المستخدم أولاً', 'error');
                return;
            }

            const userId = document.getElementById('userId').value.trim();
            const planType = document.getElementById('planType').value;
            const duration = parseInt(document.getElementById('duration').value);
            const price = parseFloat(document.getElementById('price').value);

            // التحقق من صحة البيانات
            if (!userId) {
                showStatus('❌ معرف المستخدم مطلوب', 'error');
                return;
            }

            // جمع الميزات المختارة
            const features = [];
            document.querySelectorAll('.feature-item input[type="checkbox"]:checked').forEach(checkbox => {
                features.push(checkbox.value);
            });

            console.log('🔄 Starting subscription activation for userId:', userId);
            showStatus('⏳ جاري تفعيل الاشتراك...', 'info');

            try {
                // إلغاء الاشتراكات النشطة السابقة
                if (currentSubscriptionData) {
                    console.log('🔄 Cancelling previous subscription:', currentSubscriptionData.id);
                    await window.firestore.updateDoc(
                        window.firestore.doc(window.db, 'subscriptions', currentSubscriptionData.id),
                        {
                            status: 'cancelled',
                            cancelledAt: new Date(),
                            updatedAt: new Date(),
                            notes: 'تم الإلغاء تلقائياً عند إنشاء اشتراك جديد'
                        }
                    );
                }

                // إنشاء اشتراك جديد
                const subscriptionData = {
                    userId: userId,
                    plan: planType,
                    status: 'active',
                    startDate: new Date(),
                    endDate: new Date(Date.now() + (duration * 24 * 60 * 60 * 1000)),
                    features: features,
                    price: price,
                    paymentMethod: 'manual_activation',
                    paymentId: 'MANUAL_' + Date.now(),
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    notes: 'تم التفعيل يدوياً من أداة الإدارة'
                };

                console.log('🔄 Creating subscription with data:', subscriptionData);
                const docRef = await window.firestore.addDoc(
                    window.firestore.collection(window.db, 'subscriptions'),
                    subscriptionData
                );

                console.log('✅ Subscription created with ID:', docRef.id);

                // تحديث حالة المستخدم
                console.log('🔄 Updating user status for userId:', userId);
                await window.firestore.updateDoc(
                    window.firestore.doc(window.db, 'users', userId),
                    {
                        subscriptionStatus: 'active',
                        updatedAt: new Date()
                    }
                );

                console.log('✅ User status updated successfully');
                showStatus('✅ تم تفعيل الاشتراك بنجاح!', 'success');

                // إعادة تحميل بيانات المستخدم والاشتراك
                await searchCurrentSubscription(userId);

                // تحديث بيانات المستخدم المعروضة
                currentUserData.subscriptionStatus = 'active';
                displayUserInfo(currentUserData);

            } catch (error) {
                console.error('❌ Error activating subscription:', error);
                showStatus('❌ خطأ في تفعيل الاشتراك: ' + error.message, 'error');
            }
        }

        // إلغاء الاشتراك
        async function deactivateSubscription() {
            if (!currentUserData) {
                showStatus('❌ يرجى البحث عن المستخدم أولاً', 'error');
                return;
            }

            if (!currentSubscriptionData) {
                showStatus('❌ لا يوجد اشتراك نشط لإلغائه', 'error');
                return;
            }

            if (!confirm('هل أنت متأكد من إلغاء الاشتراك؟')) {
                return;
            }

            const userId = document.getElementById('userId').value.trim();
            console.log('🔄 Starting subscription deactivation for userId:', userId);
            showStatus('⏳ جاري إلغاء الاشتراك...', 'info');

            try {
                // إلغاء الاشتراك النشط
                console.log('🔄 Cancelling subscription:', currentSubscriptionData.id);
                await window.firestore.updateDoc(
                    window.firestore.doc(window.db, 'subscriptions', currentSubscriptionData.id),
                    {
                        status: 'cancelled',
                        cancelledAt: new Date(),
                        updatedAt: new Date(),
                        notes: 'تم الإلغاء يدوياً من أداة الإدارة'
                    }
                );

                // تحديث حالة المستخدم
                console.log('🔄 Updating user status to none for userId:', userId);
                await window.firestore.updateDoc(
                    window.firestore.doc(window.db, 'users', userId),
                    {
                        subscriptionStatus: 'none',
                        updatedAt: new Date()
                    }
                );

                console.log('✅ Subscription cancelled successfully');
                showStatus('✅ تم إلغاء الاشتراك بنجاح!', 'success');

                // إعادة تحميل البيانات
                await searchCurrentSubscription(userId);

                // تحديث بيانات المستخدم المعروضة
                currentUserData.subscriptionStatus = 'none';
                displayUserInfo(currentUserData);

            } catch (error) {
                console.error('❌ Error deactivating subscription:', error);
                showStatus('❌ خطأ في إلغاء الاشتراك: ' + error.message, 'error');
            }
        }

        // تحديث السعر حسب نوع الخطة
        document.getElementById('planType').addEventListener('change', function() {
            const planType = this.value;
            const priceInput = document.getElementById('price');

            switch(planType) {
                case 'basic':
                    priceInput.value = 5;
                    break;
                case 'premium':
                    priceInput.value = 10;
                    break;
                case 'pro':
                    priceInput.value = 20;
                    break;
                default:
                    priceInput.value = 10;
            }
        });

        // السماح بالبحث عند الضغط على Enter
        document.getElementById('userId').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchUser();
            }
        });

        // السماح بتسجيل الدخول عند الضغط على Enter
        document.getElementById('adminEmail').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('adminPassword').focus();
            }
        });

        document.getElementById('adminPassword').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loginAdmin();
            }
        });
    </script>

</body>
</html>
