/**
 * نظام عكس اتجاه النص العام - يعمل على جميع الصفحات
 */

// دالة تطبيق اتجاه النص العامة
function applyGlobalTextDirection() {
  // التحقق من الإعداد المحفوظ
  const isReversed = localStorage.getItem('reverse_text_direction') === 'true';
  const currentLang = window.TranslationSystem ? window.TranslationSystem.getCurrentLanguage() : 'ar';

  if (currentLang === 'en' && isReversed) {
    // تطبيق LTR للإنجليزية - فقط للشريط الجانبي
    document.documentElement.setAttribute('dir', 'ltr');

    // تطبيق على الشريط الجانبي فقط
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
      sidebar.style.left = '0';
      sidebar.style.right = 'auto';
    }

    // تطبيق على المحتوى الرئيسي
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
      mainContent.style.marginLeft = '250px';
      mainContent.style.marginRight = '0';
    }

    // تطبيق على عناصر الشريط الجانبي
    const navLinks = document.querySelectorAll('.nav-menu a');
    navLinks.forEach(link => {
      link.style.flexDirection = 'row'; // الأيقونة في اليسار، النص في اليمين
      link.style.justifyContent = 'flex-start';
    });

    const navIcons = document.querySelectorAll('.nav-icon');
    navIcons.forEach(icon => {
      icon.style.marginLeft = '0';
      icon.style.marginRight = '10px'; // مسافة أكبر
    });

  } else {
    // العودة للـ RTL (الوضع الطبيعي)
    document.documentElement.setAttribute('dir', 'rtl');

    // إعادة تعيين الشريط الجانبي فقط
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
      sidebar.style.right = '0';
      sidebar.style.left = 'auto';
    }

    // إعادة تعيين المحتوى الرئيسي
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
      mainContent.style.marginRight = '250px';
      mainContent.style.marginLeft = '0';
    }

    // إعادة تعيين عناصر الشريط الجانبي فقط إذا كانت مُعدلة
    const navLinks = document.querySelectorAll('.nav-menu a');
    navLinks.forEach(link => {
      // إزالة التعديلات فقط إذا كانت موجودة
      if (link.style.flexDirection) {
        link.style.flexDirection = '';
      }
      if (link.style.justifyContent) {
        link.style.justifyContent = '';
      }
    });

    const navIcons = document.querySelectorAll('.nav-icon');
    navIcons.forEach(icon => {
      // إزالة التعديلات فقط إذا كانت موجودة
      if (icon.style.marginLeft || icon.style.marginRight) {
        icon.style.marginLeft = '';
        icon.style.marginRight = '';
      }
    });
  }
}

// تطبيق الاتجاه عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  applyGlobalTextDirection();

  // معالجة خيار عكس اتجاه النص في صفحة الإعدادات
  const reverseTextCheckbox = document.getElementById('reverseTextDirection');
  if (reverseTextCheckbox) {
    // تحميل الحالة المحفوظة
    const saved = localStorage.getItem('reverse_text_direction');
    reverseTextCheckbox.checked = saved === 'true';

    // معالج التغيير
    reverseTextCheckbox.addEventListener('change', () => {
      const isEnabled = reverseTextCheckbox.checked;
      localStorage.setItem('reverse_text_direction', isEnabled.toString());

      // تطبيق التغيير فوراً
      applyGlobalTextDirection();

      // إظهار رسالة تأكيد
      if (typeof showSaveConfirmation === 'function') {
        showSaveConfirmation('تم حفظ إعدادات اتجاه النص');
      }
    });
  }
});

// مراقبة تغيير اللغة
if (window.TranslationSystem) {
  // حفظ الدالة الأصلية
  const originalSetLanguage = window.TranslationSystem.setLanguage;

  // استبدال الدالة بنسخة محسنة
  window.TranslationSystem.setLanguage = function(language) {
    // استدعاء الدالة الأصلية
    originalSetLanguage.call(this, language);

    // تطبيق اتجاه النص بعد تغيير اللغة
    setTimeout(() => {
      applyGlobalTextDirection();
    }, 100);
  };
}

// مراقبة تغيير إعداد عكس الاتجاه
window.addEventListener('storage', (e) => {
  if (e.key === 'reverse_text_direction') {
    applyGlobalTextDirection();
  }
});

// تصدير الدالة للاستخدام العام
window.applyGlobalTextDirection = applyGlobalTextDirection;
