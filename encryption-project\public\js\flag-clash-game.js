/**
 * 🏁 Flag Clash Game - صراع الأعلام
 * لعبة ثلاثية الأبعاد تفاعلية مع TikTok Live
 * تطوير: Augment Agent
 */

class FlagClashGame {
  constructor() {
    this.canvas = document.getElementById('gameCanvas');
    this.socket = null;
    this.isDemo = new URLSearchParams(window.location.search).has('demo');

    // إعدادات اللعبة
    this.gameSettings = this.loadGameSettings();

    // حالة اللعبة
    this.gameState = {
      isRunning: false,
      isPaused: false,
      connectedToTikTok: false,
      currentLeader: null,
      totalGifts: 0,
      startTime: Date.now(),
      lastRanking: [], // آخر ترتيب للدول
      isAnimatingRanking: false // هل يتم تنفيذ أنيميشن الترتيب حالياً
    };

    // بيانات الدول العربية مع دعم الأعلام المخصصة ونوع الهدية
    this.countries = {
      'EG': { name: 'مصر', flag: '🇪🇬', power: 0, cube: null, color: 0xff0000, position: null, customName: null, customFlag: null, giftType: null, lastSender: null, giftCount: 0 },
      'SA': { name: 'السعودية', flag: '🇸🇦', power: 0, cube: null, color: 0x00ff00, position: null, customName: null, customFlag: null, giftType: null, lastSender: null, giftCount: 0 },
      'DZ': { name: 'الجزائر', flag: '🇩🇿', power: 0, cube: null, color: 0x0080ff, position: null, customName: null, customFlag: null, giftType: null, lastSender: null, giftCount: 0 },
      'MA': { name: 'المغرب', flag: '🇲🇦', power: 0, cube: null, color: 0xff8000, position: null, customName: null, customFlag: null, giftType: null, lastSender: null, giftCount: 0 },
      'IQ': { name: 'العراق', flag: '🇮🇶', power: 0, cube: null, color: 0x8000ff, position: null, customName: null, customFlag: null, giftType: null, lastSender: null, giftCount: 0 },
      'AE': { name: 'الإمارات', flag: '🇦🇪', power: 0, cube: null, color: 0xff0080, position: null, customName: null, customFlag: null, giftType: null, lastSender: null, giftCount: 0 },
      'TN': { name: 'تونس', flag: '🇹🇳', power: 0, cube: null, color: 0x00ff80, position: null, customName: null, customFlag: null, giftType: null, lastSender: null, giftCount: 0 },
      'SY': { name: 'سوريا', flag: '🇸🇾', power: 0, cube: null, color: 0xffff00, position: null, customName: null, customFlag: null, giftType: null, lastSender: null, giftCount: 0 },
      'LB': { name: 'لبنان', flag: '🇱🇧', power: 0, cube: null, color: 0xff4080, position: null, customName: null, customFlag: null, giftType: null, lastSender: null, giftCount: 0 },
      'QA': { name: 'قطر', flag: '🇶🇦', power: 0, cube: null, color: 0x80ff40, position: null, customName: null, customFlag: null, giftType: null, lastSender: null, giftCount: 0 },
      'KW': { name: 'الكويت', flag: '🇰🇼', power: 0, cube: null, color: 0x4080ff, position: null, customName: null, customFlag: null, giftType: null, lastSender: null, giftCount: 0 },
      'BH': { name: 'البحرين', flag: '🇧🇭', power: 0, cube: null, color: 0xff8040, position: null, customName: null, customFlag: null, giftType: null, lastSender: null, giftCount: 0 }
    };

    // تحميل الإعدادات المخصصة للدول
    this.loadCustomCountrySettings();

    // نظام الانضمام للفرق
    this.teamMembers = {}; // قائمة أعضاء كل فريق
    this.userTeams = {}; // ربط المستخدمين بالفرق
    this.teamJoinMessages = []; // رسائل الانضمام
    this.initTeamSystem();

    // متغيرات Three.js
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.animationId = null;

    // إضاءة ومؤثرات
    this.lights = {};
    this.particles = [];
    this.explosions = [];

    // عناصر DOM
    this.scoreboardContent = null;
    this.connectionStatus = null;

    // متغيرات التجربة
    this.demoInterval = null;

    console.log('🏁 تم إنشاء لعبة صراع الأعلام');
  }

  /**
   * تحميل إعدادات اللعبة
   */
  loadGameSettings() {
    try {
      const saved = localStorage.getItem('flagClashSettings');
      if (saved) {
        return { ...this.getDefaultSettings(), ...JSON.parse(saved) };
      }
    } catch (error) {
      console.warn('خطأ في تحميل الإعدادات:', error);
    }
    return this.getDefaultSettings();
  }

  /**
   * إعادة تحميل الإعدادات وتطبيقها
   */
  reloadSettings() {
    const oldSettings = { ...this.gameSettings };
    this.gameSettings = this.loadGameSettings();

    // تطبيق التغييرات حسب نوع الإعداد المتغير
    if (oldSettings.cubeArrangement !== this.gameSettings.cubeArrangement ||
        oldSettings.cubeSpacing !== this.gameSettings.cubeSpacing ||
        oldSettings.gridRows !== this.gameSettings.gridRows ||
        oldSettings.gridCols !== this.gameSettings.gridCols) {
      this.rearrangeCubes();
    }

    // تحديث إعدادات الكاميرا
    if (oldSettings.cameraX !== this.gameSettings.cameraX ||
        oldSettings.cameraY !== this.gameSettings.cameraY ||
        oldSettings.cameraZ !== this.gameSettings.cameraZ ||
        oldSettings.cameraControls !== this.gameSettings.cameraControls) {
      this.updateCameraSettings();
    }

    console.log('🔄 تم إعادة تحميل وتطبيق الإعدادات');
  }

  /**
   * تهيئة نظام الانضمام للفرق
   */
  initTeamSystem() {
    // تهيئة قوائم الفرق لكل دولة
    Object.keys(this.countries).forEach(countryCode => {
      this.teamMembers[countryCode] = [];
    });

    console.log('🏆 تم تهيئة نظام الانضمام للفرق');
  }

  /**
   * تحميل الإعدادات المخصصة للدول
   */
  loadCustomCountrySettings() {
    try {
      const saved = localStorage.getItem('flagClashCustomCountries');
      if (saved) {
        const customSettings = JSON.parse(saved);

        // تطبيق الإعدادات المخصصة
        Object.keys(customSettings).forEach(countryCode => {
          if (this.countries[countryCode]) {
            const custom = customSettings[countryCode];
            if (custom.customName) {
              this.countries[countryCode].customName = custom.customName;
            }
            if (custom.customFlag) {
              this.countries[countryCode].customFlag = custom.customFlag;
            }
            if (custom.giftType) {
              this.countries[countryCode].giftType = custom.giftType;
            }
          }
        });

        console.log('✅ تم تحميل الإعدادات المخصصة للدول');
      }
    } catch (error) {
      console.warn('خطأ في تحميل الإعدادات المخصصة:', error);
    }
  }

  /**
   * حفظ الإعدادات المخصصة للدول
   */
  saveCustomCountrySettings() {
    try {
      const customSettings = {};

      Object.keys(this.countries).forEach(countryCode => {
        const country = this.countries[countryCode];
        if (country.customName || country.customFlag || country.giftType) {
          customSettings[countryCode] = {
            customName: country.customName,
            customFlag: country.customFlag,
            giftType: country.giftType
          };
        }
      });

      localStorage.setItem('flagClashCustomCountries', JSON.stringify(customSettings));
      console.log('✅ تم حفظ الإعدادات المخصصة للدول');
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات المخصصة:', error);
    }
  }

  /**
   * الإعدادات الافتراضية
   */
  getDefaultSettings() {
    return {
      particleEffects: true,
      autoRotate: true,
      rotationSpeed: 0.01,
      maxCubeSize: 5, // الحد الأقصى للارتفاع العمودي
      minCubeSize: 1,
      growthRate: 0.002, // معدل النمو: 2000 وردة للفوز
      soundEnabled: true,
      soundVolume: 50,
      demoMode: false,
      demoSpeed: 2,
      countryCount: 8,
      // إعدادات الكاميرا
      cameraControls: false, // تمكين/إيقاف تحكم الكاميرا
      cameraX: 0,
      cameraY: 8,
      cameraZ: 15,
      cameraLookAtX: 0,
      cameraLookAtY: 0,
      cameraLookAtZ: 0,
      // إعدادات ترتيب المكعبات
      cubeArrangement: 'circle', // circle, grid, line, double_circle
      cubeSpacing: 8, // المسافة بين المكعبات
      gridRows: 3, // عدد الصفوف في الشبكة (3 صفوف × 4 أعمدة = 12 دولة)
      gridCols: 4, // عدد الأعمدة في الشبكة
      // إعدادات الترتيب الديناميكي
      dynamicRanking: true, // تفعيل الترتيب الديناميكي
      rankingAnimationSpeed: 800, // سرعة أنيميشن الترتيب (بالميلي ثانية)
      // إعدادات واجهة المستخدم
      showLeaderboard: true // إظهار/إخفاء ترتيب الدول
    };
  }

  /**
   * تهيئة اللعبة
   */
  async init() {
    try {
      console.log('🚀 بدء تهيئة لعبة صراع الأعلام...');
      
      // تهيئة عناصر DOM
      this.initDOMElements();
      
      // تهيئة Three.js
      await this.initThreeJS();
      
      // إنشاء المشهد
      this.createScene();
      
      // إنشاء مكعبات الأعلام
      this.createFlagCubes();
      
      // تهيئة Socket.IO
      this.initSocketIO();

      // إنشاء حاوية رسائل الانضمام
      this.createTeamJoinMessagesContainer();

      // بدء حلقة الرسم
      this.startGameLoop();
      
      // تحديث واجهة المستخدم
      this.updateUI();
      
      this.gameState.isRunning = true;
      console.log('✅ تم تهيئة لعبة صراع الأعلام بنجاح');
      console.log('🏆 حالة الترتيب الديناميكي:', this.gameSettings.dynamicRanking ? 'مفعل' : 'معطل');
      console.log('💥 تأثير Pop:', 'مفعل - المكعبات تنتفخ وتعود للحجم الطبيعي');
      
      // بدء وضع التجربة إذا كان مطلوباً (ولكن ليس في وضع العب الآن)
      const isPlayMode = new URLSearchParams(window.location.search).has('play');
      if ((this.isDemo || this.gameSettings.demoMode) && !isPlayMode) {
        this.startDemo();
      }
      
    } catch (error) {
      console.error('❌ خطأ في تهيئة اللعبة:', error);
    }
  }

  /**
   * تهيئة عناصر DOM
   */
  initDOMElements() {
    this.scoreboardContent = document.getElementById('scoreboardContent');
    this.connectionStatus = document.getElementById('connectionStatus');
    
    if (!this.canvas) {
      throw new Error('لم يتم العثور على عنصر Canvas');
    }
  }

  /**
   * تهيئة Three.js - بسيط وفعال
   */
  async initThreeJS() {
    // إنشاء المشهد
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x222222);

    // إنشاء الكاميرا باستخدام الإعدادات
    const aspect = window.innerWidth / window.innerHeight;
    this.camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
    this.camera.position.set(
      this.gameSettings.cameraX,
      this.gameSettings.cameraY,
      this.gameSettings.cameraZ
    );
    this.camera.lookAt(
      this.gameSettings.cameraLookAtX,
      this.gameSettings.cameraLookAtY,
      this.gameSettings.cameraLookAtZ
    );

    // إضافة تحكم الكاميرا حسب الإعدادات
    if (this.gameSettings.cameraControls) {
      this.initCameraControls();
    }

    // إنشاء الرندرر مع Anti-Aliasing والظلال
    this.renderer = new THREE.WebGLRenderer({
      canvas: this.canvas,
      antialias: true // تمكين Anti-Aliasing
    });

    this.renderer.setSize(window.innerWidth, window.innerHeight);

    // تمكين الظلال
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    console.log('✅ تم تهيئة Three.js بنجاح');

    // إضافة مستمع لتغيير حجم النافذة
    window.addEventListener('resize', () => this.onWindowResize());
  }

  /**
   * تهيئة تحكم الكاميرا
   */
  initCameraControls() {
    if (typeof THREE.OrbitControls === 'undefined') {
      console.warn('⚠️ OrbitControls غير متوفر - تم تخطي تحكم الكاميرا');
      return;
    }

    this.controls = new THREE.OrbitControls(this.camera, this.canvas);
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.05;
    this.controls.minDistance = 5;
    this.controls.maxDistance = 50;
    this.controls.maxPolarAngle = Math.PI / 2;

    console.log('🎥 تم تمكين تحكم الكاميرا');
  }

  /**
   * تحديث إعدادات الكاميرا
   */
  updateCameraSettings() {
    if (!this.camera) return;

    // تحديث موضع الكاميرا
    this.camera.position.set(
      this.gameSettings.cameraX,
      this.gameSettings.cameraY,
      this.gameSettings.cameraZ
    );

    // تحديث اتجاه النظر
    this.camera.lookAt(
      this.gameSettings.cameraLookAtX,
      this.gameSettings.cameraLookAtY,
      this.gameSettings.cameraLookAtZ
    );

    // تمكين/إيقاف تحكم الكاميرا
    if (this.gameSettings.cameraControls && !this.controls) {
      this.initCameraControls();
    } else if (!this.gameSettings.cameraControls && this.controls) {
      this.controls.dispose();
      this.controls = null;
      console.log('🎥 تم إيقاف تحكم الكاميرا');
    }
  }

  /**
   * إعادة ترتيب المكعبات
   */
  rearrangeCubes() {
    if (!this.countries) return;

    const countryKeys = Object.keys(this.countries).slice(0, this.gameSettings.countryCount);
    const positions = this.calculateCubePositions(countryKeys.length);

    countryKeys.forEach((countryCode, index) => {
      const country = this.countries[countryCode];
      if (country.cube && positions[index]) {
        const newPosition = positions[index];

        // تحديث موضع المكعب بسلاسة
        const cube = country.cube;
        const currentPos = cube.position;
        const targetPos = new THREE.Vector3(newPosition.x, newPosition.y, newPosition.z);

        // تحريك سلس للموضع الجديد
        this.animatePosition(cube, targetPos, 1000);

        // تحديث الموضع المحفوظ
        country.position = newPosition;
      }
    });

    console.log(`🔄 تم إعادة ترتيب المكعبات إلى ${this.gameSettings.cubeArrangement}`);
  }

  /**
   * تحريك المكعب بسلاسة إلى موضع جديد
   */
  animatePosition(object, targetPosition, duration) {
    const startPosition = object.position.clone();
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // تأثير easing
      const easeProgress = 1 - Math.pow(1 - progress, 3);

      object.position.lerpVectors(startPosition, targetPosition, easeProgress);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    animate();
  }

  /**
   * إنشاء المشهد - بسيط وفعال
   */
  createScene() {
    // إضاءة بسيطة
    const light = new THREE.AmbientLight(0xffffff, 0.6);
    this.scene.add(light);

    // إضاءة اتجاهية مع ظلال
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true; // تمكين الظلال

    // إعدادات الظلال
    directionalLight.shadow.mapSize.width = 1024;
    directionalLight.shadow.mapSize.height = 1024;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 50;
    directionalLight.shadow.camera.left = -20;
    directionalLight.shadow.camera.right = 20;
    directionalLight.shadow.camera.top = 20;
    directionalLight.shadow.camera.bottom = -20;

    this.scene.add(directionalLight);

    // إنشاء الأرض
    this.createFloor();

    // إنشاء الاستاد
    this.createStadium();

    console.log('✅ تم إنشاء المشهد مع الاستاد');
  }

  /**
   * إنشاء حقل النجوم
   */
  createStarField() {
    const starGeometry = new THREE.BufferGeometry();
    const starMaterial = new THREE.PointsMaterial({ 
      color: 0xffffff,
      size: 2,
      transparent: true,
      opacity: 0.8
    });

    const starVertices = [];
    for (let i = 0; i < 1000; i++) {
      const x = (Math.random() - 0.5) * 200;
      const y = (Math.random() - 0.5) * 200;
      const z = (Math.random() - 0.5) * 200;
      starVertices.push(x, y, z);
    }

    starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starVertices, 3));
    const stars = new THREE.Points(starGeometry, starMaterial);
    this.scene.add(stars);
  }

  /**
   * إنشاء الأرض مع دعم الصور المخصصة
   */
  createFloor() {
    const floorGeometry = new THREE.PlaneGeometry(60, 60);

    // البدء بلون افتراضي
    const floorMaterial = new THREE.MeshLambertMaterial({
      color: 0x2d5016 // أخضر داكن
    });

    const floor = new THREE.Mesh(floorGeometry, floorMaterial);
    floor.rotation.x = -Math.PI / 2;
    floor.position.y = -2;
    floor.receiveShadow = true;
    this.scene.add(floor);

    // حفظ مرجع للأرض لإمكانية تحديثها
    this.floor = floor;

    // تحميل الصورة المخصصة إن وجدت
    this.loadCustomFloorTexture();

    console.log('🟫 تم إنشاء الأرض');
  }

  /**
   * إنشاء الاستاد المربع المدمج في حافة الأرض
   */
  createStadium() {
    const fieldSize = 60; // حجم الأرض (60x60)
    const wallThickness = 2; // سماكة الجدران
    const wallHeight = 2; // ارتفاع قصير جداً
    const stadiumSize = fieldSize + wallThickness; // حجم الاستاد الكامل

    // إنشاء الجدران الأربعة للاستاد المربع
    this.createSquareStadiumWalls(fieldSize, wallThickness, wallHeight);

    // إنشاء حافة بسيطة فقط (بدون مدرجات معقدة)
    this.createSimpleStadiumEdge(fieldSize, wallThickness, wallHeight);

    console.log('🏟️ تم إنشاء الاستاد المربع المدمج');
  }

  /**
   * إنشاء جدران الاستاد المربع
   */
  createSquareStadiumWalls(fieldSize, wallThickness, wallHeight) {
    const halfField = fieldSize / 2;
    const wallPosition = halfField + wallThickness / 2;

    // مادة الجدران
    const wallMaterial = new THREE.MeshLambertMaterial({
      color: 0x8B4513, // لون بني للخرسانة
      side: THREE.DoubleSide
    });

    // الجدار الشمالي
    const northWallGeometry = new THREE.BoxGeometry(fieldSize + wallThickness * 2, wallHeight, wallThickness);
    const northWall = new THREE.Mesh(northWallGeometry, wallMaterial);
    northWall.position.set(0, wallHeight / 2 - 2, wallPosition);
    northWall.castShadow = true;
    northWall.receiveShadow = true;
    this.scene.add(northWall);

    // الجدار الجنوبي
    const southWall = new THREE.Mesh(northWallGeometry, wallMaterial);
    southWall.position.set(0, wallHeight / 2 - 2, -wallPosition);
    southWall.castShadow = true;
    southWall.receiveShadow = true;
    this.scene.add(southWall);

    // الجدار الشرقي
    const eastWallGeometry = new THREE.BoxGeometry(wallThickness, wallHeight, fieldSize);
    const eastWall = new THREE.Mesh(eastWallGeometry, wallMaterial);
    eastWall.position.set(wallPosition, wallHeight / 2 - 2, 0);
    eastWall.castShadow = true;
    eastWall.receiveShadow = true;
    this.scene.add(eastWall);

    // الجدار الغربي
    const westWall = new THREE.Mesh(eastWallGeometry, wallMaterial);
    westWall.position.set(-wallPosition, wallHeight / 2 - 2, 0);
    westWall.castShadow = true;
    westWall.receiveShadow = true;
    this.scene.add(westWall);
  }

  /**
   * إنشاء حافة بسيطة للاستاد
   */
  createSimpleStadiumEdge(fieldSize, wallThickness, wallHeight) {
    const edgeOffset = fieldSize / 2 + wallThickness;

    // مادة الحافة - لون أزرق بسيط
    const edgeMaterial = new THREE.MeshLambertMaterial({
      color: 0x4169E1, // أزرق ملكي
      side: THREE.DoubleSide
    });

    // حافة الجانب الشمالي
    const northEdgeGeometry = new THREE.BoxGeometry(fieldSize + wallThickness * 2, wallHeight * 0.5, wallThickness);
    const northEdge = new THREE.Mesh(northEdgeGeometry, edgeMaterial);
    northEdge.position.set(0, wallHeight - 2 + wallHeight * 0.25, edgeOffset);
    northEdge.castShadow = true;
    northEdge.receiveShadow = true;
    this.scene.add(northEdge);

    // حافة الجانب الجنوبي
    const southEdge = new THREE.Mesh(northEdgeGeometry, edgeMaterial);
    southEdge.position.set(0, wallHeight - 2 + wallHeight * 0.25, -edgeOffset);
    southEdge.castShadow = true;
    southEdge.receiveShadow = true;
    this.scene.add(southEdge);

    // حافة الجانب الشرقي
    const eastEdgeGeometry = new THREE.BoxGeometry(wallThickness, wallHeight * 0.5, fieldSize);
    const eastEdge = new THREE.Mesh(eastEdgeGeometry, edgeMaterial);
    eastEdge.position.set(edgeOffset, wallHeight - 2 + wallHeight * 0.25, 0);
    eastEdge.castShadow = true;
    eastEdge.receiveShadow = true;
    this.scene.add(eastEdge);

    // حافة الجانب الغربي
    const westEdge = new THREE.Mesh(eastEdgeGeometry, edgeMaterial);
    westEdge.position.set(-edgeOffset, wallHeight - 2 + wallHeight * 0.25, 0);
    westEdge.castShadow = true;
    westEdge.receiveShadow = true;
    this.scene.add(westEdge);
  }



  /**
   * إنشاء أعمدة الإنارة للاستاد المربع
   */
  createSquareStadiumLights(stadiumSize) {
    const lightHeight = 12; // ارتفاع العمود
    const lightOffset = stadiumSize / 2 + 2; // المسافة من مركز الاستاد

    // مواضع الأعمدة في الزوايا الأربع
    const cornerPositions = [
      { x: lightOffset, z: lightOffset },   // الزاوية الشمالية الشرقية
      { x: -lightOffset, z: lightOffset },  // الزاوية الشمالية الغربية
      { x: lightOffset, z: -lightOffset },  // الزاوية الجنوبية الشرقية
      { x: -lightOffset, z: -lightOffset }  // الزاوية الجنوبية الغربية
    ];

    // مواضع الأعمدة في منتصف كل جانب
    const sidePositions = [
      { x: 0, z: lightOffset },      // الجانب الشمالي
      { x: 0, z: -lightOffset },     // الجانب الجنوبي
      { x: lightOffset, z: 0 },      // الجانب الشرقي
      { x: -lightOffset, z: 0 }      // الجانب الغربي
    ];

    // دمج جميع المواضع
    const allPositions = [...cornerPositions, ...sidePositions];

    allPositions.forEach((position, index) => {
      // إنشاء عمود الإنارة
      const poleGeometry = new THREE.CylinderGeometry(0.3, 0.3, lightHeight, 8);
      const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });

      const pole = new THREE.Mesh(poleGeometry, poleMaterial);
      pole.position.set(position.x, lightHeight / 2 - 2, position.z);
      pole.castShadow = true;
      this.scene.add(pole);

      // إنشاء المصباح
      const lightGeometry = new THREE.SphereGeometry(0.8, 8, 8);
      const lightMaterial = new THREE.MeshBasicMaterial({
        color: 0xFFFFE0,
        emissive: 0xFFFFE0,
        emissiveIntensity: 0.3
      });

      const lightBulb = new THREE.Mesh(lightGeometry, lightMaterial);
      lightBulb.position.set(position.x, lightHeight - 2, position.z);
      this.scene.add(lightBulb);

      // إضافة إضاءة فعلية
      const spotLight = new THREE.SpotLight(0xFFFFE0, 0.4, 60, Math.PI / 5, 0.1);
      spotLight.position.set(position.x, lightHeight - 2, position.z);
      spotLight.target.position.set(0, -2, 0);
      spotLight.castShadow = true;
      spotLight.shadow.mapSize.width = 512;
      spotLight.shadow.mapSize.height = 512;
      this.scene.add(spotLight);
      this.scene.add(spotLight.target);
    });
  }

  /**
   * تحميل نسيج الأرض المخصص (من IndexedDB أو localStorage)
   */
  loadCustomFloorTexture() {
    // التحقق من وجود صورة في IndexedDB أولاً
    if (localStorage.getItem('flagClashFloorSaved') === 'true') {
      this.loadFloorFromIndexedDB();
    } else {
      // تحميل من localStorage
      this.loadFloorFromLocalStorage();
    }
  }

  /**
   * تحميل صورة الأرض من IndexedDB
   */
  loadFloorFromIndexedDB() {
    const request = indexedDB.open('FlagClashDB', 1);

    request.onsuccess = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains('floorImages')) {
        this.loadFloorFromLocalStorage();
        return;
      }

      const transaction = db.transaction(['floorImages'], 'readonly');
      const store = transaction.objectStore('floorImages');
      const getRequest = store.get('currentFloor');

      getRequest.onsuccess = () => {
        if (getRequest.result && this.floor) {
          const imageUrl = URL.createObjectURL(getRequest.result.blob);
          this.applyFloorTexture(imageUrl);
          console.log('🎨 تم تحميل صورة الأرض عالية الجودة من IndexedDB');
        } else {
          this.loadFloorFromLocalStorage();
        }
      };

      getRequest.onerror = () => {
        this.loadFloorFromLocalStorage();
      };
    };

    request.onerror = () => {
      this.loadFloorFromLocalStorage();
    };
  }

  /**
   * تحميل صورة الأرض من localStorage
   */
  loadFloorFromLocalStorage() {
    const customFloorImage = localStorage.getItem('flagClashCustomFloor');

    if (customFloorImage && this.floor) {
      this.applyFloorTexture(customFloorImage);
      console.log('🖼️ تم تحميل صورة الأرض من localStorage');
    }
  }

  /**
   * تطبيق نسيج الأرض
   */
  applyFloorTexture(imageUrl) {
    if (!this.floor) return;

    try {
      const textureLoader = new THREE.TextureLoader();
      const floorTexture = textureLoader.load(
        imageUrl,
        // عند نجاح التحميل
        (texture) => {
          texture.wrapS = THREE.RepeatWrapping;
          texture.wrapT = THREE.RepeatWrapping;
          texture.repeat.set(2, 2);
          texture.flipY = false; // إصلاح الصورة المقلوبة

          this.floor.material = new THREE.MeshLambertMaterial({
            map: texture
          });

          console.log('✨ تم تطبيق نسيج الأرض بنجاح');
        },
        // أثناء التحميل
        undefined,
        // عند فشل التحميل
        (error) => {
          console.error('❌ فشل في تحميل صورة الأرض:', error);
        }
      );
    } catch (error) {
      console.error('❌ خطأ في تطبيق نسيج الأرض:', error);
    }
  }

  /**
   * إنشاء مكعبات الأعلام مع ترتيبات مختلفة
   */
  createFlagCubes() {
    console.log('🚀 بدء إنشاء مكعبات الأعلام...');

    const countryKeys = Object.keys(this.countries).slice(0, this.gameSettings.countryCount);
    const positions = this.calculateCubePositions(countryKeys.length);

    countryKeys.forEach((countryCode, index) => {
      const country = this.countries[countryCode];
      const position = positions[index];

      // حفظ الموضع
      country.position = position;

      // إنشاء المكعب
      const geometry = new THREE.BoxGeometry(2, 2, 2);
      const material = new THREE.MeshLambertMaterial({
        color: country.color
      });

      const cube = new THREE.Mesh(geometry, material);
      cube.position.set(position.x, position.y, position.z);

      // تمكين الظلال للمكعب
      cube.castShadow = true;
      cube.receiveShadow = true;

      // تهيئة userData لمنع تداخل التأثيرات
      cube.userData = {
        isPopping: false, // حالة تأثير Pop
        lastSenderText: null, // نص اسم آخر مرسل
        giftCountText: null // نص عداد الهدايا
      };

      // إضافة المكعب للمشهد فوراً
      this.scene.add(cube);
      country.cube = cube;

      console.log(`🏁 تم إنشاء مكعب ${country.name} في الموضع (${position.x.toFixed(1)}, ${position.y}, ${position.z.toFixed(1)})`);

      // إضافة نسيج العلم
      this.addFlagTexture(cube, countryCode);
    });

    console.log(`📊 تم إنشاء ${countryKeys.length} مكعبات بترتيب ${this.gameSettings.cubeArrangement}`);
  }

  /**
   * حساب مواضع المكعبات حسب نوع الترتيب
   */
  calculateCubePositions(count) {
    const positions = [];
    const spacing = this.gameSettings.cubeSpacing;

    switch (this.gameSettings.cubeArrangement) {
      case 'circle':
        return this.getCirclePositions(count, spacing);

      case 'grid':
        return this.getGridPositions(count, spacing);

      case 'line':
        return this.getLinePositions(count, spacing);

      case 'double_circle':
        return this.getDoubleCirclePositions(count, spacing);

      default:
        return this.getCirclePositions(count, spacing);
    }
  }

  /**
   * ترتيب دائري (الافتراضي)
   */
  getCirclePositions(count, radius) {
    const positions = [];
    for (let i = 0; i < count; i++) {
      const angle = (i / count) * Math.PI * 2;
      positions.push({
        x: Math.cos(angle) * radius,
        y: -1, // وضع المكعبات على الأرض (الأرض في y = -2، المكعب ارتفاع 2)
        z: Math.sin(angle) * radius
      });
    }
    return positions;
  }

  /**
   * ترتيب شبكي (صفوف وأعمدة)
   */
  getGridPositions(count, spacing) {
    const positions = [];
    const rows = this.gameSettings.gridRows;
    const cols = this.gameSettings.gridCols;

    // التأكد من أن الشبكة تتسع للعدد المطلوب
    const actualRows = Math.ceil(count / cols);

    for (let i = 0; i < count; i++) {
      const row = Math.floor(i / cols);
      const col = i % cols;

      // توسيط الشبكة
      const startX = -(cols - 1) * spacing / 2;
      const startZ = -(actualRows - 1) * spacing / 2;

      positions.push({
        x: startX + col * spacing,
        y: -1, // وضع المكعبات على الأرض
        z: startZ + row * spacing
      });
    }
    return positions;
  }

  /**
   * ترتيب خطي
   */
  getLinePositions(count, spacing) {
    const positions = [];
    const startX = -(count - 1) * spacing / 2;

    for (let i = 0; i < count; i++) {
      positions.push({
        x: startX + i * spacing,
        y: -1, // وضع المكعبات على الأرض
        z: 0
      });
    }
    return positions;
  }

  /**
   * ترتيب دائرة مزدوجة
   */
  getDoubleCirclePositions(count, spacing) {
    const positions = [];
    const innerRadius = spacing * 0.6;
    const outerRadius = spacing * 1.2;

    const innerCount = Math.floor(count / 2);
    const outerCount = count - innerCount;

    // الدائرة الداخلية
    for (let i = 0; i < innerCount; i++) {
      const angle = (i / innerCount) * Math.PI * 2;
      positions.push({
        x: Math.cos(angle) * innerRadius,
        y: -1, // وضع المكعبات على الأرض
        z: Math.sin(angle) * innerRadius
      });
    }

    // الدائرة الخارجية
    for (let i = 0; i < outerCount; i++) {
      const angle = (i / outerCount) * Math.PI * 2;
      positions.push({
        x: Math.cos(angle) * outerRadius,
        y: -1, // وضع المكعبات على الأرض
        z: Math.sin(angle) * outerRadius
      });
    }

    return positions;
  }

  /**
   * إضافة نسيج العلم للمكعب - الصور المخصصة فقط
   */
  async addFlagTexture(cube, countryCode) {
    try {
      const country = this.countries[countryCode];
      console.log(`🏁 محاولة تحميل علم ${countryCode}...`);

      let flagUrl;
      let isCustomFlag = false;

      // التحقق من وجود علم مخصص فقط
      if (country.customFlag) {
        flagUrl = country.customFlag;
        isCustomFlag = true;
        console.log(`🎨 استخدام علم مخصص: ${flagUrl}`);
      } else {
        // لا يوجد علم مخصص - استخدام اللون فقط
        console.log(`🎨 لا يوجد علم مخصص لـ ${countryCode} - استخدام اللون فقط`);
        this.applyFallbackColor(cube, countryCode);
        return;
      }

      const textureLoader = new THREE.TextureLoader();

      const texture = await new Promise((resolve, reject) => {
        textureLoader.load(
          flagUrl,
          (loadedTexture) => {
            console.log(`✅ تم تحميل صورة علم ${countryCode} بنجاح`);
            // إعدادات لمنع الانحناء والتشويه
            loadedTexture.minFilter = THREE.LinearFilter;
            loadedTexture.magFilter = THREE.LinearFilter;
            loadedTexture.wrapS = THREE.ClampToEdgeWrapping;
            loadedTexture.wrapT = THREE.ClampToEdgeWrapping;

            // إصلاح الصورة المقلوبة للصور المخصصة
            loadedTexture.flipY = true;

            resolve(loadedTexture);
          },
          (progress) => {
            if (progress.total > 0) {
              console.log(`📥 تحميل علم ${countryCode}: ${(progress.loaded / progress.total * 100).toFixed(0)}%`);
            }
          },
          (error) => {
            console.error(`❌ فشل في تحميل علم ${countryCode}:`, error);
            resolve(null);
          }
        );
      });

      if (texture) {
        // إنشاء مواد منفصلة لكل وجه
        const materials = [
          new THREE.MeshLambertMaterial({ map: texture }), // يمين
          new THREE.MeshLambertMaterial({ map: texture }), // يسار
          new THREE.MeshLambertMaterial({ map: texture }), // أعلى
          new THREE.MeshLambertMaterial({ map: texture }), // أسفل
          new THREE.MeshLambertMaterial({ map: texture }), // أمام
          new THREE.MeshLambertMaterial({ map: texture })  // خلف
        ];

        cube.material = materials;
        console.log(`🎨 تم تطبيق علم ${countryCode} على المكعب`);
      } else {
        // استخدام لون الدولة كبديل
        console.log(`🎨 استخدام لون احتياطي لـ ${countryCode}`);
        this.applyFallbackColor(cube, countryCode);
      }

    } catch (error) {
      console.error(`❌ خطأ في تحميل علم ${countryCode}:`, error);
      this.applyFallbackColor(cube, countryCode);
    }
  }

  /**
   * تطبيق لون احتياطي للمكعب
   */
  applyFallbackColor(cube, countryCode) {
    const country = this.countries[countryCode];
    if (country) {
      const material = new THREE.MeshLambertMaterial({
        color: country.color
      });
      cube.material = material;
      console.log(`🎨 تم تطبيق اللون الاحتياطي لـ ${this.getCountryDisplayName(countryCode)}: #${country.color.toString(16)}`);
    }
  }

  /**
   * الحصول على اسم الدولة للعرض (الاسم المخصص من الإعدادات أولاً)
   */
  getCountryDisplayName(countryCode) {
    const country = this.countries[countryCode];
    if (!country) return countryCode;

    // استخدام الاسم المخصص من الإعدادات إذا كان موجود
    if (country.customName) {
      return country.customName;
    }

    // إذا لم يكن هناك اسم مخصص، استخدم الاسم الافتراضي
    return country.name;
  }

  /**
   * تحديث اسم دولة مخصص
   */
  updateCustomCountryName(countryCode, customName) {
    if (this.countries[countryCode]) {
      this.countries[countryCode].customName = customName;
      this.saveCustomCountrySettings();
      console.log(`✅ تم تحديث اسم ${countryCode} إلى: ${customName}`);
    }
  }

  /**
   * تحديث علم دولة مخصص
   */
  updateCustomCountryFlag(countryCode, customFlagUrl) {
    if (this.countries[countryCode]) {
      this.countries[countryCode].customFlag = customFlagUrl;
      this.saveCustomCountrySettings();

      // إعادة تحميل العلم على المكعب
      if (this.countries[countryCode].cube) {
        this.addFlagTexture(this.countries[countryCode].cube, countryCode);
      }

      console.log(`✅ تم تحديث علم ${countryCode} إلى: ${customFlagUrl}`);
    }
  }

  /**
   * إزالة التخصيصات لدولة معينة
   */
  resetCountryCustomizations(countryCode) {
    if (this.countries[countryCode]) {
      this.countries[countryCode].customName = null;
      this.countries[countryCode].customFlag = null;
      this.saveCustomCountrySettings();

      // إعادة تحميل العلم الافتراضي
      if (this.countries[countryCode].cube) {
        this.addFlagTexture(this.countries[countryCode].cube, countryCode);
      }

      console.log(`🔄 تم إعادة تعيين تخصيصات ${countryCode}`);
    }
  }

  /**
   * تهيئة Socket.IO للاتصال بـ TikTok Live
   */
  initSocketIO() {
    try {
      this.socket = io();

      // الاتصال
      this.socket.on('connect', () => {
        console.log('🔌 تم الاتصال بالخادم');
        this.updateConnectionStatus(true);
      });

      // قطع الاتصال
      this.socket.on('disconnect', () => {
        console.log('🔌 تم قطع الاتصال عن الخادم');
        this.updateConnectionStatus(false);
      });

      // استقبال الهدايا
      this.socket.on('gift', (data) => {
        this.handleGiftReceived(data);
      });

      // استقبال التعليقات والدردشة
      this.socket.on('comment', (data) => {
        console.log('💬 تعليق:', data);
      });

      this.socket.on('chat', (data) => {
        console.log('💬 دردشة:', data);
      });

      // استقبال أوامر الانضمام للفرق
      this.socket.on('teamJoin', (data) => {
        console.log('🏆 استقبال أمر انضمام:', data);
        this.handleTeamJoin(data);
      });

      this.socket.on('teamLeave', (data) => {
        console.log('👋 استقبال أمر مغادرة:', data);
        this.handleTeamLeave(data);
      });

      this.socket.on('teamQuery', (data) => {
        console.log('❓ استقبال استعلام فريق:', data);
        this.handleTeamQuery(data);
      });

      this.socket.on('teamsQuery', (data) => {
        console.log('📋 استقبال استعلام فرق:', data);
        this.handleTeamsQuery(data);
      });

      // حالة اتصال TikTok
      this.socket.on('connectionUpdate', (data) => {
        this.gameState.connectedToTikTok = data.status === 'connected';
        this.updateConnectionStatus(this.gameState.connectedToTikTok);
      });

    } catch (error) {
      console.error('خطأ في تهيئة Socket.IO:', error);
    }
  }

  /**
   * بدء حلقة الرسم - بسيط وفعال
   */
  startGameLoop() {
    const animate = () => {
      requestAnimationFrame(animate);
      this.renderer.render(this.scene, this.camera);
    };

    animate();
    console.log('🎮 تم بدء حلقة الرسم');
  }

  /**
   * تحديث المشهد
   */
  updateScene() {
    if (this.gameState.isPaused) return;

    // دوران المكعبات
    if (this.gameSettings.autoRotate) {
      Object.values(this.countries).forEach(country => {
        if (country.cube) {
          country.cube.rotation.x += this.gameSettings.rotationSpeed;
          country.cube.rotation.y += this.gameSettings.rotationSpeed * 0.7;
        }
      });
    }

    // تحديث الجسيمات
    this.updateParticles();

    // تحديث الانفجارات
    this.updateExplosions();
  }

  /**
   * معالجة استقبال الهدايا
   */
  handleGiftReceived(data) {
    console.log('🎁 تم استقبال هدية:', data);

    // تحديد الدولة مع دعم التحكم اليدوي
    let countryCode;

    // التحقق من التحكم اليدوي
    if (data.uniqueId && data.uniqueId.startsWith('manual_user_')) {
      countryCode = data.uniqueId.replace('manual_user_', '');
      console.log(`🎮 تحكم يدوي: إرسال هدية لـ ${countryCode}`);
    } else {
      const username = data.nickname || data.uniqueId;

      // الأولوية 1: التحقق من انضمام المستخدم لفريق
      if (username && this.userTeams[username]) {
        countryCode = this.userTeams[username];
        console.log(`🏆 ${username} منضم لفريق ${countryCode} - الهدية تذهب للفريق`);
      } else {
        // الأولوية 2: التحقق من نوع الهدية
        countryCode = this.detectCountryByGiftType(data.giftName);

        if (countryCode) {
          console.log(`🎯 هدية ${data.giftName} مرتبطة بـ ${countryCode}`);
        } else {
          // الأولوية 3: التوزيع العشوائي
          countryCode = this.detectUserCountry(username);
          console.log(`🎲 توزيع عشوائي لـ ${username}: ${countryCode}`);
        }
      }
    }

    if (this.countries[countryCode]) {
      const country = this.countries[countryCode];
      const giftPower = this.calculateGiftPower(data.giftName, data.repeatCount || 1);

      // الحصول على عدد الهدايا الفعلي
      const giftCount = data.repeatCount || 1;
      console.log(`🎁 تنفيذ ${giftCount} هدية من نوع ${data.giftName} للدولة ${country.name}`);

      // زيادة قوة الدولة وعداد الهدايا بالعدد الكامل
      country.power += giftPower;
      country.giftCount += giftCount; // زيادة عداد الهدايا بالعدد الكامل
      this.gameState.totalGifts += giftCount;

      // بيانات المستخدم
      const userData = {
        profilePictureUrl: data.profilePictureUrl || null,
        nickname: data.nickname || data.uniqueId || 'مستخدم'
      };

      // حفظ اسم آخر مرسل
      country.lastSender = userData.nickname;

      // تحديد طريقة التنفيذ حسب عدد الهدايا
      if (giftCount > 100) {
        // تنفيذ فوري للهدايا الكثيرة جداً (أكثر من 100)
        console.log(`⚡ تنفيذ فوري لـ ${giftCount} هدية - تجاوز الحد الأقصى`);
        this.executeInstantGifts(country, giftCount, userData);
      } else {
        // تنفيذ تأثير لكل هدية منفردة
        this.executeMultipleGifts(country, giftCount, userData);
      }

      // إضافة تأثيرات بصرية
      this.createGiftEffect(country, data, countryCode);

      // تحديث القائد سيتم بعد تحديث العداد
      // this.updateLeader(); // تم نقله لبعد تحديث العداد

      // تحديث الترتيب الديناميكي مع التأثير الجيلي
      if (this.gameSettings.dynamicRanking) {
        console.log('🎯 تفعيل النظام الديناميكي...');
        this.updateDynamicRanking();
      } else {
        console.log('⚠️ النظام الديناميكي معطل في الإعدادات');
      }

      // تحديث واجهة المستخدم
      this.updateUI();

      // تشغيل صوت (إذا كان مفعلاً)
      if (this.gameSettings.soundEnabled) {
        this.playSound('gift_received');
      }

      console.log(`💪 ${country.name} تلقت ${giftPower} نقطة قوة! إجمالي القوة: ${country.power}`);
    }
  }

  /**
   * تحديث الترتيب الديناميكي مع التأثير الجيلي
   */
  updateDynamicRanking() {
    console.log('🔄 تم استدعاء updateDynamicRanking');

    if (this.gameState.isAnimatingRanking) {
      console.log('⏳ الأنيميشن قيد التنفيذ - تم التجاهل');
      return; // تجنب التداخل
    }

    // حساب الترتيب الحالي
    const currentRanking = this.calculateCountryRanking();
    console.log('📊 الترتيب الحالي:', currentRanking.map(c => `${c.code}:${c.power}`));

    // مقارنة مع الترتيب السابق
    if (this.hasRankingChanged(currentRanking)) {
      console.log('🏆 تغيير في الترتيب - بدء الأنيميشن الجيلي');
      this.animateRankingChangeWithJelly(currentRanking);
      this.gameState.lastRanking = [...currentRanking];
    } else {
      console.log('✅ لا يوجد تغيير في الترتيب');
    }
  }

  /**
   * حساب ترتيب الدول حسب عدد الهدايا (الدعم)
   */
  calculateCountryRanking() {
    // استخدام جميع الدول المتاحة (12 دولة عربية)
    const countryKeys = Object.keys(this.countries);
    return countryKeys
      .map(code => ({
        code,
        power: this.countries[code].giftCount, // استخدام عدد الهدايا بدلاً من القوة
        giftCount: this.countries[code].giftCount
      }))
      .sort((a, b) => b.power - a.power); // ترتيب تنازلي (الأكثر دعماً أولاً)
  }

  /**
   * التحقق من تغيير الترتيب
   */
  hasRankingChanged(newRanking) {
    if (this.gameState.lastRanking.length === 0) {
      return true; // أول مرة
    }

    // مقارنة الترتيب الجديد مع السابق (جميع الدول الـ12)
    for (let i = 0; i < newRanking.length; i++) {
      if (!this.gameState.lastRanking[i] ||
          newRanking[i].code !== this.gameState.lastRanking[i].code) {
        return true;
      }
    }
    return false;
  }

  /**
   * تنفيذ أنيميشن تغيير الترتيب مع التأثير الجيلي
   */
  async animateRankingChangeWithJelly(newRanking) {
    this.gameState.isAnimatingRanking = true;

    try {
      // حساب المواضع الجديدة - الدولة الأكثر تقدماً على اليمين في الصف الأول
      const newPositions = this.calculateRankingPositionsWithFrontRow(newRanking);

      // تحريك الدول إلى مواضعها الجديدة مع التأثير الجيلي
      await this.animateCountriesToNewPositionsJelly(newPositions);



      console.log('✨ تم الانتهاء من أنيميشن الترتيب الجيلي');
    } catch (error) {
      console.error('❌ خطأ في أنيميشن الترتيب:', error);
    } finally {
      this.gameState.isAnimatingRanking = false;
    }
  }

  /**
   * حساب المواضع الجديدة - الدول المتقدمة في الصف الأول والأكثر تقدماً على اليمين
   */
  calculateRankingPositionsWithFrontRow(ranking) {
    const positions = {};
    const spacing = this.gameSettings.cubeSpacing;
    const cols = this.gameSettings.gridCols;
    const rows = this.gameSettings.gridRows;

    // حساب جميع المواضع في الشبكة العادية أولاً
    const allPositions = [];
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        allPositions.push({
          x: -(cols - 1) * spacing / 2 + col * spacing,
          y: -1, // على الأرض
          z: (rows - 1) * spacing / 2 - row * spacing  // الصف الأول Z موجب = المقدمة
        });
      }
    }

    // ترتيب المواضع حسب الأولوية: الصف الأول من اليمين إلى اليسار
    const prioritizedPositions = [];

    // الصف الأول (المقدمة) - من اليمين إلى اليسار للدول المتقدمة
    for (let col = cols - 1; col >= 0; col--) {
      const pos = allPositions.find(p =>
        p.x === (-(cols - 1) * spacing / 2 + col * spacing) &&
        p.z === ((rows - 1) * spacing / 2)  // الصف الأول Z موجب = المقدمة
      );
      if (pos) prioritizedPositions.push(pos);
    }

    // باقي الصفوف - من اليسار إلى اليمين (الترتيب العادي)
    for (let row = 1; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const pos = allPositions.find(p =>
          p.x === (-(cols - 1) * spacing / 2 + col * spacing) &&
          p.z === ((rows - 1) * spacing / 2 - row * spacing)
        );
        if (pos) prioritizedPositions.push(pos);
      }
    }

    // تعيين المواضع للدول حسب ترتيبها
    ranking.forEach((country, index) => {
      if (index < prioritizedPositions.length) {
        positions[country.code] = {
          ...prioritizedPositions[index],
          rank: index + 1
        };
      } else {
        // في حالة وجود دول أكثر من المواضع المتاحة (احتياط)
        positions[country.code] = {
          x: 0,
          y: -1,
          z: 0,
          rank: index + 1
        };
      }
    });

    return positions;
  }

  /**
   * تحريك الدول إلى مواضعها الجديدة مع التأثير الجيلي
   */
  async animateCountriesToNewPositionsJelly(newPositions) {
    const animationPromises = [];
    const duration = this.gameSettings.rankingAnimationSpeed || 800;

    Object.keys(newPositions).forEach(countryCode => {
      const country = this.countries[countryCode];
      const newPos = newPositions[countryCode];

      if (country.cube) {
        // أنيميشن جيلي مع ارتفاع منحني
        const promise = this.animateJellyMovement(
          country.cube,
          new THREE.Vector3(newPos.x, newPos.y, newPos.z),
          duration,
          newPos.rank <= 3 // حركة خاصة للمراكز الثلاثة الأولى
        );
        animationPromises.push(promise);

        // تحديث الموضع المحفوظ
        country.position = newPos;
      }
    });

    // انتظار انتهاء جميع الحركات
    await Promise.all(animationPromises);
  }

  /**
   * أنيميشن حركة جيلي مع ارتفاع منحني
   */
  animateJellyMovement(object, targetPosition, duration, isTopRank = false) {
    return new Promise((resolve) => {
      const startPosition = object.position.clone();
      const startTime = Date.now();

      // ارتفاع إضافي للحركة المنحنية (تأثير جيلي)
      const arcHeight = isTopRank ? 6 : 3;

      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // تأثير easing جيلي مع تذبذب
        const easeProgress = this.jellyEasing(progress);

        // حساب الموضع المنحني (تأثير جيلي)
        const currentPos = new THREE.Vector3();
        currentPos.lerpVectors(startPosition, targetPosition, easeProgress);

        // إضافة الانحناء الجيلي (قوس مع تذبذب)
        const arcProgress = Math.sin(progress * Math.PI);
        currentPos.y += arcProgress * arcHeight;

        object.position.copy(currentPos);

        // تحديث موضع نص آخر مرسل إذا كان موجوداً
        if (object.userData.lastSenderText) {
          object.userData.lastSenderText.position.copy(currentPos);
          object.userData.lastSenderText.position.y += 2.5; // فوق المكعب (نفس موضع النص الجديد)
          object.userData.lastSenderText.lookAt(this.camera.position);
        }

        // تحديث موضع عداد الهدايا إذا كان موجوداً
        if (object.userData.giftCountText) {
          object.userData.giftCountText.position.copy(currentPos);
          object.userData.giftCountText.position.y += 3.2; // فوق اسم المستخدم
          object.userData.giftCountText.lookAt(this.camera.position);
        }

        // تحديث موضع التاج إذا كان موجوداً
        if (object.userData.crown) {
          this.updateCrownPosition(object.userData.crown, object);
        }

        // تأثير دوران جيلي للدول الرائدة
        if (isTopRank) {
          object.rotation.y += Math.sin(progress * Math.PI * 4) * 0.02;
        }

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          // التأكد من الوصول للموضع النهائي
          object.position.copy(targetPosition);
          object.rotation.y = 0; // إعادة تعيين الدوران

          // تحديث موضع النص النهائي
          if (object.userData.lastSenderText) {
            object.userData.lastSenderText.position.copy(targetPosition);
            object.userData.lastSenderText.position.y += 2.5; // فوق المكعب (نفس موضع النص الجديد)
            object.userData.lastSenderText.lookAt(this.camera.position);
          }

          // تحديث موضع عداد الهدايا النهائي
          if (object.userData.giftCountText) {
            object.userData.giftCountText.position.copy(targetPosition);
            object.userData.giftCountText.position.y += 3.2; // فوق اسم المستخدم
            object.userData.giftCountText.lookAt(this.camera.position);
          }

          // تحديث موضع التاج النهائي
          if (object.userData.crown) {
            this.updateCrownPosition(object.userData.crown, object);
          }

          resolve();
        }
      };

      animate();
    });
  }

  /**
   * تأثير easing جيلي مع تذبذب
   */
  jellyEasing(t) {
    // تأثير جيلي مع تذبذب خفيف في النهاية
    if (t < 0.7) {
      // حركة سريعة في البداية
      return 2 * t * t;
    } else {
      // تذبذب جيلي في النهاية
      const bounce = t - 0.7;
      const bounceEffect = Math.sin(bounce * Math.PI * 6) * (1 - bounce) * 0.1;
      return 0.98 + bounceEffect + (t - 0.7) * 0.067;
    }
  }

  /**
   * تحديد الدولة بناءً على نوع الهدية
   */
  detectCountryByGiftType(giftName) {
    for (const [countryCode, country] of Object.entries(this.countries)) {
      if (country.giftType === giftName) {
        console.log(`🎯 تم العثور على دولة مرتبطة بـ ${giftName}: ${countryCode}`);
        return countryCode;
      }
    }
    return null;
  }

  /**
   * تحديد دولة المستخدم (مبسط)
   */
  detectUserCountry(username) {
    // خوارزمية بسيطة لتحديد الدولة
    const countryHints = {
      'EG': ['مصر', 'egypt', 'cairo', 'القاهرة', 'مصري', 'egyptian'],
      'SA': ['سعودي', 'saudi', 'riyadh', 'الرياض', 'السعودية', 'سعوديه'],
      'DZ': ['جزائر', 'algeria', 'algiers', 'الجزائر', 'جزائري', 'algerian'],
      'MA': ['مغرب', 'morocco', 'rabat', 'الرباط', 'مغربي', 'moroccan'],
      'IQ': ['عراق', 'iraq', 'baghdad', 'بغداد', 'عراقي', 'iraqi', 'العراق'],
      'AE': ['uae', 'emirates', 'dubai', 'الإمارات', 'إماراتي', 'ابوظبي', 'abudhabi'],
      'TN': ['تونس', 'tunisia', 'tunis', 'تونسي', 'tunisian'],
      'SY': ['سوريا', 'syria', 'damascus', 'دمشق', 'سوري', 'syrian'],
      'LB': ['لبنان', 'lebanon', 'beirut', 'بيروت', 'لبناني', 'lebanese'],
      'QA': ['قطر', 'qatar', 'doha', 'الدوحة', 'قطري', 'qatari'],
      'KW': ['كويت', 'kuwait', 'الكويت', 'كويتي', 'kuwaiti'],
      'BH': ['بحرين', 'bahrain', 'manama', 'المنامة', 'بحريني', 'bahraini', 'البحرين']
    };

    const lowerUsername = username.toLowerCase();

    for (const [countryCode, hints] of Object.entries(countryHints)) {
      if (hints.some(hint => lowerUsername.includes(hint))) {
        return countryCode;
      }
    }

    // افتراضي: اختيار عشوائي
    const countryKeys = Object.keys(this.countries);
    return countryKeys[Math.floor(Math.random() * countryKeys.length)];
  }

  /**
   * حساب قوة الهدية
   * تم إضافة جميع هدايا TikTok الحقيقية (133 هدية) مع قيمها الصحيحة
   */
  calculateGiftPower(giftName, repeatCount) {
    const giftValues = {
      // هدايا قوة 1
      "Love you so much": 1,
      "Wink Charm": 1,
      "GG": 1,
      "Ice Cream Cone": 1,
      "Rose": 1,
      "TikTok": 1,
      "Heart Me": 1,
      "Thumbs Up": 1,
      "Cake Slice": 1,
      "Glow Stick": 1,
      "Love you": 1,
      "Enjoy Music": 1,
      "Happy Eid": 1,
      "Music Mic": 1,
      "Level Up": 1,
      "really hot": 1,
      "Go Popular": 1,
      "Club Cheers": 1,

      // هدايا قوة متوسطة
      "Team Bracelet": 2,
      "Finger Heart": 5,
      "Yeet": 8,
      "Cheer You Up": 9,
      "Club Power": 9,
      "Heart": 10,
      "Heart Gaze": 10,
      "Friendship Necklace": 10,
      "Rosa": 10,
      "Stitch": 10,
      "Go Go Go": 10,
      "Celestial Badge": 10,
      "Gold Boxing Gloves": 10,
      "Next Legends": 15,
      "Perfume": 20,
      "Doughnut": 30,
      "Witchy Kitty": 30,
      "Butterfly": 88,
      "Family": 90,

      // هدايا قوة 99
      "Paper Crane": 99,
      "Little Crown": 99,
      "Cap": 99,
      "Hat and Mustache": 99,
      "Noor": 99,
      "Like-Pop": 99,
      "Love Painting": 99,
      "Little Wing": 99,
      "Love Chain": 99,
      "Bubble Gum": 99,
      "Mark of Love": 99,
      "Star": 99,
      "Club Victory": 99,
      "Level-up Sparks": 99,
      "Greeting Heart": 99,

      // هدايا قوة 100
      "Singing Magic": 100,
      "Confetti": 100,
      "Hand Hearts": 100,
      "Hand Heart": 100,
      "Balloon Gift Box": 100,
      "Shell Energy": 100,
      "Mini Star": 100,
      "Bouquet": 100,
      "Marvelous Confetti": 100,

      // هدايا قوة 149-150
      "Heart Rain": 149,
      "Bowknot": 149,
      "Dizzy Bird": 149,
      "Big Shout Out": 149,
      "Chatting Popcorn": 149,
      "Kiss": 150,
      "Sceptre": 150,

      // هدايا قوة 199
      "Love Charger": 199,
      "Sunglasses": 199,
      "Hearts": 199,
      "Garland Headpiece": 199,
      "Love You": 199,
      "Cheer For You": 199,
      "Stinging Bee": 199,
      "Massage for You": 199,
      "Coffee Magic": 199,
      "Dancing Hands": 199,
      "Meerkat": 199,
      "Cheering Crab": 199,
      "The Crown": 199,
      "Night Star": 199,
      "Twinkling Star": 199,
      "Floating Octopus": 199,
      "Flower Headband": 199,

      // هدايا قوة 249-299
      "Pinch Face": 249,
      "Candy Bouquet": 249,
      "Hawaiian Lei": 299,
      "Boxing Gloves": 299,
      "Corgi": 299,
      "Victory Sign": 299,
      "Fruit Friends": 299,
      "Naughty Chicken": 299,
      "Play for You": 299,
      "Rock Star": 299,
      "Elephant trunk": 299,
      "Butterfly for You": 299,
      "Golden Crown": 299,
      "Starlight Compass": 299,
      "Puppy Kisses": 299,
      "LIVE Ranking Crown": 299,
      "United Heart": 299,
      "Budding Heart": 299,

      // هدايا قوة 300-399
      "Birthday Cake": 300,
      "Feather Mask": 300,
      "Forever Rosa": 399,
      "Magic Rhythm": 399,
      "Relaxed Goose": 399,
      "Tom's Hug": 399,
      "Rosie the Rose Bean": 399,
      "Jollie the Joy Bean": 399,
      "Rocky the Rock Bean": 399,
      "Sage the Smart Bean": 399,
      "Sage's Slash": 399,
      "Let butterfly dances": 399,
      "Kitten Kneading": 399,
      "Shoot the Apple": 399,
      "Alien Buddy": 399,

      // هدايا قوة 400-450
      "Crystal Dreams": 400,
      "Wishing Cake": 400,
      "Mic Champ": 400,
      "Beating Heart": 449,
      "Fairy Mask": 450,
      "Powerful Mind": 450,
      "Hat of Joy": 450,

      // هدايا قوة 499-500
      "Coral": 499,
      "Hands Up": 499,
      "Flower Show": 500,
      "Money Gun": 500,

      // الهدايا القديمة للتوافق مع النسخة السابقة
      'Diamond': 5,
      'Crown': 10,
      'Rocket': 20,
      'default': 1
    };

    const baseValue = giftValues[giftName] || giftValues.default;
    return baseValue * repeatCount;
  }

  /**
   * معالجة انضمام مستخدم لفريق
   */
  handleTeamJoin(data) {
    const { username, countryCode, teamNumber } = data;

    // التحقق من وجود الدولة
    if (!this.countries[countryCode]) {
      console.error(`❌ الدولة ${countryCode} غير موجودة`);
      return;
    }

    // إزالة المستخدم من أي فريق سابق
    this.removeUserFromAllTeams(username);

    // إضافة المستخدم للفريق الجديد
    if (!this.teamMembers[countryCode]) {
      this.teamMembers[countryCode] = [];
    }

    this.teamMembers[countryCode].push(username);
    this.userTeams[username] = countryCode;

    // عرض رسالة الانضمام
    const country = this.countries[countryCode];
    const countryName = country.customName || country.name;
    const message = `🎉 ${username} انضم إلى فريق ${countryName}!`;

    this.showTeamJoinMessage(message);
    this.updateTeamCounters();
  }

  /**
   * معالجة مغادرة مستخدم للفريق
   */
  handleTeamLeave(data) {
    const { username } = data;
    const currentTeam = this.userTeams[username];

    if (currentTeam) {
      this.removeUserFromAllTeams(username);

      const country = this.countries[currentTeam];
      const countryName = country.customName || country.name;
      const message = `👋 ${username} غادر فريق ${countryName}`;

      this.showTeamJoinMessage(message);
      this.updateTeamCounters();

      console.log(`👋 ${username} غادر الفريق ${currentTeam}`);
    }
  }

  /**
   * معالجة استعلام المستخدم عن فريقه
   */
  handleTeamQuery(data) {
    const { username } = data;
    const currentTeam = this.userTeams[username];

    if (currentTeam) {
      const country = this.countries[currentTeam];
      const countryName = country.customName || country.name;
      const message = `👤 ${username} في فريق ${countryName}`;
      this.showTeamJoinMessage(message);
    } else {
      const message = `❓ ${username} غير منضم لأي فريق`;
      this.showTeamJoinMessage(message);
    }
  }

  /**
   * معالجة استعلام عن قائمة الفرق
   */
  handleTeamsQuery(data) {
    const { username } = data;
    let message = `📋 قائمة الفرق:\n`;

    Object.keys(this.countries).forEach((countryCode, index) => {
      const country = this.countries[countryCode];
      const countryName = country.customName || country.name;
      const memberCount = this.teamMembers[countryCode] ? this.teamMembers[countryCode].length : 0;
      message += `${index + 1}. ${countryName} (${memberCount} عضو)\n`;
    });

    this.showTeamJoinMessage(message);
  }

  /**
   * إزالة مستخدم من جميع الفرق
   */
  removeUserFromAllTeams(username) {
    // إزالة من الفريق السابق
    const oldTeam = this.userTeams[username];
    if (oldTeam && this.teamMembers[oldTeam]) {
      const index = this.teamMembers[oldTeam].indexOf(username);
      if (index > -1) {
        this.teamMembers[oldTeam].splice(index, 1);
      }
    }

    // إزالة من قاموس المستخدمين
    delete this.userTeams[username];
  }

  /**
   * عرض رسالة انضمام الفريق
   */
  showTeamJoinMessage(message) {
    // إضافة الرسالة مع معرف فريد ووقت الإنشاء
    const messageObj = {
      id: Date.now() + Math.random(),
      text: message,
      timestamp: Date.now()
    };

    this.teamJoinMessages.unshift(messageObj);

    // الاحتفاظ بآخر 5 رسائل فقط
    if (this.teamJoinMessages.length > 5) {
      this.teamJoinMessages = this.teamJoinMessages.slice(0, 5);
    }

    // تحديث العرض
    this.updateTeamJoinMessagesDisplay();

    // إزالة الرسالة بعد ثانية واحدة
    setTimeout(() => {
      this.removeTeamJoinMessage(messageObj.id);
    }, 3000);
  }

  /**
   * إزالة رسالة انضمام محددة
   */
  removeTeamJoinMessage(messageId) {
    // البحث عن العنصر في DOM
    const messageElement = document.querySelector(`[data-id="${messageId}"]`);
    if (messageElement) {
      // إضافة كلاس الاختفاء
      messageElement.classList.add('fade-out');

      // انتظار انتهاء التأثير ثم الحذف
      setTimeout(() => {
        // البحث عن الرسالة وإزالتها من القائمة
        const messageIndex = this.teamJoinMessages.findIndex(msg => msg.id === messageId);
        if (messageIndex > -1) {
          this.teamJoinMessages.splice(messageIndex, 1);

          // تحديث العرض
          this.updateTeamJoinMessagesDisplay();
        }
      }, 300); // مدة التأثير
    }
  }

  /**
   * تحديث عرض رسائل الانضمام
   */
  updateTeamJoinMessagesDisplay() {
    const container = document.getElementById('teamJoinMessages');

    if (!container) {
      // إنشاء الحاوية إذا لم تكن موجودة
      this.createTeamJoinMessagesContainer();
      return;
    }

    const html = this.teamJoinMessages
      .map(messageObj => `<div class="team-join-message" data-id="${messageObj.id}">${messageObj.text}</div>`)
      .join('');

    container.innerHTML = html;
  }

  /**
   * إنشاء حاوية رسائل الانضمام
   */
  createTeamJoinMessagesContainer() {
    const container = document.createElement('div');
    container.id = 'teamJoinMessages';
    container.className = 'team-join-messages';

    // إضافة الحاوية إلى body مباشرة
    document.body.appendChild(container);

    // تحديث العرض
    this.updateTeamJoinMessagesDisplay();
  }

  /**
   * تحديث عدادات الفرق
   */
  updateTeamCounters() {
    Object.keys(this.countries).forEach(countryCode => {
      const memberCount = this.teamMembers[countryCode] ? this.teamMembers[countryCode].length : 0;
      this.countries[countryCode].teamMemberCount = memberCount;
    });

    // تحديث العرض البصري
    this.updateUI();
  }

  /**
   * تنفيذ فوري للهدايا الكثيرة جداً (أكثر من 100)
   */
  executeInstantGifts(country, giftCount, userData) {
    console.log(`⚡ تنفيذ فوري لـ ${giftCount} هدية للدولة ${country.name}`);

    // تنفيذ تأثير Pop واحد قوي بدلاً من تأثيرات متعددة
    this.growCube(country, 0, userData);

    // تأخير تحديث العداد حتى وصول الكرة (1 ثانية)
    setTimeout(() => {
      this.updateGiftCountText(country);
      // تحديث التاج بعد تحديث العداد
      this.updateLeader();
    }, 1000);
    this.updateLastSenderText(country);

    // إضافة تأثير بصري خاص للهدايا الكثيرة
    this.createMassiveGiftEffect(country, giftCount);

    console.log(`🎆 تم تنفيذ ${giftCount} هدية فورياً للدولة ${country.name}`);
  }

  /**
   * تنفيذ هدايا متعددة بشكل متتالي مع تحسين السرعة
   */
  executeMultipleGifts(country, giftCount, userData) {
    console.log(`🚀 بدء تنفيذ ${giftCount} هدية للدولة ${country.name}`);

    // تحديد التأخير حسب عدد الهدايا لتحسين الأداء
    let delay;
    if (giftCount > 50) {
      delay = 20; // تنفيذ سريع جداً للهدايا الكثيرة
    } else if (giftCount > 20) {
      delay = 30; // تنفيذ سريع للهدايا المتوسطة
    } else if (giftCount > 10) {
      delay = 50; // تنفيذ متوسط
    } else {
      delay = 100; // تنفيذ عادي للهدايا القليلة
    }

    console.log(`⚡ استخدام تأخير ${delay}ms لـ ${giftCount} هدية`);

    // تنفيذ الهدايا بشكل متتالي مع التأخير المحسن
    for (let i = 0; i < giftCount; i++) {
      setTimeout(() => {
        // تنفيذ تأثير Pop لكل هدية
        this.growCube(country, 0, userData);

        console.log(`💥 تنفيذ الهدية ${i + 1} من ${giftCount} للدولة ${country.name}`);

        // تحديث العداد في الهدية الأخيرة فقط لتجنب التحديث المتكرر
        if (i === giftCount - 1) {
          // تأخير تحديث العداد حتى وصول الكرة (1 ثانية)
          setTimeout(() => {
            this.updateGiftCountText(country);
            // تحديث التاج بعد تحديث العداد
            this.updateLeader();
          }, 1000);
        }
      }, i * delay); // استخدام التأخير المحسن
    }
  }

  /**
   * إنشاء تأثير بصري خاص للهدايا الكثيرة
   */
  createMassiveGiftEffect(country, giftCount) {
    if (!country.cube) return;

    // تأثير انفجار كبير
    for (let i = 0; i < 20; i++) {
      setTimeout(() => {
        this.createExplosion(country.cube.position, {
          color: 0xFFD700, // ذهبي
          size: 2,
          count: 50
        });
      }, i * 50);
    }

    // تأثير وهج قوي
    this.createIntenseGlow(country.cube);

    console.log(`🎆 تم إنشاء تأثير بصري خاص لـ ${giftCount} هدية`);
  }

  /**
   * إنشاء تأثير انفجار
   */
  createExplosion(position, options = {}) {
    const color = options.color || 0xFFD700;
    const size = options.size || 1;
    const count = options.count || 30;

    for (let i = 0; i < count; i++) {
      const particle = {
        position: position.clone(),
        velocity: new THREE.Vector3(
          (Math.random() - 0.5) * 10,
          Math.random() * 8 + 2,
          (Math.random() - 0.5) * 10
        ),
        life: 1.0,
        maxLife: 1.0,
        size: size,
        color: color
      };

      this.particles.push(particle);
    }
  }

  /**
   * إنشاء وهج قوي للمكعب
   */
  createIntenseGlow(cube) {
    if (!cube) return;

    // إنشاء هالة ذهبية قوية
    const glowGeometry = new THREE.SphereGeometry(3, 16, 16);
    const glowMaterial = new THREE.MeshBasicMaterial({
      color: 0xFFD700,
      transparent: true,
      opacity: 0.3
    });

    const glow = new THREE.Mesh(glowGeometry, glowMaterial);
    glow.position.copy(cube.position);
    this.scene.add(glow);

    // أنيميشن الوهج
    let scale = 1;
    const animate = () => {
      scale += 0.1;
      glow.scale.set(scale, scale, scale);
      glow.material.opacity = Math.max(0, 0.3 - scale * 0.05);

      if (glow.material.opacity > 0) {
        requestAnimationFrame(animate);
      } else {
        this.scene.remove(glow);
        glow.geometry.dispose();
        glow.material.dispose();
      }
    };

    animate();
  }

  /**
   * تأثير Pop للمكعب مع كرة المستخدم وعرض اسم آخر مرسل
   */
  growCube(country, power, userData = null) {
    if (!country.cube) return;

    // حفظ اسم آخر مرسل للدولة
    if (userData && userData.nickname) {
      country.lastSender = userData.nickname;
      console.log(`📝 حفظ اسم آخر مرسل: ${userData.nickname} للدولة: ${country.name}`);
      // تحديث نص اسم آخر مرسل على المكعب
      this.updateLastSenderText(country);
    } else {
      console.log('⚠️ لا توجد بيانات مستخدم لحفظ اسم المرسل');
    }

    // لا نحدث العداد هنا - سيتم تحديثه في executeMultipleGifts

    // إنشاء كرة المستخدم إذا كانت هناك بيانات مستخدم (شروط مخففة)
    if (userData && userData.nickname) {
      console.log(`🎯 محاولة إنشاء كرة المستخدم لـ: ${userData.nickname}`);
      this.createAndAnimateUserSphere(country.cube, userData);
    } else {
      console.log('⚠️ لا توجد بيانات مستخدم كافية - تنفيذ Pop مباشر');
      // تأثير Pop مباشر بدون كرة مستخدم (مع فحص التداخل)
      this.animatePopEffectSafe(country.cube);
    }

    console.log(`💥 ${country.name} تأثير Pop - كأن شيء دخل في المكعب!`);
  }

  /**
   * تحديث نص اسم آخر مرسل على المكعب
   */
  updateLastSenderText(country) {
    console.log(`🔄 محاولة تحديث نص آخر مرسل للدولة: ${country.name}`);

    if (!country.cube) {
      console.log('❌ لا يوجد مكعب للدولة');
      return;
    }

    if (!country.lastSender) {
      console.log('❌ لا يوجد اسم آخر مرسل');
      return;
    }

    console.log(`✅ إنشاء نص لآخر مرسل: ${country.lastSender}`);

    // إزالة النص السابق إن وجد
    if (country.cube.userData.lastSenderText) {
      console.log('🗑️ إزالة النص السابق');
      this.scene.remove(country.cube.userData.lastSenderText);
      country.cube.userData.lastSenderText.geometry.dispose();
      country.cube.userData.lastSenderText.material.dispose();
    }

    // إنشاء نص جديد لاسم آخر مرسل
    const senderText = this.createLastSenderText(country.lastSender, country.cube.position);

    // حفظ مرجع النص في userData للمكعب
    country.cube.userData.lastSenderText = senderText;

    console.log(`🎯 تم إنشاء وإضافة نص آخر مرسل بنجاح`);
  }

  /**
   * تحديث عداد الهدايا على المكعب مع تأثير نمو جميل
   */
  updateGiftCountText(country) {
    if (!country.cube) return;

    // إزالة العداد السابق إن وجد
    if (country.cube.userData.giftCountText) {
      this.scene.remove(country.cube.userData.giftCountText);
      country.cube.userData.giftCountText.geometry.dispose();
      country.cube.userData.giftCountText.material.dispose();
    }

    // إنشاء عداد جديد
    const countText = this.createGiftCountText(country.giftCount, country.cube.position);
    country.cube.userData.giftCountText = countText;

    // تأثير نمو جميل للعداد
    this.animateCounterGrowth(countText);
  }

  /**
   * تأثير نمو جميل للعداد - كبير وواضح
   */
  animateCounterGrowth(textMesh) {
    const originalScale = new THREE.Vector3(2.5, 1.2, 1); // حجم كبير
    const growScale = new THREE.Vector3(3, 1.5, 1); // تكبير واضح

    textMesh.scale.copy(originalScale);

    const startTime = Date.now();
    const growDuration = 150; // أسرع
    const shrinkDuration = 100; // أسرع

    // مرحلة النمو
    const animateGrow = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / growDuration, 1);

      const easeProgress = 1 - Math.pow(1 - progress, 3);
      textMesh.scale.lerpVectors(originalScale, growScale, easeProgress);

      if (progress < 1) {
        requestAnimationFrame(animateGrow);
      } else {
        // بدء مرحلة العودة
        const shrinkStartTime = Date.now();

        const animateShrink = () => {
          const elapsed = Date.now() - shrinkStartTime;
          const progress = Math.min(elapsed / shrinkDuration, 1);

          const easeProgress = progress * progress * (3 - 2 * progress);
          textMesh.scale.lerpVectors(growScale, originalScale, easeProgress);

          if (progress < 1) {
            requestAnimationFrame(animateShrink);
          } else {
            textMesh.scale.copy(originalScale);
          }
        };

        animateShrink();
      }
    };

    animateGrow();
  }

  /**
   * إنشاء نص عداد الهدايا - رقم كبير وواضح وصافي
   */
  createGiftCountText(giftCount, cubePosition) {
    // إنشاء canvas كبير للرقم
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 256;
    canvas.height = 128;

    // تحسين جودة الرسم
    context.imageSmoothingEnabled = true;
    context.imageSmoothingQuality = 'high';

    // مسح Canvas (شفاف بالكامل)
    context.clearRect(0, 0, canvas.width, canvas.height);

    // إعداد النص الكبير والواضح مع خط جميل
    context.fillStyle = '#FFFFFF'; // أبيض ناصع
    context.strokeStyle = '#000000'; // حدود سوداء كاملة
    context.lineWidth = 6; // حدود سميكة
    context.font = '48px "Comic Sans MS", cursive'; // خط Comic Sans MS ممتع
    context.textAlign = 'center';
    context.textBaseline = 'middle';

    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    // رسم الحدود السوداء (طبقات متعددة للوضوح)
    context.strokeText(giftCount.toString(), centerX, centerY);
    context.lineWidth = 4;
    context.strokeText(giftCount.toString(), centerX, centerY);
    context.lineWidth = 2;
    context.strokeText(giftCount.toString(), centerX, centerY);

    // رسم النص الأبيض فوق الحدود
    context.fillText(giftCount.toString(), centerX, centerY);

    // إنشاء نسيج عالي الجودة
    const texture = new THREE.CanvasTexture(canvas);
    texture.generateMipmaps = false;
    texture.minFilter = THREE.LinearFilter;
    texture.magFilter = THREE.LinearFilter;

    const textMaterial = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      alphaTest: 0.1
    });

    // إنشاء هندسة مناسبة للرقم - أقل عرض
    const textGeometry = new THREE.PlaneGeometry(1.2, 1.2); // مربع بدلاً من مستطيل عريض
    const textMesh = new THREE.Mesh(textGeometry, textMaterial);

    // وضع الرقم فوق اسم المستخدم
    textMesh.position.copy(cubePosition);
    textMesh.position.y += 3.2; // فوق اسم المستخدم (2.5 + 0.7)

    // جعل الرقم يواجه الكاميرا
    textMesh.lookAt(this.camera.position);

    // إضافة للمشهد
    this.scene.add(textMesh);

    return textMesh;
  }

  /**
   * إنشاء نص اسم آخر مرسل للمكعب
   */
  createLastSenderText(senderName, cubePosition) {
    console.log(`🎨 إنشاء نص لاسم: ${senderName} في الموضع:`, cubePosition);

    // إنشاء canvas لرسم النص - أكبر للوضوح
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 512; // تكبير العرض من 256 إلى 512
    canvas.height = 96; // تكبير الارتفاع من 48 إلى 96

    // مسح Canvas (شفاف بالكامل)
    context.clearRect(0, 0, canvas.width, canvas.height);

    // رسم النص بدون خلفية - أبيض مع حدود داكنة كاملة
    context.fillStyle = '#FFFFFF'; // نص أبيض
    context.strokeStyle = '#000000'; // حدود سوداء كاملة (داكنة 100%)
    context.lineWidth = 8; // حدود أسمك للوضوح الكامل
    context.font = 'bold 36px Arial'; // خط أكبر من 18px إلى 36px
    context.textAlign = 'center';
    context.textBaseline = 'middle';

    // إزالة أي توهج أولاً للحدود الواضحة
    context.shadowColor = 'transparent';
    context.shadowBlur = 0;
    context.shadowOffsetX = 0;
    context.shadowOffsetY = 0;

    // رسم الحدود السوداء الكاملة أولاً (متعددة الطبقات للوضوح)
    context.strokeText(senderName, canvas.width / 2, canvas.height / 2);

    // رسم طبقة إضافية من الحدود للتأكد من الوضوح
    context.lineWidth = 6;
    context.strokeText(senderName, canvas.width / 2, canvas.height / 2);

    // رسم طبقة ثالثة للحدود
    context.lineWidth = 4;
    context.strokeText(senderName, canvas.width / 2, canvas.height / 2);

    // أخيراً رسم النص الأبيض فوق الحدود
    context.fillText(senderName, canvas.width / 2, canvas.height / 2);

    console.log('✅ تم رسم النص على Canvas');

    // إنشاء نسيج من Canvas
    const texture = new THREE.CanvasTexture(canvas);

    // إنشاء مادة النص مع شفافية كاملة للخلفية
    const textMaterial = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      alphaTest: 0.1
    });

    // إنشاء هندسة مستطيلة للنص - أكبر للوضوح
    const textGeometry = new THREE.PlaneGeometry(3.6, 0.8); // تكبير من 1.8×0.4 إلى 3.6×0.8
    const textMesh = new THREE.Mesh(textGeometry, textMaterial);

    // وضع النص فوق المكعب
    textMesh.position.copy(cubePosition);
    textMesh.position.y += 2.5; // أعلى قليلاً من 2 إلى 2.5

    console.log(`📍 موضع النص النهائي:`, textMesh.position);

    // جعل النص يواجه الكاميرا دائماً
    textMesh.lookAt(this.camera.position);

    // إضافة النص للمشهد
    this.scene.add(textMesh);

    console.log('🎯 تم إضافة النص للمشهد بنجاح');

    return textMesh;
  }

  /**
   * إنشاء وتحريك كرة المستخدم من موضع الاتصال إلى المكعب
   */
  async createAndAnimateUserSphere(targetCube, userData) {
    try {
      console.log(`🚀 بدء إنشاء كرة المستخدم لـ: ${userData.nickname}`);

      // محاولة الحصول على موضع نص الاتصال
      const connectionStatus = document.querySelector('.connection-status');
      let startPosition;

      if (connectionStatus) {
        // حساب موضع البداية من عنصر الاتصال
        const rect = connectionStatus.getBoundingClientRect();
        startPosition = this.screenToWorldPosition(
          rect.left + rect.width / 2,
          rect.top + rect.height / 2
        );
        console.log(`📍 استخدام موضع عنصر الاتصال`);
      } else {
        // 🎯 استخدام موضع افتراضي مع التتبع الذكي
        console.log('⚠️ لم يتم العثور على عنصر حالة الاتصال - استخدام موضع افتراضي مع التتبع');
        startPosition = new THREE.Vector3(
          targetCube.position.x + 5, // يمين المكعب الحالي
          targetCube.position.y + 8, // فوق المكعب الحالي
          targetCube.position.z + 5  // أمام المكعب الحالي
        );
      }

      // إنشاء كرة المستخدم
      const userSphere = await this.createUserSphere(userData, startPosition);

      // إنشاء نص اسم المستخدم
      const userNameText = this.createUserNameText(userData.nickname, startPosition);

      // تحريك الكرة والنص إلى المكعب
      await this.animateUserSphereToTarget(userSphere, userNameText, targetCube);

      // تنفيذ تأثير Pop بعد وصول الكرة
      this.animatePopEffect(targetCube);
      console.log('✅ تم إنشاء وتحريك كرة المستخدم بنجاح');

    } catch (error) {
      console.error('❌ خطأ في إنشاء كرة المستخدم:', error);
      console.error('📋 تفاصيل الخطأ:', error.message);
      console.error('👤 بيانات المستخدم:', userData);

      // تنفيذ تأثير Pop العادي في حالة الخطأ
      console.log('🔄 تنفيذ تأثير Pop احتياطي');
      this.animatePopEffect(targetCube);
    }
  }

  /**
   * تحويل إحداثيات الشاشة إلى إحداثيات العالم ثلاثي الأبعاد
   */
  screenToWorldPosition(screenX, screenY) {
    const vector = new THREE.Vector3();

    // تحويل إحداثيات الشاشة إلى إحداثيات مُطبعة (-1 إلى +1)
    vector.x = (screenX / window.innerWidth) * 2 - 1;
    vector.y = -(screenY / window.innerHeight) * 2 + 1;
    vector.z = 0.5;

    // إلغاء الإسقاط للحصول على الموضع في العالم
    vector.unproject(this.camera);

    // حساب الاتجاه من الكاميرا
    const dir = vector.sub(this.camera.position).normalize();

    // حساب المسافة إلى مستوى Y = 5 (فوق المكعبات)
    const distance = (5 - this.camera.position.y) / dir.y;

    // حساب الموضع النهائي
    const worldPosition = this.camera.position.clone().add(dir.multiplyScalar(distance));

    return worldPosition;
  }

  /**
   * إنشاء كرة المستخدم مع صورة الملف الشخصي
   */
  async createUserSphere(userData, position) {
    console.log(`🎨 إنشاء كرة المستخدم لـ: ${userData.nickname}`);

    // إنشاء هندسة الكرة
    const sphereGeometry = new THREE.SphereGeometry(0.8, 16, 16);

    // تحميل صورة الملف الشخصي (مع تحسين معالجة الأخطاء)
    let sphereMaterial;

    // التحقق من وجود رابط صورة الملف الشخصي
    if (userData.profilePictureUrl && userData.profilePictureUrl.startsWith('http')) {
      try {
        console.log(`🖼️ محاولة تحميل صورة الملف الشخصي: ${userData.profilePictureUrl}`);
        const textureLoader = new THREE.TextureLoader();
        const profileTexture = await new Promise((resolve, reject) => {
          textureLoader.load(
            userData.profilePictureUrl,
            (texture) => {
              console.log('✅ تم تحميل صورة الملف الشخصي بنجاح');
              resolve(texture);
            },
            undefined,
            (error) => {
              console.log('❌ فشل تحميل صورة الملف الشخصي:', error);
              reject(error);
            }
          );
        });

        sphereMaterial = new THREE.MeshLambertMaterial({
          map: profileTexture
        });
        console.log('🎯 تم إنشاء مادة الكرة مع صورة الملف الشخصي');
      } catch (error) {
        console.log('⚠️ فشل تحميل صورة الملف الشخصي، استخدام لون افتراضي');
        sphereMaterial = this.createDefaultSphereMaterial(userData.nickname);
      }
    } else {
      console.log('⚠️ لا يوجد رابط صورة ملف شخصي صالح، استخدام لون افتراضي');
      sphereMaterial = this.createDefaultSphereMaterial(userData.nickname);
    }

    // إنشاء الكرة
    const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
    sphere.position.copy(position);
    sphere.castShadow = true;

    // إضافة الكرة للمشهد
    this.scene.add(sphere);
    console.log('🌟 تم إضافة كرة المستخدم للمشهد بنجاح');

    return sphere;
  }

  /**
   * إنشاء مادة افتراضية للكرة مع لون مميز
   */
  createDefaultSphereMaterial(nickname) {
    // إنشاء لون مميز بناءً على اسم المستخدم
    const colors = [
      0x4CAF50, // أخضر
      0x2196F3, // أزرق
      0xFF9800, // برتقالي
      0x9C27B0, // بنفسجي
      0xF44336, // أحمر
      0x00BCD4, // سماوي
      0x8BC34A, // أخضر فاتح
      0xFF5722  // برتقالي محمر
    ];

    // اختيار لون بناءً على اسم المستخدم
    let colorIndex = 0;
    if (nickname) {
      for (let i = 0; i < nickname.length; i++) {
        colorIndex += nickname.charCodeAt(i);
      }
    }
    const selectedColor = colors[colorIndex % colors.length];

    console.log(`🎨 تم اختيار اللون ${selectedColor.toString(16)} لـ ${nickname}`);

    return new THREE.MeshLambertMaterial({
      color: selectedColor
    });
  }

  /**
   * إنشاء نص اسم المستخدم فوق الكرة - بدون خلفية
   */
  createUserNameText(nickname, position) {
    // إنشاء canvas لرسم النص
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 256;
    canvas.height = 64;

    // مسح Canvas (شفاف بالكامل)
    context.clearRect(0, 0, canvas.width, canvas.height);

    // رسم النص بدون خلفية
    context.fillStyle = '#ffffff'; // نص أبيض
    context.strokeStyle = '#000000'; // حدود سوداء للوضوح
    context.lineWidth = 3;
    context.font = 'bold 24px Arial';
    context.textAlign = 'center';
    context.textBaseline = 'middle';

    // رسم الحدود أولاً (للوضوح)
    context.strokeText(nickname, canvas.width / 2, canvas.height / 2);
    // ثم رسم النص
    context.fillText(nickname, canvas.width / 2, canvas.height / 2);

    // إنشاء نسيج من Canvas
    const texture = new THREE.CanvasTexture(canvas);

    // إنشاء مادة النص مع شفافية كاملة للخلفية
    const textMaterial = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      alphaTest: 0.1 // إزالة البكسلات الشفافة تماماً
    });

    // إنشاء هندسة مستطيلة للنص
    const textGeometry = new THREE.PlaneGeometry(2, 0.5);
    const textMesh = new THREE.Mesh(textGeometry, textMaterial);

    // وضع النص فوق الكرة
    textMesh.position.copy(position);
    textMesh.position.y += 1.5;

    // جعل النص يواجه الكاميرا دائماً
    textMesh.lookAt(this.camera.position);

    // إضافة النص للمشهد
    this.scene.add(textMesh);

    return textMesh;
  }

  /**
   * تحريك كرة المستخدم والنص إلى المكعب المستهدف مع التتبع الذكي
   */
  animateUserSphereToTarget(sphere, textMesh, targetCube) {
    return new Promise((resolve) => {
      const startPosition = sphere.position.clone();
      const startTime = Date.now();
      const duration = 1000; // ثانية واحدة للوصول

      // حفظ الموضع الأصلي للنص
      const textStartPosition = textMesh.position.clone();

      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // تأثير easing منحني
        const easeProgress = 1 - Math.pow(1 - progress, 3);

        // 🎯 التتبع الذكي: الحصول على موضع المكعب الحالي في كل إطار
        const currentTargetPosition = targetCube.position.clone();
        currentTargetPosition.y += 1; // فوق المكعب قليلاً

        // حساب الموضع الحالي للكرة
        const currentPosition = new THREE.Vector3();
        currentPosition.lerpVectors(startPosition, currentTargetPosition, easeProgress);

        // إضافة مسار منحني (قوس)
        const arcHeight = 3;
        const arcProgress = Math.sin(progress * Math.PI);
        currentPosition.y += arcProgress * arcHeight;

        // تحديث موضع الكرة
        sphere.position.copy(currentPosition);

        // تحديث موضع النص (فوق الكرة)
        textMesh.position.copy(currentPosition);
        textMesh.position.y += 1.5;

        // جعل النص يواجه الكاميرا
        textMesh.lookAt(this.camera.position);

        // دوران الكرة أثناء الحركة
        sphere.rotation.x += 0.1;
        sphere.rotation.y += 0.1;

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          // وصلت الكرة للمكعب - إخفاؤها داخل المكعب
          this.hideUserSphereInCube(sphere, textMesh, targetCube);
          resolve();
        }
      };

      animate();
    });
  }

  /**
   * إخفاء كرة المستخدم والنص داخل المكعب
   */
  hideUserSphereInCube(sphere, textMesh, targetCube) {
    // تأثير اختفاء سريع
    const startTime = Date.now();
    const duration = 200; // 200ms للاختفاء

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // تقليل الحجم والشفافية
      const scale = 1 - progress;
      sphere.scale.setScalar(scale);
      textMesh.scale.setScalar(scale);

      // تقليل الشفافية
      if (sphere.material.opacity !== undefined) {
        sphere.material.opacity = scale;
      }
      if (textMesh.material.opacity !== undefined) {
        textMesh.material.opacity = scale;
      }

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        // إزالة الكرة والنص من المشهد
        this.scene.remove(sphere);
        this.scene.remove(textMesh);

        // تنظيف الذاكرة
        sphere.geometry.dispose();
        sphere.material.dispose();
        textMesh.geometry.dispose();
        textMesh.material.dispose();
      }
    };

    animate();
  }

  /**
   * تأثير Pop آمن - يتحقق من التداخل
   */
  animatePopEffectSafe(object) {
    // فحص التداخل - إذا كان Pop قيد التنفيذ، تجاهل
    if (object.userData.isPopping) {
      console.log(`⏳ تأثير Pop قيد التنفيذ - تم التجاهل`);
      return;
    }

    // تنفيذ Pop العادي
    this.animatePopEffect(object);
  }

  /**
   * تأثير Pop - تكبير سريع ثم عودة للحجم الطبيعي مع منع التداخل
   */
  animatePopEffect(object) {
    // منع تداخل التأثير
    if (object.userData.isPopping) {
      return;
    }

    // تعيين حالة Pop
    object.userData.isPopping = true;

    const originalScale = new THREE.Vector3(1, 1, 1); // الحجم الأصلي دائماً 1,1,1
    const popScale = new THREE.Vector3(1.3, 1.3, 1.3); // تكبير 30%
    const startTime = Date.now();
    const popDuration = 80; // 80ms للتكبير (سريع جداً)
    const returnDuration = 60; // 60ms للعودة (سريع جداً)

    // المرحلة الأولى: التكبير السريع
    const animatePop = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / popDuration, 1);

      // تأثير easing سريع للخارج
      const easeProgress = 1 - Math.pow(1 - progress, 2);

      object.scale.lerpVectors(originalScale, popScale, easeProgress);

      if (progress < 1) {
        requestAnimationFrame(animatePop);
      } else {
        // بدء المرحلة الثانية: العودة للحجم الطبيعي
        const returnStartTime = Date.now();

        const animateReturn = () => {
          const elapsed = Date.now() - returnStartTime;
          const progress = Math.min(elapsed / returnDuration, 1);

          // تأثير easing مرن للداخل (bounce)
          const easeProgress = progress < 0.5
            ? 2 * progress * progress
            : 1 - Math.pow(-2 * progress + 2, 2) / 2;

          object.scale.lerpVectors(popScale, originalScale, easeProgress);

          if (progress < 1) {
            requestAnimationFrame(animateReturn);
          } else {
            // التأكد من العودة للحجم الأصلي وإلغاء حالة Pop
            object.scale.copy(originalScale);
            object.userData.isPopping = false;
          }
        };

        animateReturn();
      }
    };

    animatePop();
  }

  /**
   * تحريك التكبير والموضع معاً بسلاسة
   */
  animateScaleAndPosition(object, targetScale, targetPosition, duration) {
    const startScale = object.scale.clone();
    const startPosition = object.position.clone();
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // تأثير easing
      const easeProgress = 1 - Math.pow(1 - progress, 3);

      object.scale.lerpVectors(startScale, targetScale, easeProgress);
      object.position.lerpVectors(startPosition, targetPosition, easeProgress);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    animate();
  }

  /**
   * تحريك التكبير بسلاسة (للاستخدام العام)
   */
  animateScale(object, targetScale, duration) {
    const startScale = object.scale.clone();
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // تأثير easing
      const easeProgress = 1 - Math.pow(1 - progress, 3);

      object.scale.lerpVectors(startScale, targetScale, easeProgress);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    animate();
  }

  /**
   * إنشاء تأثير بصري للهدية
   */
  createGiftEffect(country, giftData, countryCode) {
    if (!this.gameSettings.particleEffects || !country.cube) return;

    const position = country.cube.position.clone();

    // إنشاء جسيمات
    this.createParticleExplosion(position, country.color, 20);

    // تأثير إضاءة مؤقت
    this.createLightFlash(position, country.color);

    // رسالة تنبيه مع الاسم المخصص
    const displayName = this.getCountryDisplayName(countryCode);
    this.showAlert(`${country.flag} ${displayName} تتقدم!`);
  }

  /**
   * إنشاء انفجار جسيمات
   */
  createParticleExplosion(position, color, count) {
    for (let i = 0; i < count; i++) {
      const particle = {
        position: position.clone(),
        velocity: new THREE.Vector3(
          (Math.random() - 0.5) * 10,
          Math.random() * 8 + 2,
          (Math.random() - 0.5) * 10
        ),
        color: color,
        life: 60,
        maxLife: 60,
        size: Math.random() * 0.2 + 0.1
      };

      this.particles.push(particle);
    }
  }

  /**
   * إنشاء وميض إضاءة
   */
  createLightFlash(position, color) {
    const light = new THREE.PointLight(color, 2, 10);
    light.position.copy(position);
    this.scene.add(light);

    // تلاشي الإضاءة تدريجياً
    let intensity = 2;
    const fadeOut = () => {
      intensity -= 0.1;
      light.intensity = Math.max(intensity, 0);

      if (intensity > 0) {
        requestAnimationFrame(fadeOut);
      } else {
        this.scene.remove(light);
      }
    };

    fadeOut();
  }

  /**
   * عرض رسالة تنبيه
   */
  showAlert(message) {
    // إزالة التنبيه السابق إن وجد
    const existingAlert = document.querySelector('.alert-message');
    if (existingAlert) {
      existingAlert.remove();
    }

    // إنشاء تنبيه جديد
    const alert = document.createElement('div');
    alert.className = 'alert-message';
    alert.textContent = message;
    document.body.appendChild(alert);

    // إزالة التنبيه بعد 3 ثوان
    setTimeout(() => {
      if (alert.parentNode) {
        alert.remove();
      }
    }, 3000);
  }

  /**
   * تحديث الجسيمات
   */
  updateParticles() {
    this.particles = this.particles.filter(particle => {
      // تحديث الموضع
      particle.position.add(particle.velocity);

      // تطبيق الجاذبية
      particle.velocity.y -= 0.2;

      // تقليل العمر
      particle.life--;

      // إزالة الجسيمات المنتهية الصلاحية
      return particle.life > 0;
    });
  }

  /**
   * تحديث الانفجارات
   */
  updateExplosions() {
    this.explosions = this.explosions.filter(explosion => {
      explosion.life--;
      explosion.scale += 0.1;
      explosion.opacity -= 0.02;

      return explosion.life > 0;
    });
  }

  /**
   * تحديث القائد مع إضافة التاج (يستخدم giftCount مثل الترتيب الديناميكي)
   */
  updateLeader() {
    let newLeader = null;
    let maxGifts = 0;

    // استخدام giftCount بدلاً من power للتوافق مع الترتيب الديناميكي
    Object.entries(this.countries).forEach(([code, country]) => {
      if (country.giftCount > maxGifts) {
        maxGifts = country.giftCount;
        newLeader = code;
      }
    });

    if (newLeader !== this.gameState.currentLeader) {
      // إزالة التاج من القائد السابق
      if (this.gameState.currentLeader) {
        this.removeCrown(this.countries[this.gameState.currentLeader]);
      }

      this.gameState.currentLeader = newLeader;

      if (newLeader) {
        const leader = this.countries[newLeader];
        const displayName = this.getCountryDisplayName(newLeader);
        this.showAlert(`👑 ${leader.flag} ${displayName} تتصدر الآن!`);

        // إضافة التاج للقائد الجديد
        this.addCrown(leader);
      }
    }
  }

  /**
   * إضافة التاج للقائد مع ضوء لامع
   */
  addCrown(country) {
    if (!country.cube) return;

    // إزالة التاج السابق إن وجد
    this.removeCrown(country);

    // إنشاء التاج
    const crownGroup = new THREE.Group();

    // مادة ذهبية لامعة مع انعكاس
    const goldMaterial = new THREE.MeshPhongMaterial({
      color: 0xFFD700,
      shininess: 100,
      specular: 0xFFFFFF,
      emissive: 0x332200 // توهج ذهبي خفيف
    });

    // القاعدة الذهبية للتاج - أكبر وأجمل
    const baseGeometry = new THREE.CylinderGeometry(0.6, 0.8, 0.4, 12);
    const base = new THREE.Mesh(baseGeometry, goldMaterial);
    base.position.y = 0.2;
    crownGroup.add(base);

    // الأسنان العلوية للتاج - أجمل وأكثر تفصيلاً
    for (let i = 0; i < 8; i++) {
      const angle = (i / 8) * Math.PI * 2;
      const height = i % 2 === 0 ? 0.8 : 0.5; // أسنان متناوبة

      const toothGeometry = new THREE.ConeGeometry(0.12, height, 6);
      const tooth = new THREE.Mesh(toothGeometry, goldMaterial);

      tooth.position.x = Math.cos(angle) * 0.6;
      tooth.position.z = Math.sin(angle) * 0.6;
      tooth.position.y = 0.4 + height / 2;

      crownGroup.add(tooth);
    }

    // جواهر ملونة حول التاج
    const gemColors = [0xFF0000, 0x00FF00, 0x0000FF, 0xFF00FF]; // أحمر، أخضر، أزرق، بنفسجي
    for (let i = 0; i < 4; i++) {
      const angle = (i / 4) * Math.PI * 2;
      const gemGeometry = new THREE.SphereGeometry(0.08, 8, 8);
      const gemMaterial = new THREE.MeshPhongMaterial({
        color: gemColors[i],
        shininess: 150,
        emissive: gemColors[i],
        emissiveIntensity: 0.2
      });
      const gem = new THREE.Mesh(gemGeometry, gemMaterial);

      gem.position.x = Math.cos(angle) * 0.5;
      gem.position.z = Math.sin(angle) * 0.5;
      gem.position.y = 0.3;

      crownGroup.add(gem);
    }

    // ضوء لامع فوق التاج
    const crownLight = new THREE.PointLight(0xFFD700, 1.5, 8);
    crownLight.position.set(0, 1.2, 0);
    crownGroup.add(crownLight);

    // تأثير وهج ذهبي
    const glowGeometry = new THREE.SphereGeometry(1.2, 16, 16);
    const glowMaterial = new THREE.MeshBasicMaterial({
      color: 0xFFD700,
      transparent: true,
      opacity: 0.15
    });
    const glow = new THREE.Mesh(glowGeometry, glowMaterial);
    glow.position.y = 0.5;
    crownGroup.add(glow);

    // وضع التاج فوق المكعب مباشرة
    this.updateCrownPosition(crownGroup, country.cube);

    // إضافة التاج للمشهد
    this.scene.add(crownGroup);

    // حفظ مرجع التاج والضوء
    country.cube.userData.crown = crownGroup;
    country.cube.userData.crownLight = crownLight;

    // أنيميشن دوران للتاج
    this.animateCrownRotation(crownGroup);

    console.log(`👑 تم إضافة التاج اللامع لـ ${country.name}`);
  }

  /**
   * تحديث موضع التاج بدقة
   */
  updateCrownPosition(crownGroup, cube) {
    crownGroup.position.copy(cube.position);
    crownGroup.position.y = cube.position.y + 0.7; // أقرب للمكعب
  }

  /**
   * أنيميشن دوران التاج
   */
  animateCrownRotation(crownGroup) {
    const animate = () => {
      if (crownGroup.parent) { // التحقق من أن التاج لا يزال في المشهد
        crownGroup.rotation.y += 0.02; // دوران بطيء وجميل
        requestAnimationFrame(animate);
      }
    };
    animate();
  }

  /**
   * إزالة التاج من الدولة
   */
  removeCrown(country) {
    if (!country.cube || !country.cube.userData.crown) return;

    // إزالة التاج من المشهد
    this.scene.remove(country.cube.userData.crown);

    // تنظيف الذاكرة
    country.cube.userData.crown.traverse((child) => {
      if (child.geometry) child.geometry.dispose();
      if (child.material) child.material.dispose();
    });

    // إزالة المراجع
    country.cube.userData.crown = null;
    country.cube.userData.crownLight = null;

    console.log(`🗑️ تم إزالة التاج من ${country.name}`);
  }

  /**
   * تسليط الضوء على القائد
   */
  highlightLeader(leader) {
    // تم تعطيل الهالة الدائرية للقائد
    return;
  }

  /**
   * تحديث واجهة المستخدم
   */
  updateUI() {
    this.updateScoreboard();
  }

  /**
   * تحديث لوحة النتائج مع عرض تفاصيل أكثر
   */
  updateScoreboard() {
    if (!this.scoreboardContent) return;

    // العثور على عنصر لوحة النتائج الكامل
    const scoreboardElement = this.scoreboardContent.closest('.scoreboard');

    // التحقق من إعداد إظهار ترتيب الدول
    if (!this.gameSettings.showLeaderboard) {
      // إخفاء القائمة بالكامل
      if (scoreboardElement) {
        scoreboardElement.style.display = 'none';
      }
      return;
    }

    // إظهار القائمة إذا كانت مخفية
    if (scoreboardElement) {
      scoreboardElement.style.display = 'block';
    }

    // ترتيب الدول حسب القوة
    const sortedCountries = Object.entries(this.countries)
      .sort(([,a], [,b]) => b.power - a.power);

    let html = '';
    sortedCountries.forEach(([code, country], index) => {
      const isLeading = code === this.gameState.currentLeader;
      const className = isLeading ? 'country-score leading' : 'country-score';

      // عرض عداد الهدايا وآخر مرسل وعدد أعضاء الفريق
      const giftCount = country.giftCount || 0;
      const lastSender = country.lastSender || 'لا يوجد';
      const lastSenderDisplay = lastSender.length > 10 ? lastSender.substring(0, 10) + '...' : lastSender;
      const teamMemberCount = country.teamMemberCount || 0;

      html += `
        <div class="${className}">
          <div class="country-main-info">
            <span class="country-flag">${country.flag}</span>
            <span class="country-name">${country.customName || country.name}</span>
            <span class="country-power">${country.power}</span>
          </div>
          <div class="country-details">
            <small class="gift-count">🎁 ${giftCount}</small>
            <small class="last-sender">👤 ${lastSenderDisplay}</small>
            <small class="team-members">👥 ${teamMemberCount}</small>
          </div>
        </div>
      `;
    });

    this.scoreboardContent.innerHTML = html;
  }

  /**
   * تحديث حالة الاتصال
   */
  updateConnectionStatus(connected) {
    if (!this.connectionStatus) return;

    if (connected) {
      this.connectionStatus.textContent = 'متصل بـ TikTok Live ✅';
      this.connectionStatus.className = 'connection-status connected';
    } else {
      this.connectionStatus.textContent = 'غير متصل بـ TikTok Live ❌';
      this.connectionStatus.className = 'connection-status disconnected';
    }
  }

  /**
   * تشغيل الصوت
   */
  playSound(soundName) {
    // يمكن إضافة ملفات صوتية لاحقاً
    console.log(`🔊 تشغيل صوت: ${soundName}`);
  }

  /**
   * تبديل وضع التجربة
   */
  toggleDemoMode() {
    this.gameSettings.demoMode = !this.gameSettings.demoMode;

    if (this.gameSettings.demoMode) {
      this.startDemo();
      this.showAlert('🧪 تم تفعيل وضع التجربة');
    } else {
      this.stopDemo();
      this.showAlert('🎮 تم إيقاف وضع التجربة');
    }
  }

  /**
   * بدء وضع التجربة
   */
  startDemo() {
    if (this.demoInterval) return;

    this.demoInterval = setInterval(() => {
      // محاكاة هدية عشوائية
      const countries = Object.keys(this.countries);
      const randomCountry = countries[Math.floor(Math.random() * countries.length)];
      const gifts = [
        'Rose', 'Heart', 'TikTok', 'Finger Heart', 'Star', 'Butterfly',
        'Little Crown', 'Confetti', 'Kiss', 'Hearts', 'Golden Crown',
        'Birthday Cake', 'Forever Rosa', 'Money Gun', 'Diamond', 'Crown', 'Rocket'
      ];
      const randomGift = gifts[Math.floor(Math.random() * gifts.length)];

      const fakeGiftData = {
        giftName: randomGift,
        repeatCount: Math.floor(Math.random() * 5) + 1,
        uniqueId: `demo_user_${randomCountry}`,
        nickname: `مستخدم_تجريبي_${randomCountry}`
      };

      this.handleGiftReceived(fakeGiftData);
    }, this.gameSettings.demoSpeed * 1000);

    console.log('🧪 تم بدء وضع التجربة');
  }

  /**
   * إيقاف وضع التجربة
   */
  stopDemo() {
    if (this.demoInterval) {
      clearInterval(this.demoInterval);
      this.demoInterval = null;
    }

    console.log('🛑 تم إيقاف وضع التجربة');
  }

  /**
   * إعادة تشغيل اللعبة
   */
  reset() {
    console.log('🔄 بدء إعادة تشغيل اللعبة...');

    // إعادة تعيين قوة الدول وجميع البيانات المرتبطة
    Object.values(this.countries).forEach(country => {
      // إعادة تعيين القوة والعدادات
      country.power = 0;
      country.giftCount = 0;        // إعادة تعيين عداد الهدايا
      country.lastSender = null;    // إعادة تعيين آخر مرسل

      console.log(`🏳️ إعادة تعيين بيانات ${country.name}`);

      if (country.cube) {
        // إعادة حجم المكعب للحجم الأصلي
        country.cube.scale.set(1, 1, 1);

        // إزالة نصوص العداد وآخر مرسل
        if (country.cube.userData.giftCountText) {
          this.scene.remove(country.cube.userData.giftCountText);
          country.cube.userData.giftCountText = null;
          console.log(`🗑️ تم حذف نص العداد لـ ${country.name}`);
        }

        if (country.cube.userData.lastSenderText) {
          this.scene.remove(country.cube.userData.lastSenderText);
          country.cube.userData.lastSenderText = null;
          console.log(`🗑️ تم حذف نص آخر مرسل لـ ${country.name}`);
        }

        // إزالة التاج إن وجد
        if (country.cube.userData.crown) {
          this.removeCrown(country);
          console.log(`👑 تم إزالة التاج من ${country.name}`);
        }

        // إعادة المكعب للموضع الأصلي على الأرض
        if (country.position) {
          country.cube.position.set(country.position.x, country.position.y, country.position.z);
        }
      }
    });

    // إعادة تعيين حالة اللعبة
    this.gameState.currentLeader = null;
    this.gameState.totalGifts = 0;
    this.gameState.startTime = Date.now();

    // إعادة تعيين بيانات الفرق
    this.teamMembers = {};
    this.userTeams = {};
    this.teamJoinMessages = [];
    this.initTeamSystem();

    // إزالة حاوية رسائل الانضمام
    const teamMessagesContainer = document.getElementById('teamJoinMessages');
    if (teamMessagesContainer) {
      teamMessagesContainer.remove();
    }

    // مسح التأثيرات
    this.particles = [];
    this.explosions = [];

    // إزالة الهالة
    if (this.currentGlow) {
      this.scene.remove(this.currentGlow);
      this.currentGlow = null;
      console.log('✨ تم إزالة الهالة');
    }

    // تحديث واجهة المستخدم
    this.updateUI();

    this.showAlert('🔄 تم إعادة تشغيل اللعبة بنجاح');
    console.log('✅ تم إعادة تشغيل اللعبة بنجاح - جميع البيانات تم تنظيفها');
  }

  /**
   * معالجة تغيير حجم النافذة
   */
  onWindowResize() {
    if (!this.camera || !this.renderer) return;

    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * تنظيف الموارد
   */
  dispose() {
    this.gameState.isRunning = false;

    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }

    if (this.demoInterval) {
      clearInterval(this.demoInterval);
    }

    if (this.socket) {
      this.socket.disconnect();
    }

    if (this.renderer) {
      this.renderer.dispose();
    }

    console.log('🧹 تم تنظيف موارد اللعبة');
  }


}

// تصدير الكلاس للاستخدام العام
window.FlagClashGame = FlagClashGame;
