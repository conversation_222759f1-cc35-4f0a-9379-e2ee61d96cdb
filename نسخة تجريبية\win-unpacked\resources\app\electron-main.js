const { app, BrowserWindow, screen } = require('electron');
const path = require('path');
const { spawn } = require('child_process');

// متغيرات النوافذ
let mainWindow;
let serverProcess;

// إعدادات التطبيق
const isDev = process.argv.includes('--dev');
const serverPort = 3000;

/**
 * إنشاء النافذة الرئيسية
 */
function createMainWindow() {
  // الحصول على أبعاد الشاشة
  const { width, height } = screen.getPrimaryDisplay().workAreaSize;
  
  mainWindow = new BrowserWindow({
    width: Math.min(1400, width - 100),
    height: Math.min(900, height - 100),
    minWidth: 1200,
    minHeight: 700,
    center: true,
    show: false, // لا تظهر حتى تكون جاهزة
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true,
      preload: path.join(__dirname, 'electron-preload.js')
    },
    titleBarStyle: 'default',
    autoHideMenuBar: !isDev, // إخفاء شريط القوائم في الإنتاج
    backgroundColor: '#f8f9fa' // لون خلفية لمنع الوميض
  });

  // تحميل التطبيق
  mainWindow.loadURL(`http://localhost:${serverPort}`);

  // إظهار النافذة عند الجاهزية
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // فتح أدوات المطور في وضع التطوير
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // معالجة إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // منع التنقل خارج التطبيق
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    if (parsedUrl.origin !== `http://localhost:${serverPort}`) {
      event.preventDefault();
    }
  });

  // معالجة الروابط الخارجية
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    require('electron').shell.openExternal(url);
    return { action: 'deny' };
  });
}

/**
 * بدء خادم Express
 */
function startServer() {
  return new Promise((resolve, reject) => {
    // تحديد مسار Node.js والمجلد
    let nodePath = 'node';
    let workingDir = __dirname;

    if (app.isPackaged) {
      // في التطبيق المبني، استخدم نفس الطريقة
      const electronDir = path.dirname(process.execPath);

      // جرب node.exe المنسوخ أولاً
      const nodeExePath = path.join(electronDir, 'node.exe');
      if (require('fs').existsSync(nodeExePath)) {
        nodePath = nodeExePath;
      }

      // تحديد مجلد العمل الصحيح
      workingDir = path.join(electronDir, 'resources', 'app');

      console.log('Working directory:', workingDir);
      console.log('Node path:', nodePath);
    }

    // بدء الخادم
    serverProcess = spawn(nodePath, ['index.js'], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: workingDir,
      env: {
        ...process.env,
        NODE_ENV: 'production',
        ELECTRON_IS_PACKAGED: app.isPackaged ? 'true' : 'false'
      }
    });

    let serverReady = false;

    // مراقبة مخرجات الخادم
    serverProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log('Server:', output);
      
      // التحقق من جاهزية الخادم
      if (output.includes('Server running') || output.includes(`${serverPort}`)) {
        if (!serverReady) {
          serverReady = true;
          resolve();
        }
      }
    });

    // مراقبة أخطاء الخادم
    serverProcess.stderr.on('data', (data) => {
      console.error('Server Error:', data.toString());
    });

    // معالجة إغلاق الخادم
    serverProcess.on('close', (code) => {
      console.log(`Server process exited with code ${code}`);
      if (!serverReady) {
        reject(new Error(`Server failed to start (exit code: ${code})`));
      }
    });

    // معالجة أخطاء بدء الخادم
    serverProcess.on('error', (error) => {
      console.error('Failed to start server:', error);
      reject(error);
    });

    // timeout للأمان
    setTimeout(() => {
      if (!serverReady) {
        reject(new Error('Server startup timeout'));
      }
    }, 10000); // 10 ثوان
  });
}

/**
 * بدء الخادم داخل نفس العملية (حل بديل)
 */
function startInternalServer(resolve, reject) {
  try {
    // تعيين المسار الصحيح للتطبيق
    const originalCwd = process.cwd();
    let appPath;

    if (app.isPackaged) {
      // في التطبيق المبني، الملفات في app.asar
      appPath = path.dirname(process.execPath);
      console.log('App path:', appPath);
      console.log('Resources path:', process.resourcesPath);
      console.log('Current directory:', process.cwd());
    } else {
      appPath = __dirname;
    }

    // لا نغير مجلد العمل في التطبيق المبني
    if (!app.isPackaged) {
      process.chdir(appPath);
    }

    // تعيين متغيرات البيئة المطلوبة
    process.env.NODE_ENV = process.env.NODE_ENV || 'production';
    process.env.ELECTRON_IS_PACKAGED = app.isPackaged ? 'true' : 'false';

    // تنظيف cache للوحدات
    Object.keys(require.cache).forEach(key => {
      if (key.includes('index.js') || key.includes('express') || key.includes('socket.io')) {
        delete require.cache[key];
      }
    });

    // تحميل وتشغيل الخادم
    const serverModule = require('./index.js');

    // انتظار قصير للتأكد من بدء الخادم
    setTimeout(() => {
      console.log('Internal server started successfully');
      resolve();
    }, 3000);

  } catch (error) {
    console.error('Failed to start internal server:', error);
    reject(error);
  }
}

/**
 * التحقق من جاهزية الخادم
 */
async function waitForServer() {
  const http = require('http');

  return new Promise((resolve) => {
    function checkServer() {
      const req = http.get(`http://localhost:${serverPort}`, (res) => {
        resolve();
      });

      req.on('error', () => {
        setTimeout(checkServer, 500);
      });
    }

    checkServer();
  });
}

/**
 * إيقاف الخادم
 */
function stopServer() {
  if (serverProcess) {
    serverProcess.kill();
    serverProcess = null;
  }
}

/**
 * تهيئة التطبيق
 */
async function initializeApp() {
  try {
    console.log('Starting StreamTok...');
    
    // بدء الخادم أولاً
    await startServer();
    console.log('Server started successfully');

    // التحقق من جاهزية الخادم
    await waitForServer();
    
    // إنشاء النافذة الرئيسية
    createMainWindow();
    
    console.log('Application initialized successfully');
    
  } catch (error) {
    console.error('Failed to initialize application:', error);
    
    // إظهار رسالة خطأ للمستخدم
    const { dialog } = require('electron');
    dialog.showErrorBox(
      'خطأ في بدء التطبيق',
      `فشل في تشغيل التطبيق: ${error.message}\n\nتأكد من أن المنفذ ${serverPort} غير مستخدم.`
    );
    
    app.quit();
  }
}

/**
 * أحداث التطبيق
 */

// عند جاهزية Electron
app.whenReady().then(initializeApp);

// عند إغلاق جميع النوافذ
app.on('window-all-closed', () => {
  stopServer();
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// عند تفعيل التطبيق (macOS)
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    initializeApp();
  }
});

// عند إنهاء التطبيق
app.on('before-quit', () => {
  stopServer();
});

// معالجة الأخطاء غير المتوقعة
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

module.exports = { mainWindow };
