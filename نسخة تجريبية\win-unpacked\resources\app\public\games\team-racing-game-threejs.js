// Team Racing Game with Three.js + WebGL
class TeamRacingGameThreeJS {
    constructor() {
        // Game state
        this.teams = [];
        this.supporters = new Map();
        this.particles = [];
        this.isConnected = false;
        this.demoMode = false;
        this.demoInterval = null;

        // Enhanced game settings
        this.gameSettings = {
            raceDistance: 500,
            animationSpeed: 5,
            autoReset: true,
            autoResetDelay: 10,
            showEffects: true,
            showSupporterNames: true,
            fontSize: 16,
            teamSize: 60,
            globalTeamSize: 1.0,
            defaultTeamType: 'ball',
            enableTeamAnimations: true,
            enableJoinGifts: true,
            enableProgressGifts: true,
            progressMultiplier: 1.0,
            enableWinSounds: true,
            enableWinEffects: true,
            winDisplayDuration: 5,
            backgroundOpacity: 80,
            cameraMode: 'follow', // follow, fixed, overview
            graphicsQuality: 'high', // low, medium, high, ultra
            autoOptimizeGifs: true,
            trackAngle: 15,
            trackDepth: 0.6
        };

        // Three.js components
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.gameWidth = window.innerWidth;
        this.gameHeight = window.innerHeight;

        // Performance tracking
        this.animationFrame = null;
        this.lastTime = 0;
        this.deltaTime = 0;
        this.targetFPS = 60;
        this.frameInterval = 1000 / this.targetFPS;
        this.frameCount = 0;
        this.fpsCounter = 0;
        this.lastFPSUpdate = 0;
        this.currentFPS = 0;
        this.enableVSync = true;
        this.enablePerformanceMode = false;

        // Team objects in 3D space
        this.teamMeshes = [];
        this.trackMesh = null;
        this.backgroundMesh = null;

        // Default teams with enhanced properties
        this.defaultTeams = [
            {
                name: 'الفريق الأحمر',
                color: '#ff4757',
                joinGift: '🔴',
                progress: 0,
                supporters: [],
                displayType: 'ball', // ball, image, gif
                customImage: null,
                size: 1.0,
                animations: true
            },
            {
                name: 'الفريق الأزرق',
                color: '#3742fa',
                joinGift: '🔵',
                progress: 0,
                supporters: [],
                displayType: 'ball',
                customImage: null,
                size: 1.0,
                animations: true
            },
            {
                name: 'الفريق الأخضر',
                color: '#2ed573',
                joinGift: '🟢',
                progress: 0,
                supporters: [],
                displayType: 'ball',
                customImage: null,
                size: 1.0,
                animations: true
            },
            {
                name: 'الفريق الأصفر',
                color: '#ffa502',
                joinGift: '🟡',
                progress: 0,
                supporters: [],
                displayType: 'ball',
                customImage: null,
                size: 1.0,
                animations: true
            }
        ];

        // Progress gifts
        this.progressGifts = {
            '🎁': 10, '💎': 25, '🚀': 50, '👑': 100,
            '⭐': 5, '💝': 15, '🎉': 20, '🏆': 75
        };

        // Initialize
        this.init();
    }

    // Initialize Three.js scene
    init() {
        this.setupThreeJS();
        this.setupScene();
        this.setupLighting();
        this.createTrack();
        this.createBackground();
        
        // Load settings
        this.loadSettings();
        
        // Initialize teams
        if (this.teams.length === 0) {
            this.teams = JSON.parse(JSON.stringify(this.defaultTeams));
        }
        
        this.createTeamMeshes();
        
        // Setup UI
        this.setupUI();
        this.updateStatsPanel();
        
        // Connect socket
        this.connectSocket();
        
        // Check URL parameters
        this.checkURLParams();
        
        // Start game loop
        this.startGameLoop();
        
        console.log('🚀 Three.js Racing Game initialized with WebGL acceleration!');
    }

    setupThreeJS() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x1a1a2e);

        // Create camera with perspective
        this.camera = new THREE.PerspectiveCamera(
            75, // FOV
            this.gameWidth / this.gameHeight, // Aspect ratio
            0.1, // Near plane
            1000 // Far plane
        );
        
        // Position camera for racing view
        this.camera.position.set(0, 5, 10);
        this.camera.lookAt(0, 0, 0);

        // Create WebGL renderer
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: false,
            powerPreference: "high-performance"
        });
        this.renderer.setSize(this.gameWidth, this.gameHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // Add renderer to DOM
        const gameContainer = document.getElementById('gameContainer');
        if (!gameContainer) {
            console.error('❌ Game container not found!');
            return;
        }

        // Clear container completely
        gameContainer.innerHTML = '';

        // Set canvas ID
        this.renderer.domElement.id = 'gameCanvas';

        // Add canvas to container
        gameContainer.appendChild(this.renderer.domElement);

        // Add images overlay
        const overlay = document.createElement('div');
        overlay.id = 'imagesOverlay';
        overlay.className = 'images-overlay';
        gameContainer.appendChild(overlay);

        console.log('✅ Canvas added to DOM:', this.renderer.domElement);

        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
    }

    setupScene() {
        // Add fog for depth
        this.scene.fog = new THREE.Fog(0x1a1a2e, 10, 50);
    }

    setupLighting() {
        // Enhanced ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.8);
        this.scene.add(ambientLight);

        // Main directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(0, 15, 10);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.1;
        directionalLight.shadow.camera.far = 50;
        directionalLight.shadow.camera.left = -15;
        directionalLight.shadow.camera.right = 15;
        directionalLight.shadow.camera.top = 15;
        directionalLight.shadow.camera.bottom = -15;
        this.scene.add(directionalLight);

        // Track lighting - multiple point lights along the track
        for (let i = 0; i < 5; i++) {
            const trackLight = new THREE.PointLight(0x3498db, 0.3, 15);
            trackLight.position.set(-8 + (i * 4), 4, 0);
            this.scene.add(trackLight);
        }

        // Finish line spotlight
        const finishSpotlight = new THREE.SpotLight(0xffd700, 0.8, 20, Math.PI / 6);
        finishSpotlight.position.set(9, 8, 0);
        finishSpotlight.target.position.set(9, 0, 0);
        this.scene.add(finishSpotlight);
        this.scene.add(finishSpotlight.target);
    }

    createTrack() {
        // Create beautiful racing track with gradient texture
        const trackGeometry = new THREE.PlaneGeometry(20, 8);

        // Create gradient texture for track
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        // Create realistic asphalt track
        const gradient = ctx.createLinearGradient(0, 0, 0, 256);
        gradient.addColorStop(0, '#2a2a2a');
        gradient.addColorStop(0.5, '#1a1a1a');
        gradient.addColorStop(1, '#0f0f0f');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 512, 256);

        // Add asphalt texture
        ctx.fillStyle = '#333333';
        for (let i = 0; i < 1000; i++) {
            const x = Math.random() * 512;
            const y = Math.random() * 256;
            const size = Math.random() * 2;
            ctx.fillRect(x, y, size, size);
        }

        // Add subtle road markings
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.lineWidth = 1;
        for (let i = 0; i < 512; i += 50) {
            ctx.beginPath();
            ctx.moveTo(i, 0);
            ctx.lineTo(i, 256);
            ctx.stroke();
        }

        const trackTexture = new THREE.CanvasTexture(canvas);
        trackTexture.wrapS = THREE.RepeatWrapping;
        trackTexture.wrapT = THREE.RepeatWrapping;
        trackTexture.repeat.set(2, 1);

        const trackMaterial = new THREE.MeshPhongMaterial({
            map: trackTexture,
            transparent: true,
            opacity: 0.95,
            shininess: 20
        });

        this.trackMesh = new THREE.Mesh(trackGeometry, trackMaterial);
        this.trackMesh.rotation.x = -Math.PI / 2; // Lay flat
        this.trackMesh.receiveShadow = true;
        this.scene.add(this.trackMesh);

        // Track lanes (lines)
        this.createTrackLanes();

        // Start and finish lines
        this.createStartFinishLines();
    }

    createTrackLanes() {
        const laneCount = this.teams.length; // Use actual team count
        const trackWidth = 8;
        const laneSpacing = trackWidth / laneCount;

        for (let i = 1; i < laneCount; i++) {
            // Main lane line
            const laneGeometry = new THREE.PlaneGeometry(20, 0.08);
            const laneMaterial = new THREE.MeshBasicMaterial({
                color: new THREE.Color().setHSL(i / laneCount, 0.8, 0.8),
                transparent: true,
                opacity: 0.7
            });

            const laneMesh = new THREE.Mesh(laneGeometry, laneMaterial);
            laneMesh.rotation.x = -Math.PI / 2;
            laneMesh.position.y = 0.01;
            laneMesh.position.z = -trackWidth/2 + (i * laneSpacing);
            this.scene.add(laneMesh);

            // Lane glow effect
            const glowGeometry = new THREE.PlaneGeometry(20, 0.4);
            const glowMaterial = new THREE.MeshBasicMaterial({
                color: new THREE.Color().setHSL(i / laneCount, 0.6, 0.4),
                transparent: true,
                opacity: 0.15
            });

            const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
            glowMesh.rotation.x = -Math.PI / 2;
            glowMesh.position.y = 0.005;
            glowMesh.position.z = -trackWidth/2 + (i * laneSpacing);
            this.scene.add(glowMesh);
        }
    }

    createStartFinishLines() {
        // Enhanced Start line (green with glow)
        const startGeometry = new THREE.PlaneGeometry(0.3, 8);
        const startMaterial = new THREE.MeshBasicMaterial({
            color: 0x00ff88,
            transparent: true,
            opacity: 0.9
        });
        const startLine = new THREE.Mesh(startGeometry, startMaterial);
        startLine.rotation.x = -Math.PI / 2;
        startLine.position.y = 0.02;
        startLine.position.x = -9;
        this.scene.add(startLine);

        // Start line glow
        const startGlowGeometry = new THREE.PlaneGeometry(0.8, 8.5);
        const startGlowMaterial = new THREE.MeshBasicMaterial({
            color: 0x00ff88,
            transparent: true,
            opacity: 0.2
        });
        const startGlow = new THREE.Mesh(startGlowGeometry, startGlowMaterial);
        startGlow.rotation.x = -Math.PI / 2;
        startGlow.position.y = 0.01;
        startGlow.position.x = -9;
        this.scene.add(startGlow);

        // Enhanced Finish line (golden with animation)
        const finishGeometry = new THREE.PlaneGeometry(0.3, 8);
        const finishMaterial = new THREE.MeshBasicMaterial({
            color: 0xffd700,
            transparent: true,
            opacity: 0.95
        });
        const finishLine = new THREE.Mesh(finishGeometry, finishMaterial);
        finishLine.rotation.x = -Math.PI / 2;
        finishLine.position.y = 0.02;
        finishLine.position.x = 9;
        finishLine.userData.animate = true;
        this.scene.add(finishLine);
        this.finishLine = finishLine;

        // Finish line glow
        const finishGlowGeometry = new THREE.PlaneGeometry(0.8, 8.5);
        const finishGlowMaterial = new THREE.MeshBasicMaterial({
            color: 0xffd700,
            transparent: true,
            opacity: 0.3
        });
        const finishGlow = new THREE.Mesh(finishGlowGeometry, finishGlowMaterial);
        finishGlow.rotation.x = -Math.PI / 2;
        finishGlow.position.y = 0.01;
        finishGlow.position.x = 9;
        finishGlow.userData.animate = true;
        this.scene.add(finishGlow);
        this.finishGlow = finishGlow;
    }

    createBackground() {
        // No stars - clean background
        this.createNebula();
    }

    // Star layer function removed - no more stars

    createNebula() {
        // Create nebula-like background effect
        const nebulaGeometry = new THREE.SphereGeometry(80, 32, 32);
        const nebulaMaterial = new THREE.MeshBasicMaterial({
            color: 0x1a1a3a,
            transparent: true,
            opacity: 0.1,
            side: THREE.BackSide
        });

        const nebula = new THREE.Mesh(nebulaGeometry, nebulaMaterial);
        this.scene.add(nebula);
    }

    createTeamMeshes() {
        // Clear existing meshes
        this.teamMeshes.forEach(mesh => {
            this.scene.remove(mesh);
        });
        this.teamMeshes = [];

        this.teams.forEach((team, index) => {
            const teamMesh = this.createTeamMesh(team, index);
            this.teamMeshes.push(teamMesh);
            this.scene.add(teamMesh);
        });

        // Initialize HTML overlay after creating meshes
        setTimeout(() => this.updateHTMLOverlay(), 100);
    }

    createTeamMesh(team, index) {
        // Create team group to hold both geometry and custom image
        const teamGroup = new THREE.Group();

        // Determine display type
        const displayType = team.displayType || this.gameSettings.defaultTeamType || 'ball';
        const teamSize = (team.size || 1.0) * this.gameSettings.globalTeamSize;

        // Create base geometry based on display type
        let geometry, material, mesh;

        switch (displayType) {
            case 'ball':
                geometry = new THREE.SphereGeometry(0.4 * teamSize, 20, 20);
                material = new THREE.MeshPhongMaterial({
                    color: team.color,
                    shininess: 100,
                    transparent: true,
                    opacity: team.customImage ? 0.4 : 0.95,
                    emissive: new THREE.Color(team.color).multiplyScalar(0.1)
                });
                break;

            case 'cube':
                geometry = new THREE.BoxGeometry(0.6 * teamSize, 0.6 * teamSize, 0.6 * teamSize);
                material = new THREE.MeshPhongMaterial({
                    color: team.color,
                    shininess: 80,
                    transparent: true,
                    opacity: team.customImage ? 0.4 : 0.95,
                    emissive: new THREE.Color(team.color).multiplyScalar(0.1)
                });
                break;

            case 'cylinder':
                geometry = new THREE.CylinderGeometry(0.3 * teamSize, 0.3 * teamSize, 0.6 * teamSize, 16);
                material = new THREE.MeshPhongMaterial({
                    color: team.color,
                    shininess: 90,
                    transparent: true,
                    opacity: team.customImage ? 0.4 : 0.95,
                    emissive: new THREE.Color(team.color).multiplyScalar(0.1)
                });
                break;

            default: // ball
                geometry = new THREE.SphereGeometry(0.4 * teamSize, 20, 20);
                material = new THREE.MeshPhongMaterial({
                    color: team.color,
                    shininess: 100,
                    transparent: true,
                    opacity: team.customImage ? 0.4 : 0.95,
                    emissive: new THREE.Color(team.color).multiplyScalar(0.1)
                });
        }

        mesh = new THREE.Mesh(geometry, material);
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        teamGroup.add(mesh);

        // Add custom image if available
        if (team.customImage) {
            this.addCustomImageToTeam(teamGroup, team, teamSize);
        }

        // ALWAYS ensure base mesh is visible for teams without custom images
        if (!team.customImage || (team.displayType !== 'image' && team.displayType !== 'webm')) {
            mesh.visible = true;
            mesh.material.opacity = 0.95;
        }

        // Position in lane - dynamic spacing based on team count
        const trackWidth = 8;
        const laneSpacing = trackWidth / this.teams.length;
        const laneZ = -trackWidth/2 + (index * laneSpacing) + (laneSpacing/2);
        const teamHeight = team.height || 0.3;
        teamGroup.position.set(-8, teamHeight, laneZ);

        // Store team reference
        teamGroup.userData = { team, index, baseMesh: mesh, displayType };

        return teamGroup;
    }

    addCustomImageToTeam(teamGroup, team, teamSize = 1.0) {
        // Handle different image sources
        let imageSrc = null;

        if (team.customImage) {
            if (typeof team.customImage === 'string') {
                imageSrc = team.customImage;
            } else if (team.customImage.src) {
                imageSrc = team.customImage.src;
            } else if (team.customImage.element && team.customImage.element.src) {
                imageSrc = team.customImage.element.src;
            }
        }

        if (!imageSrc) {
            console.warn('No valid image source found for team:', team.name);
            return;
        }

        // Simple approach: treat all images the same way
        const loader = new THREE.TextureLoader();

        loader.load(imageSrc, (texture) => {
            texture.magFilter = THREE.LinearFilter;
            texture.minFilter = THREE.LinearFilter;

            // Create plane geometry for the image
            const imageSize = 1.0 * teamSize;
            const imageGeometry = new THREE.PlaneGeometry(imageSize, imageSize);
            const imageMaterial = new THREE.MeshBasicMaterial({
                map: texture,
                transparent: true,
                alphaTest: 0.1,
                side: THREE.DoubleSide
            });

            const imageMesh = new THREE.Mesh(imageGeometry, imageMaterial);

            // Position based on display type
            const displayType = team.displayType || 'ball';
            switch (displayType) {
                case 'image':
                case 'gif':
                    imageMesh.position.set(0, 0, 0);
                    if (teamGroup.userData.baseMesh) {
                        teamGroup.userData.baseMesh.visible = false;
                    }
                    break;
                default:
                    imageMesh.position.set(0, 0, 0.5 * teamSize);
            }

            // Make image always face camera
            imageMesh.lookAt(this.camera.position);

            // Mark as GIF if it's a GIF file
            const isGif = imageSrc.toLowerCase().includes('.gif') || imageSrc.includes('data:image/gif');
            if (isGif) {
                imageMesh.userData.isGif = true;
            }

            teamGroup.add(imageMesh);
            teamGroup.userData.imageMesh = imageMesh;

            console.log('✅ Image loaded for team:', team.name, isGif ? '(GIF)' : '(Static)');
        }, undefined, (error) => {
            console.error('❌ Error loading image:', error);
        });
    }



    setupGifAnimation(imageMesh, gifImage) {
        // For GIF animation in WebGL, we create a video element
        const video = document.createElement('video');
        video.src = gifImage.src;
        video.autoplay = true;
        video.loop = true;
        video.muted = true;
        video.playsInline = true;
        video.style.display = 'none';
        document.body.appendChild(video);

        video.onloadeddata = () => {
            // Create video texture
            const videoTexture = new THREE.VideoTexture(video);
            videoTexture.minFilter = THREE.LinearFilter;
            videoTexture.magFilter = THREE.LinearFilter;

            // Update material
            imageMesh.material.map = videoTexture;
            imageMesh.material.needsUpdate = true;

            console.log('✅ GIF converted to video texture for WebGL');
        };

        video.onerror = () => {
            console.warn('❌ Failed to convert GIF to video texture');
        };
    }

    updateTeamCustomImages() {
        // Update team meshes when custom images change
        this.teamMeshes.forEach((teamGroup, index) => {
            const team = this.teams[index];

            // Remove existing image mesh if any
            if (teamGroup.userData.imageMesh) {
                teamGroup.remove(teamGroup.userData.imageMesh);
                teamGroup.userData.imageMesh.material.dispose();
                teamGroup.userData.imageMesh.geometry.dispose();
                teamGroup.userData.imageMesh = null;
            }

            if (team.customImage) {
                // For WebM and images, keep base mesh visible with transparency
                if (team.displayType === 'image' || team.displayType === 'webm') {
                    teamGroup.userData.baseMesh.visible = true;
                    teamGroup.userData.baseMesh.material.opacity = 0.7;
                } else {
                    // For other types, add to 3D scene
                    this.addCustomImageToTeam(teamGroup, team, team.size || 1.0);
                    teamGroup.userData.baseMesh.material.opacity = 0.3;
                    teamGroup.userData.baseMesh.visible = true;
                }
            } else {
                // ALWAYS show base mesh if no custom image
                teamGroup.userData.baseMesh.material.opacity = 1.0;
                teamGroup.userData.baseMesh.visible = true;
            }
        });

        // Update HTML overlay
        this.updateHTMLOverlay();
    }

    updateHTMLOverlay() {
        const overlay = document.getElementById('imagesOverlay');
        if (!overlay) return;

        // Clear existing elements
        overlay.innerHTML = '';

        // Add HTML elements for teams with images/videos
        this.teams.forEach((team, index) => {
            if (team.customImage && (team.displayType === 'image' || team.displayType === 'webm')) {
                let element;

                if (team.displayType === 'webm') {
                    // Create video element for WebM
                    element = document.createElement('video');
                    element.src = team.customImage.src;
                    element.autoplay = true;
                    element.loop = true;
                    element.muted = true;
                    element.playsInline = true;
                    element.className = 'team-video';

                    // Add error handling
                    element.onerror = () => {
                        console.error('❌ Error loading WebM video for team:', team.name);
                    };

                    element.onloadeddata = () => {
                        console.log('✅ WebM video loaded for team:', team.name);
                    };
                } else {
                    // Create img element for static images
                    element = document.createElement('img');
                    element.src = team.customImage.src;
                    element.className = 'team-image';

                    element.onerror = () => {
                        console.error('❌ Error loading image for team:', team.name);
                    };

                    element.onload = () => {
                        console.log('✅ Image loaded for team:', team.name);
                    };
                }

                element.style.borderColor = team.color;
                element.dataset.teamIndex = index;
                overlay.appendChild(element);
            }
        });

        console.log('✅ HTML overlay updated');
    }

    updateHTMLPositions() {
        const overlay = document.getElementById('imagesOverlay');
        if (!overlay) return;

        // Get canvas dimensions and position
        const canvas = this.renderer.domElement;
        const canvasRect = canvas.getBoundingClientRect();

        // Update positions for each team element
        this.teamMeshes.forEach((mesh, index) => {
            const team = this.teams[index];

            if (team.customImage && (team.displayType === 'image' || team.displayType === 'webm')) {
                const element = overlay.querySelector(`[data-team-index="${index}"]`);
                if (element) {
                    // Get 3D world position
                    const worldPosition = mesh.position.clone();

                    // Convert to screen coordinates
                    worldPosition.project(this.camera);

                    // Convert to pixel coordinates
                    const x = (worldPosition.x * 0.5 + 0.5) * canvasRect.width;
                    const y = (worldPosition.y * -0.5 + 0.5) * canvasRect.height;

                    // Apply transform with GPU acceleration
                    element.style.transform = `translate3d(${x - 30}px, ${y - 30}px, 0)`;
                }
            }
        });
    }

    updateTeamSizes() {
        if (!this.teamMeshes) return;

        this.teamMeshes.forEach((teamGroup, index) => {
            const team = this.teams[index];
            const globalSize = this.gameSettings.globalTeamSize || 1.0;
            const teamSize = (team.size || 1.0) * globalSize;

            // Update base mesh scale
            if (teamGroup.userData.baseMesh) {
                teamGroup.userData.baseMesh.scale.setScalar(teamSize);
            }

            // Update custom image size if exists
            if (teamGroup.userData.imageMesh) {
                const imageSize = 1.2 * teamSize;
                teamGroup.userData.imageMesh.scale.set(imageSize, imageSize, 1);
            }
        });
    }

    updateTeamHeights() {
        if (!this.teamMeshes) return;

        this.teamMeshes.forEach((teamGroup, index) => {
            const team = this.teams[index];
            const teamHeight = team.height || 0.3;

            // Update team group position Y
            teamGroup.position.y = teamHeight;
        });
    }

    updateCameraMode(mode) {
        this.gameSettings.cameraMode = mode;
        this.saveSettings();

        switch (mode) {
            case 'follow':
                // Camera follows leading team (default behavior)
                break;
            case 'fixed':
                this.camera.position.set(0, 5, 10);
                this.camera.lookAt(0, 0, 0);
                break;
            case 'overview':
                this.camera.position.set(0, 10, 15);
                this.camera.lookAt(0, 0, 0);
                break;
        }
    }

    // Game loop with Three.js
    startGameLoop() {
        const gameLoop = (currentTime) => {
            // Calculate delta time
            this.deltaTime = currentTime - this.lastTime;

            // Frame rate limiting for consistent 60fps
            if (this.enableVSync && this.deltaTime < this.frameInterval) {
                this.animationFrame = requestAnimationFrame(gameLoop);
                return;
            }

            this.lastTime = currentTime;
            this.frameCount++;

            // Update FPS counter
            this.updateFPSCounter(currentTime);

            // Update game logic
            this.update(this.deltaTime);

            // Render Three.js scene
            this.render();

            // Continue loop
            this.animationFrame = requestAnimationFrame(gameLoop);
        };

        // Initialize timing
        this.lastTime = performance.now();
        this.lastFPSUpdate = this.lastTime;

        // Start the loop
        this.animationFrame = requestAnimationFrame(gameLoop);

        console.log(`🎮 Three.js game loop started - Target: ${this.targetFPS} FPS`);
    }

    updateFPSCounter(currentTime) {
        this.fpsCounter++;

        // Update FPS display every second
        if (currentTime - this.lastFPSUpdate >= 1000) {
            this.currentFPS = this.fpsCounter;
            this.fpsCounter = 0;
            this.lastFPSUpdate = currentTime;

            // Log FPS occasionally for debugging
            if (this.frameCount % 300 === 0) { // Every 5 seconds at 60fps
                console.log(`📊 Current FPS: ${this.currentFPS} (WebGL)`);
            }
        }
    }

    update(deltaTime) {
        // Normalize delta time for consistent animation speed
        const normalizedDelta = Math.min(deltaTime / 16.67, 2);

        // Update team positions
        this.updateTeamPositions(normalizedDelta);

        // Update particles
        this.updateParticles(normalizedDelta);

        // Update animations
        this.updateAnimations(normalizedDelta);

        // Performance monitoring
        if (this.frameCount % 180 === 0) { // Every 3 seconds
            this.checkPerformance();
        }
    }

    updateTeamPositions(normalizedDelta) {
        let leadingPosition = -8;

        this.teamMeshes.forEach((mesh, index) => {
            const team = mesh.userData.team;
            const progressRatio = Math.min(team.progress / this.gameSettings.raceDistance, 1);

            // Calculate target position
            const targetX = -8 + (16 * progressRatio);

            // Smooth movement
            mesh.position.x = THREE.MathUtils.lerp(mesh.position.x, targetX, 0.1 * normalizedDelta);

            // Track leading position for camera
            if (mesh.position.x > leadingPosition) {
                leadingPosition = mesh.position.x;
            }

            // Use team's custom height setting
            const teamHeight = team.height || 0.3;
            mesh.position.y = teamHeight;

            // Normal rotation for movement effect
            if (team.supporters.length > 0) {
                mesh.rotation.y += 0.03 * normalizedDelta;
            }

            // Update custom image orientation to face camera
            if (mesh.userData.imageMesh) {
                mesh.userData.imageMesh.lookAt(this.camera.position);
            }
        });

        // Update HTML overlay positions (throttled for performance)
        if (this.frameCount % 2 === 0) {
            this.updateHTMLPositions();
        }

        // Update camera to follow the action
        this.updateCamera(leadingPosition);
    }

    updateCamera(leadingPosition) {
        // Smooth camera follow
        const targetCameraX = Math.max(0, leadingPosition - 5);
        this.camera.position.x = THREE.MathUtils.lerp(this.camera.position.x, targetCameraX, 0.02);

        // Keep camera looking at the track center
        this.camera.lookAt(targetCameraX, 0, 0);
    }

    updateParticles(normalizedDelta) {
        // Update Three.js particle system here
        // Will implement later
    }

    updateAnimations(normalizedDelta) {
        // Update any ongoing animations
        // Remove star rotation to stop the spinning effect

        // Animate finish line glow
        if (this.finishLine && this.finishLine.userData.animate) {
            const time = performance.now() * 0.003;
            this.finishLine.material.opacity = 0.8 + Math.sin(time) * 0.2;

            if (this.finishGlow) {
                this.finishGlow.material.opacity = 0.2 + Math.sin(time * 1.5) * 0.1;
            }
        }

        // Update custom image animations (GIFs and rotations)
        this.updateImageAnimations(normalizedDelta);
    }

    updateImageAnimations(normalizedDelta) {
        if (!this.gameSettings.enableTeamAnimations) return;

        this.teamMeshes.forEach(teamGroup => {
            const imageMesh = teamGroup.userData.imageMesh;
            const team = teamGroup.userData.team;

            if (imageMesh && team) {
                // Simple rotation for GIF files
                if (imageMesh.userData.isGif) {
                    imageMesh.rotation.z += 0.02 * normalizedDelta;
                }

                // Subtle floating animation for all custom images
                if (team.displayType === 'image' || team.displayType === 'gif') {
                    const time = performance.now() * 0.001;
                    const floatOffset = Math.sin(time + teamGroup.userData.index) * 0.02;
                    imageMesh.position.y = floatOffset;
                }

                // Make sure image always faces camera
                imageMesh.lookAt(this.camera.position);
            }
        });
    }

    render() {
        // Render the Three.js scene
        this.renderer.render(this.scene, this.camera);

        // Draw UI overlay (Canvas 2D on top)
        this.drawUI();
    }

    drawUI() {
        // Create or get UI canvas overlay
        if (!this.uiCanvas) {
            this.createUIOverlay();
        }

        const ctx = this.uiCtx;

        // Clear UI canvas
        ctx.clearRect(0, 0, this.gameWidth, this.gameHeight);

        // Draw connection status
        const statusText = this.isConnected ? '🟢 متصل' : '🔴 غير متصل';
        const statusColor = this.isConnected ? '#00ff00' : '#ff0000';

        ctx.fillStyle = statusColor;
        ctx.font = `${this.gameSettings.fontSize}px Tajawal`;
        ctx.textAlign = 'left';
        ctx.fillText(statusText, 20, 30);

        // Draw FPS counter (if enabled)
        if (this.gameSettings.showFPS !== false) {
            const fpsColor = this.currentFPS >= 55 ? '#00ff00' : this.currentFPS >= 30 ? '#ffa500' : '#ff0000';
            ctx.fillStyle = fpsColor;
            ctx.fillText(`📊 ${this.currentFPS} FPS (WebGL)`, 20, 55);
        }

        if (this.demoMode) {
            ctx.fillStyle = '#ffa500';
            ctx.fillText('🧪 وضع التجربة', 20, 80);
        }

        // Performance mode indicator
        if (this.enablePerformanceMode) {
            ctx.fillStyle = '#ff6b6b';
            ctx.fillText('⚡ وضع الأداء', 20, this.demoMode ? 105 : 80);
        }

        // WebGL indicator
        ctx.fillStyle = '#00ffff';
        ctx.fillText('🚀 WebGL مفعل', 20, this.gameHeight - 30);

        // Draw instructions if no active supporters
        const hasActiveSupporters = this.teams.some(team => team.supporters.length > 0);
        if (!hasActiveSupporters && !this.demoMode) {
            this.drawInstructions();
        }
    }

    createUIOverlay() {
        // Create UI canvas overlay
        this.uiCanvas = document.createElement('canvas');
        this.uiCanvas.width = this.gameWidth;
        this.uiCanvas.height = this.gameHeight;
        this.uiCanvas.style.position = 'absolute';
        this.uiCanvas.style.top = '0';
        this.uiCanvas.style.left = '0';
        this.uiCanvas.style.pointerEvents = 'none';
        this.uiCanvas.style.zIndex = '10';

        this.uiCtx = this.uiCanvas.getContext('2d');

        // Add to game container
        const gameContainer = document.getElementById('gameContainer');
        gameContainer.appendChild(this.uiCanvas);
    }

    drawInstructions() {
        const ctx = this.uiCtx;
        const centerX = this.gameWidth / 2;
        const centerY = this.gameHeight / 2;

        // Semi-transparent overlay
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.fillRect(0, 0, this.gameWidth, this.gameHeight);

        // Main title
        ctx.fillStyle = 'white';
        ctx.font = `bold ${this.gameSettings.fontSize + 8}px Tajawal`;
        ctx.textAlign = 'center';
        ctx.fillText('🏁 مرحباً بك في سباق الفرق! (WebGL)', centerX, centerY - 120);

        // Instructions
        ctx.font = `${this.gameSettings.fontSize + 2}px Tajawal`;
        ctx.fillText('كيفية اللعب:', centerX, centerY - 80);

        const instructions = [
            '1️⃣ انضم لفريق بإرسال هدية الانضمام',
            '2️⃣ ادعم فريقك بإرسال هدايا أخرى',
            '3️⃣ أول فريق يصل للنهاية يفوز!',
            '',
            'الفرق المتاحة:'
        ];

        ctx.font = `${this.gameSettings.fontSize}px Tajawal`;
        instructions.forEach((instruction, index) => {
            ctx.fillText(instruction, centerX, centerY - 40 + (index * 25));
        });

        // Team join instructions
        this.teams.forEach((team, index) => {
            const y = centerY + 40 + (index * 30);
            ctx.fillStyle = team.color;
            ctx.fillText(`${team.joinGift} ${team.name}`, centerX, y);
        });

        // Demo hint
        ctx.fillStyle = '#ffa500';
        ctx.font = `${this.gameSettings.fontSize - 2}px Tajawal`;
        ctx.fillText('💡 اضغط "تجربة" لمشاهدة عرض توضيحي', centerX, this.gameHeight - 50);

        // WebGL info
        ctx.fillStyle = '#00ffff';
        ctx.fillText('🚀 مدعوم بـ WebGL لأداء فائق!', centerX, this.gameHeight - 80);
    }

    checkPerformance() {
        // Monitor WebGL performance
        const info = this.renderer.info;

        if (this.currentFPS < 45) {
            if (!this.enablePerformanceMode) {
                console.warn(`⚠️ Low FPS detected (${this.currentFPS}), enabling performance mode`);
                this.enablePerformanceMode = true;
                this.gameSettings.showEffects = false;

                // Reduce WebGL quality
                this.renderer.setPixelRatio(Math.min(window.devicePixelRatio * 0.5, 1));
            }
        } else if (this.currentFPS > 55 && this.enablePerformanceMode) {
            console.log(`✅ FPS improved (${this.currentFPS}), disabling performance mode`);
            this.enablePerformanceMode = false;
            this.gameSettings.showEffects = true;

            // Restore WebGL quality
            this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        }

        // Log WebGL info occasionally
        if (this.frameCount % 600 === 0) { // Every 10 seconds
            console.log('🎮 WebGL Info:', {
                geometries: info.memory.geometries,
                textures: info.memory.textures,
                calls: info.render.calls,
                triangles: info.render.triangles
            });
        }
    }

    onWindowResize() {
        this.gameWidth = window.innerWidth;
        this.gameHeight = window.innerHeight;

        // Update camera
        this.camera.aspect = this.gameWidth / this.gameHeight;
        this.camera.updateProjectionMatrix();

        // Update renderer
        this.renderer.setSize(this.gameWidth, this.gameHeight);

        // Update UI canvas
        if (this.uiCanvas) {
            this.uiCanvas.width = this.gameWidth;
            this.uiCanvas.height = this.gameHeight;
        }
    }

    // Event handlers (same as original)
    handleGift(data) {
        if (this.demoMode) return;

        const giftName = data.giftName || data.gift_name || '';
        const senderName = data.uniqueId || data.sender || 'مجهول';

        console.log(`🎁 Gift received: ${giftName} from ${senderName}`);

        // Check if it's a join gift
        const joinTeam = this.teams.find(team => team.joinGift === giftName);
        if (joinTeam && !this.supporters.has(senderName)) {
            // New supporter joins team
            this.supporters.set(senderName, joinTeam.name);
            joinTeam.supporters.push(senderName);
            console.log(`👥 ${senderName} joined ${joinTeam.name}`);

            this.showNotification(`${senderName} انضم إلى ${joinTeam.name}!`, joinTeam.color);
            this.updateStatsPanel();
            return;
        }

        // Check if it's a progress gift
        const progressValue = this.progressGifts[giftName];
        if (progressValue && this.supporters.has(senderName)) {
            const teamName = this.supporters.get(senderName);
            const team = this.teams.find(t => t.name === teamName);
            if (team) {
                team.progress += progressValue;
                console.log(`⚡ ${teamName} advanced by ${progressValue} (${senderName})`);

                this.showNotification(`${teamName} تقدم +${progressValue} بفضل ${senderName}`, team.color);
                this.createProgressParticles(team, progressValue);

                // Check for winner
                if (team.progress >= this.gameSettings.raceDistance) {
                    this.announceWinner(team);
                }

                this.updateStatsPanel();
            }
        } else if (progressValue && !this.supporters.has(senderName)) {
            this.showNotification(`${senderName}: يجب الانضمام لفريق أولاً!`, '#ff6b6b');
        }
    }

    createProgressParticles(team, value) {
        if (!this.gameSettings.showEffects) return;

        // Create Three.js particles
        const particleCount = Math.min(value, 20);
        const geometry = new THREE.BufferGeometry();
        const positions = [];
        const colors = [];
        const sizes = [];

        const teamMesh = this.teamMeshes.find(mesh => mesh.userData.team === team);
        if (!teamMesh) return;

        const teamColor = new THREE.Color(team.color);

        for (let i = 0; i < particleCount; i++) {
            // Position around team
            positions.push(
                teamMesh.position.x + (Math.random() - 0.5) * 2,
                teamMesh.position.y + Math.random() * 2,
                teamMesh.position.z + (Math.random() - 0.5) * 2
            );

            // Color
            colors.push(teamColor.r, teamColor.g, teamColor.b);

            // Size
            sizes.push(Math.random() * 0.1 + 0.05);
        }

        geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.Float32BufferAttribute(sizes, 1));

        const material = new THREE.PointsMaterial({
            size: 0.1,
            vertexColors: true,
            transparent: true,
            opacity: 0.8
        });

        const particles = new THREE.Points(geometry, material);
        this.scene.add(particles);

        // Animate particles
        const startTime = performance.now();
        const animate = () => {
            const elapsed = performance.now() - startTime;
            const progress = elapsed / 2000; // 2 seconds

            if (progress < 1) {
                particles.position.y += 0.02;
                particles.rotation.y += 0.02;
                material.opacity = 1 - progress;
                requestAnimationFrame(animate);
            } else {
                this.scene.remove(particles);
                geometry.dispose();
                material.dispose();
            }
        };
        animate();
    }

    announceWinner(winningTeam) {
        console.log(`🏆 ${winningTeam.name} WINS!`);
        this.showWinnerAnimation(winningTeam);

        if (this.gameSettings.autoReset) {
            setTimeout(() => {
                this.resetRace();
            }, 5000);
        }
    }

    showWinnerAnimation(team) {
        // Create fireworks effect in Three.js
        const teamMesh = this.teamMeshes.find(mesh => mesh.userData.team === team);
        if (!teamMesh) return;

        // Scale up winner
        const originalScale = teamMesh.scale.clone();
        const targetScale = originalScale.clone().multiplyScalar(2);

        const animate = () => {
            teamMesh.scale.lerp(targetScale, 0.1);
            teamMesh.rotation.y += 0.1;

            if (teamMesh.scale.x < targetScale.x * 0.9) {
                requestAnimationFrame(animate);
            }
        };
        animate();

        // Reset after animation
        setTimeout(() => {
            teamMesh.scale.copy(originalScale);
        }, 3000);
    }

    resetRace() {
        this.teams.forEach(team => {
            team.progress = 0;
            team.supporters = [];
        });
        this.supporters.clear();
        this.particles = [];

        // Reset team positions
        this.teamMeshes.forEach((mesh, index) => {
            const team = this.teams[index];
            const teamHeight = team.height || 0.3;
            mesh.position.x = -8;
            mesh.position.y = teamHeight; // Maintain custom height
            mesh.rotation.y = 0;
            mesh.scale.set(1, 1, 1);
        });

        this.updateStatsPanel();
        console.log('🔄 Race reset (WebGL)');
    }

    // UI and settings
    setupUI() {
        window.gameInstance = this;
        this.setupImageUpload();
    }

    setupImageUpload() {
        // Create file input for team images
        this.teams.forEach((team, index) => {
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = 'image/*';
            fileInput.style.display = 'none';
            fileInput.id = `teamImage_${index}`;

            fileInput.addEventListener('change', (e) => {
                this.handleImageUpload(e, index);
            });

            document.body.appendChild(fileInput);
        });
    }

    handleImageUpload(event, teamIndex) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                this.teams[teamIndex].customImage = img;
                this.saveSettings();

                // Update the 3D mesh
                this.updateTeamCustomImages();

                const fileType = file.type === 'image/gif' ? 'GIF' : 'صورة';
                this.showNotification(`تم رفع ${fileType} ${this.teams[teamIndex].name} بنجاح! (WebGL)`, '#27ae60');

                console.log(`✅ ${fileType} uploaded for ${this.teams[teamIndex].name} (WebGL)`);
            };
            img.onerror = () => {
                this.showNotification(`خطأ في تحميل الصورة لـ ${this.teams[teamIndex].name}`, '#e74c3c');
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    connectSocket() {
        // Socket connection logic (same as original)
        if (typeof io !== 'undefined') {
            this.socket = io();

            this.socket.on('connect', () => {
                this.isConnected = true;
                console.log('🔌 Connected to server');
            });

            this.socket.on('disconnect', () => {
                this.isConnected = false;
                console.log('🔌 Disconnected from server');
            });

            this.socket.on('gift', (data) => this.handleGift(data));
        }
    }

    checkURLParams() {
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('demo') === 'true') {
            setTimeout(() => this.startDemo(), 1000);
        }
    }

    showNotification(message, color = '#3498db') {
        // Create notification element
        const notification = document.createElement('div');
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${color};
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-family: Tajawal, Arial, sans-serif;
            font-size: 14px;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    updateStatsPanel() {
        const statsContainer = document.getElementById('teamStats');
        if (!statsContainer) return;

        const sortedTeams = [...this.teams].sort((a, b) => b.progress - a.progress);

        statsContainer.innerHTML = sortedTeams.map((team, index) => {
            const progressRatio = Math.min(team.progress / this.gameSettings.raceDistance, 1);
            const isWinning = index === 0 && team.progress > 0;

            return `
                <div class="team-stat" style="border: 2px solid ${team.color}; ${isWinning ? 'box-shadow: 0 0 15px ' + team.color + ';' : ''}">
                    <div class="team-name" style="color: ${team.color};">
                        ${isWinning ? '👑 ' : ''}${team.name}
                    </div>
                    <div class="team-progress">
                        ${team.progress}/${this.gameSettings.raceDistance}
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progressRatio * 100}%; background: ${team.color};"></div>
                    </div>
                    <div class="team-supporters">
                        الداعمون: ${team.supporters.length}
                        ${team.supporters.length > 0 ? '<br>' + team.supporters.slice(0, 2).join(', ') + (team.supporters.length > 2 ? '...' : '') : ''}
                    </div>
                </div>
            `;
        }).join('');
    }

    // Demo mode
    startDemo() {
        this.demoMode = !this.demoMode;
        const btn = document.querySelector('.demo-btn');

        if (this.demoMode) {
            btn.textContent = '⏹️ إيقاف التجربة';
            btn.style.background = '#e74c3c';
            this.startDemoSimulation();
        } else {
            btn.textContent = '🧪 تجربة';
            btn.style.background = '';
            this.stopDemoSimulation();
        }
    }

    startDemoSimulation() {
        const demoSupporters = ['أحمد', 'فاطمة', 'محمد', 'عائشة', 'علي', 'زينب', 'خالد', 'مريم'];
        const allGifts = [...Object.keys(this.progressGifts), ...this.teams.map(t => t.joinGift)];

        // Ensure each team has supporters
        this.teams.forEach((team, index) => {
            if (team.supporters.length === 0 && index < demoSupporters.length) {
                this.handleGift({
                    giftName: team.joinGift,
                    uniqueId: demoSupporters[index]
                });
            }
        });

        this.demoInterval = setInterval(() => {
            const randomSupporter = demoSupporters[Math.floor(Math.random() * demoSupporters.length)];

            let randomGift;
            if (Math.random() < 0.7 && this.supporters.has(randomSupporter)) {
                const progressGiftKeys = Object.keys(this.progressGifts);
                randomGift = progressGiftKeys[Math.floor(Math.random() * progressGiftKeys.length)];
            } else {
                randomGift = allGifts[Math.floor(Math.random() * allGifts.length)];
            }

            this.handleGift({
                giftName: randomGift,
                uniqueId: randomSupporter
            });
        }, 1500 + Math.random() * 1000);
    }

    stopDemoSimulation() {
        if (this.demoInterval) {
            clearInterval(this.demoInterval);
            this.demoInterval = null;
        }
    }

    // Settings management
    saveSettings() {
        const settings = {
            teams: this.teams.map(team => ({
                ...team,
                customImage: null
            })),
            progressGifts: this.progressGifts,
            gameSettings: this.gameSettings,
            performanceSettings: {
                targetFPS: this.targetFPS,
                enableVSync: this.enableVSync,
                enablePerformanceMode: this.enablePerformanceMode
            }
        };
        localStorage.setItem('teamRacingSettings', JSON.stringify(settings));
    }

    loadSettings() {
        try {
            const saved = localStorage.getItem('teamRacingSettings');
            if (saved) {
                const settings = JSON.parse(saved);
                if (settings.teams) this.teams = settings.teams;
                if (settings.progressGifts) this.progressGifts = settings.progressGifts;
                if (settings.gameSettings) {
                    this.gameSettings = { ...this.gameSettings, ...settings.gameSettings };
                }
                if (settings.performanceSettings) {
                    this.targetFPS = settings.performanceSettings.targetFPS || 60;
                    this.frameInterval = 1000 / this.targetFPS;
                    this.enableVSync = settings.performanceSettings.enableVSync !== false;
                    this.enablePerformanceMode = settings.performanceSettings.enablePerformanceMode || false;
                }
            }
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    // Cleanup
    destroy() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
        if (this.demoInterval) {
            clearInterval(this.demoInterval);
        }
        if (this.socket) {
            this.socket.disconnect();
        }

        // Dispose Three.js resources
        this.scene.traverse((object) => {
            if (object.geometry) object.geometry.dispose();
            if (object.material) {
                if (Array.isArray(object.material)) {
                    object.material.forEach(material => material.dispose());
                } else {
                    object.material.dispose();
                }
            }
        });

        this.renderer.dispose();

        console.log('🧹 Three.js resources cleaned up');
    }

    // Global functions
    toggleSettings() {
        const settingsPanel = document.getElementById('settingsPanel');
        settingsPanel.classList.toggle('show');
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.gameInstance = new TeamRacingGameThreeJS();
});

// Global functions for HTML interface
window.toggleSettings = () => gameInstance?.toggleSettings();
window.startDemo = () => gameInstance?.startDemo();
window.resetRace = () => gameInstance?.resetRace();
window.toggleFullscreen = () => gameInstance?.toggleFullscreen();

// Performance functions
window.setTargetFPS = (fps) => {
    if (gameInstance) {
        gameInstance.targetFPS = fps;
        gameInstance.frameInterval = 1000 / fps;
        gameInstance.saveSettings();
        console.log(`🎯 Target FPS set to ${fps} (WebGL)`);
    }
};

window.toggleVSync = () => {
    if (gameInstance) {
        gameInstance.enableVSync = !gameInstance.enableVSync;
        gameInstance.saveSettings();
        console.log(`🔄 VSync ${gameInstance.enableVSync ? 'enabled' : 'disabled'} (WebGL)`);
    }
};

window.getPerformanceInfo = () => {
    if (gameInstance) {
        const info = {
            currentFPS: gameInstance.currentFPS,
            targetFPS: gameInstance.targetFPS,
            enableVSync: gameInstance.enableVSync,
            performanceMode: gameInstance.enablePerformanceMode,
            frameCount: gameInstance.frameCount,
            renderer: 'WebGL',
            webglInfo: gameInstance.renderer.info
        };

        console.log('📊 معلومات الأداء (WebGL):', info);
        return info;
    }
    return null;
};

// Enhanced team management functions
window.updateTeamSizes = () => {
    if (gameInstance && gameInstance.teamMeshes) {
        gameInstance.teamMeshes.forEach((teamGroup, index) => {
            const team = gameInstance.teams[index];
            const globalSize = gameInstance.gameSettings.globalTeamSize || 1.0;
            const teamSize = (team.size || 1.0) * globalSize;

            // Update base mesh scale
            if (teamGroup.userData.baseMesh) {
                teamGroup.userData.baseMesh.scale.setScalar(teamSize);
            }

            // Update custom image size if exists
            if (teamGroup.userData.imageMesh) {
                const imageSize = 1.2 * teamSize;
                teamGroup.userData.imageMesh.scale.set(imageSize, imageSize, 1);
            }
        });
        console.log('📏 Team sizes updated');
    }
};

window.updateTeamHeights = () => {
    if (gameInstance) {
        gameInstance.updateTeamHeights();
        console.log('📐 Team heights updated');
    }
};

window.updateCameraMode = (mode) => {
    if (gameInstance) {
        gameInstance.gameSettings.cameraMode = mode;
        gameInstance.saveSettings();

        switch (mode) {
            case 'follow':
                // Camera follows leading team
                break;
            case 'fixed':
                gameInstance.camera.position.set(0, 5, 10);
                gameInstance.camera.lookAt(0, 0, 0);
                break;
            case 'overview':
                gameInstance.camera.position.set(0, 10, 15);
                gameInstance.camera.lookAt(0, 0, 0);
                break;
        }

        console.log(`📷 Camera mode set to: ${mode}`);
    }
};

window.assignImageToTeam = (teamIndex, fileName) => {
    if (gameInstance && gameInstance.teams[teamIndex]) {
        // This would be called from drag & drop functionality
        console.log(`🖼️ Assigning ${fileName} to team ${teamIndex}`);
        // Implementation would depend on how the file is handled
    }
};

window.closeImageDialog = () => {
    const dialog = document.querySelector('.image-upload-dialog');
    if (dialog) {
        dialog.remove();
    }
};
