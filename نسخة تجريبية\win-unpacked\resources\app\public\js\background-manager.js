/**
 * مدير خلفية التطبيق
 * يقوم بتطبيق الخلفية المحفوظة على جميع صفحات التطبيق
 */
const BackgroundManager = {
  /**
   * تهيئة مدير الخلفية
   */
  initialize: function() {
    // تطبيق الخلفية المحفوظة
    this.applyBackground();

    // الاستماع لتغييرات الخلفية
    if (typeof socket !== 'undefined') {
      socket.on('mediaSettingsUpdated', (data) => {
        if (data && data.settings) {
          this.saveSettings(data.settings);
          this.applyBackground();
        }
      });
    }
  },

  /**
   * حفظ إعدادات الخلفية
   * @param {Object} settings - إعدادات الخلفية
   */
  saveSettings: function(settings) {
    localStorage.setItem('background.renderMode', settings.renderMode || 'auto');
    localStorage.setItem('background.size', settings.size || 'cover');
    localStorage.setItem('background.opacity', settings.opacity || 15);

    // تجنب حفظ روابط blob
    const mediaUrl = settings.mediaUrl || '';
    if (!mediaUrl.startsWith('blob:')) {
      localStorage.setItem('background.url', mediaUrl);
    }
  },

  /**
   * استرجاع إعدادات الخلفية
   * @returns {Object} إعدادات الخلفية
   */
  getSettings: function() {
    // استرجاع رابط الوسائط
    let mediaUrl = localStorage.getItem('background.url') || '';

    // تجنب استخدام روابط blob
    if (mediaUrl.startsWith('blob:')) {
      mediaUrl = '';
      // مسح الرابط غير الصالح من التخزين المحلي
      localStorage.setItem('background.url', '');
    }

    return {
      renderMode: localStorage.getItem('background.renderMode') || 'auto',
      size: localStorage.getItem('background.size') || 'cover',
      opacity: parseInt(localStorage.getItem('background.opacity') || '15', 10),
      mediaUrl: mediaUrl
    };
  },

  /**
   * تطبيق الخلفية على الصفحة الحالية
   */
  applyBackground: function() {
    const settings = this.getSettings();

    // إذا لم يكن هناك رابط أو كان الرابط من نوع blob، لا تفعل شيئًا
    if (!settings.mediaUrl || settings.mediaUrl.startsWith('blob:')) return;

    // إزالة الخلفية الحالية إن وجدت
    const existingBg = document.getElementById('app-background');
    if (existingBg) {
      document.body.removeChild(existingBg);
    }

    // إنشاء حاوية الخلفية
    const bgContainer = document.createElement('div');
    bgContainer.id = 'app-background';
    bgContainer.style.position = 'fixed';
    bgContainer.style.top = '0';
    bgContainer.style.left = '0';
    bgContainer.style.width = '100%';
    bgContainer.style.height = '100%';
    bgContainer.style.zIndex = '-1';
    bgContainer.style.overflow = 'hidden';
    bgContainer.style.pointerEvents = 'none'; // لتجنب التداخل مع النقر

    // تحديد نوع الملف
    let fileType = '';
    if (settings.mediaUrl) {
      // تحقق من نوع الملف من خلال امتداده
      const fileExt = settings.mediaUrl.split('.').pop().toLowerCase();
      if (['mp4', 'webm'].includes(fileExt)) {
        fileType = 'video';
      } else if (['gif', 'webp', 'jpg', 'jpeg', 'png'].includes(fileExt)) {
        fileType = 'image';
      } else {
        // إذا لم يكن هناك امتداد واضح، حاول تخمين النوع من الرابط
        if (settings.mediaUrl.includes('mp4') || settings.mediaUrl.includes('webm')) {
          fileType = 'video';
        } else {
          fileType = 'image'; // افتراضي
        }
      }
    }

    // إنشاء العنصر المناسب حسب نوع الملف
    if (fileType === 'video' || settings.renderMode === 'video') {
      // إنشاء عنصر فيديو
      const video = document.createElement('video');
      video.src = settings.mediaUrl;
      video.autoplay = true;
      video.loop = true;
      video.muted = true;
      video.playsInline = true; // مهم للأجهزة المحمولة
      video.style.width = '100%';
      video.style.height = '100%';
      video.style.objectFit = 'cover'; // الافتراضي

      // تطبيق حجم الفيديو
      if (settings.size === 'cover' || settings.size === 'contain') {
        video.style.objectFit = settings.size;
      } else if (settings.size === '100%') {
        video.style.objectFit = 'contain';
      } else if (settings.size === '75%') {
        video.style.objectFit = 'contain';
        video.style.width = '75%';
        video.style.height = '75%';
        video.style.margin = 'auto';
        video.style.display = 'block';
      } else if (settings.size === '50%') {
        video.style.objectFit = 'contain';
        video.style.width = '50%';
        video.style.height = '50%';
        video.style.margin = 'auto';
        video.style.display = 'block';
      }

      // تطبيق الشفافية
      video.style.opacity = settings.opacity / 100;

      // التأكد من تشغيل الفيديو
      video.addEventListener('canplay', () => {
        video.play().catch(e => console.error('خطأ في تشغيل الفيديو:', e));
      });

      // معالجة أخطاء تحميل الفيديو
      video.addEventListener('error', () => {
        console.error('خطأ في تحميل الفيديو:', settings.mediaUrl);
        // محاولة استخدام صورة بدلاً من ذلك
        createImageElement();
      });

      // إضافة الفيديو إلى الحاوية
      bgContainer.appendChild(video);
    } else {
      // إنشاء عنصر صورة
      createImageElement();
    }

    // دالة لإنشاء عنصر صورة
    function createImageElement() {
      const img = document.createElement('img');
      img.src = settings.mediaUrl;
      img.style.width = '100%';
      img.style.height = '100%';
      img.style.objectFit = 'cover'; // الافتراضي

      // تطبيق حجم الصورة
      if (settings.size === 'cover' || settings.size === 'contain') {
        img.style.objectFit = settings.size;
      } else if (settings.size === '100%') {
        img.style.objectFit = 'contain';
      } else if (settings.size === '75%') {
        img.style.objectFit = 'contain';
        img.style.width = '75%';
        img.style.height = '75%';
        img.style.margin = 'auto';
        img.style.display = 'block';
      } else if (settings.size === '50%') {
        img.style.objectFit = 'contain';
        img.style.width = '50%';
        img.style.height = '50%';
        img.style.margin = 'auto';
        img.style.display = 'block';
      }

      // تطبيق الشفافية
      img.style.opacity = settings.opacity / 100;

      // معالجة أخطاء تحميل الصورة
      img.addEventListener('error', () => {
        console.error('خطأ في تحميل الصورة:', settings.mediaUrl);
      });

      // إضافة الصورة إلى الحاوية
      bgContainer.appendChild(img);
    }

    // إضافة الحاوية إلى الصفحة
    document.body.insertBefore(bgContainer, document.body.firstChild);
  }
};

// تهيئة مدير الخلفية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  BackgroundManager.initialize();
});
