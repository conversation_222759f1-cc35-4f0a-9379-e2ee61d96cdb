<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚙️ إعدادات لعبة Fish Eat Fish</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .settings-card {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .settings-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        }

        .card-header h3 {
            font-size: 1.5em;
            margin-right: 10px;
        }

        .setting-group {
            margin-bottom: 20px;
        }

        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            transition: background 0.3s ease;
        }

        .setting-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .gift-preview {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .gift-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            padding: 4px;
            transition: all 0.3s ease;
        }

        .gift-icon:hover {
            transform: scale(1.1);
            background: rgba(255, 255, 255, 0.2);
        }

        /* نظام التعيينات */
        .add-assignment-btn {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .add-assignment-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 184, 148, 0.3);
        }

        .assignment-item {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            margin-bottom: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .assignment-item:hover {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .assignment-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: rgba(0, 0, 0, 0.2);
        }

        .gift-name-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            padding: 6px 10px;
            color: white;
            font-size: 12px;
            max-width: 150px;
        }

        .gift-name-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 6px rgba(102, 126, 234, 0.3);
        }

        .remove-assignment {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            margin-left: 8px;
            transition: all 0.3s ease;
            font-size: 11px;
        }

        .remove-assignment:hover {
            background: #c0392b;
            transform: scale(1.05);
        }

        .assignment-body {
            padding: 8px 12px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .assignment-row {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .assignment-row label {
            font-size: 11px;
            font-weight: 500;
            min-width: 80px;
            color: #ecf0f1;
            white-space: nowrap;
        }

        .assignment-row select,
        .assignment-row input {
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            padding: 4px 8px;
            color: white;
            font-size: 11px;
            flex: 1;
            min-width: 100px;
        }

        .assignment-row select:focus,
        .assignment-row input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 4px rgba(102, 126, 234, 0.3);
        }

        /* تحسين للشاشات الصغيرة */
        @media (max-width: 768px) {
            .assignment-body {
                grid-template-columns: 1fr;
            }

            .assignment-row label {
                min-width: 70px;
                font-size: 10px;
            }

            .assignment-row select,
            .assignment-row input {
                font-size: 10px;
                min-width: 80px;
            }
        }

        .setting-label {
            font-size: 1.1em;
            font-weight: 500;
            min-width: 150px;
        }

        .setting-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        select, input[type="range"], input[type="number"] {
            background: rgba(0, 0, 0, 0.6);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 1em;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
        }

        select {
            cursor: pointer;
            min-width: 150px;
        }

        input[type="range"] {
            width: 120px;
            cursor: pointer;
        }

        input[type="checkbox"] {
            transform: scale(1.5);
            cursor: pointer;
            margin-left: 10px;
        }

        .volume-display {
            min-width: 50px;
            text-align: center;
            font-weight: bold;
            color: #667eea;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #ff416c, #ff4b2b);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .preview-section {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }

        .preview-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }

        .preview-btn {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .preview-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .settings-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .setting-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .setting-control {
                width: 100%;
                justify-content: space-between;
            }
        }

        .status-message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .status-message.show {
            transform: translateX(0);
        }

        .status-success {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
        }

        .status-error {
            background: linear-gradient(45deg, #ff416c, #ff4b2b);
        }

        .status-info {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        /* أنماط رفع الملفات الصوتية */
        .custom-sounds-section {
            margin-top: 25px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .custom-sounds-section h4 {
            color: #f39c12;
            margin-bottom: 15px;
            text-align: center;
            font-size: 18px;
        }

        .sound-upload-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .sound-upload-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            border: 2px dashed rgba(255, 255, 255, 0.2);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
        }

        .sound-upload-item:hover {
            border-color: rgba(243, 156, 18, 0.5);
            background: rgba(243, 156, 18, 0.1);
        }

        .sound-upload-item.has-file {
            border-color: #27ae60;
            background: rgba(39, 174, 96, 0.1);
        }

        .sound-upload-item label {
            display: block;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #ecf0f1;
        }

        .sound-upload-item input[type="file"] {
            width: 100%;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: white;
            font-size: 12px;
            cursor: pointer;
        }

        .sound-upload-item input[type="file"]::-webkit-file-upload-button {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            margin-right: 8px;
        }

        .sound-upload-item input[type="file"]::-webkit-file-upload-button:hover {
            background: linear-gradient(45deg, #e67e22, #d35400);
        }

        .remove-custom-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #e74c3c;
            color: white;
            border: none;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .remove-custom-btn:hover {
            background: #c0392b;
            transform: scale(1.1);
        }

        .upload-info {
            background: rgba(52, 152, 219, 0.1);
            border: 1px solid rgba(52, 152, 219, 0.3);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .upload-info p {
            margin: 5px 0;
            font-size: 13px;
            color: #bdc3c7;
        }

        .upload-status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 6px;
            font-size: 12px;
            text-align: center;
        }

        .upload-status.success {
            background: rgba(39, 174, 96, 0.2);
            color: #27ae60;
            border: 1px solid rgba(39, 174, 96, 0.3);
        }

        .upload-status.error {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.3);
        }

        .upload-status.loading {
            background: rgba(243, 156, 18, 0.2);
            color: #f39c12;
            border: 1px solid rgba(243, 156, 18, 0.3);
        }

        .custom-sound-indicator {
            position: absolute;
            top: -5px;
            left: -5px;
            background: #27ae60;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            z-index: 10;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .preview-btn.has-custom {
            border: 2px solid #27ae60;
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            position: relative;
        }

        .preview-btn.has-custom::before {
            content: '🎵';
            position: absolute;
            top: -8px;
            right: -8px;
            background: #27ae60;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            animation: pulse 2s infinite;
        }

        /* أنماط نظام التعيينات الموسع */
        .assignment-item {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .assignment-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .assignment-item:hover {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
        }

        .assignment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.15);
        }

        .assignment-id {
            font-weight: bold;
            color: #4CAF50;
            font-size: 14px;
            background: rgba(76, 175, 80, 0.2);
            padding: 4px 12px;
            border-radius: 20px;
            border: 1px solid rgba(76, 175, 80, 0.3);
        }

        .assignment-fields {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 18px;
        }

        .field-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .field-group label {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .field-group input,
        .field-group select {
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.25);
            border-radius: 8px;
            padding: 10px 14px;
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .field-group input:focus,
        .field-group select:focus {
            outline: none;
            border-color: #4CAF50;
            background: rgba(0, 0, 0, 0.6);
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2), inset 0 2px 4px rgba(0, 0, 0, 0.3);
            transform: translateY(-1px);
        }

        .field-group select option {
            background: #2c3e50;
            color: white;
            padding: 8px;
        }

        .remove-assignment-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            border: none;
            border-radius: 8px;
            padding: 10px 16px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        }

        .remove-assignment-btn:hover {
            background: linear-gradient(45deg, #c0392b, #a93226);
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.5);
        }

        .add-assignment-btn {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
        }

        .add-assignment-btn:hover {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(39, 174, 96, 0.5);
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .assignment-fields {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .field-group input,
            .field-group select {
                font-size: 16px; /* منع التكبير في iOS */
            }
        }

        /* أنماط أزرار الإخفاء/الإظهار */
        .setting-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .setting-control input[type="checkbox"] {
            width: 20px;
            height: 20px;
            accent-color: #4CAF50;
            cursor: pointer;
        }

        .toggle-text {
            font-weight: bold;
            color: #4CAF50;
            min-width: 50px;
            text-align: center;
            padding: 4px 8px;
            border-radius: 4px;
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            transition: all 0.3s ease;
        }

        .setting-control input[type="checkbox"]:not(:checked) + .toggle-text {
            color: #f44336;
            background: rgba(244, 67, 54, 0.1);
            border-color: rgba(244, 67, 54, 0.3);
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>⚙️ إعدادات لعبة Fish Eat Fish</h1>
            <p>تخصيص شامل لتجربة اللعب المثالية</p>
        </div>

        <!-- Settings Grid -->
        <div class="settings-grid">
            <!-- Gift Assignment System -->
            <div class="settings-card">
                <div class="card-header">
                    <h3>🎁 نظام تعيينات الأحداث</h3>
                    <button class="add-assignment-btn" onclick="addNewAssignment()">➕ إضافة تعيين جديد</button>
                </div>
                <div class="setting-group" id="assignmentsList">
                    <!-- سيتم إنشاء التعيينات ديناميكياً بواسطة JavaScript -->
                </div>

                <div class="assignment-template" style="display: none;">
                    <div class="assignment-item">
                        <div class="assignment-header">
                            <input type="text" class="gift-name-input" placeholder="اسم الحدث (مثل: Rose, Join, Like, Follow)">
                            <button class="remove-assignment" onclick="removeAssignment(this)">🗑️</button>
                        </div>
                        <div class="assignment-body">
                            <div class="assignment-row">
                                <label>نوع الكائن:</label>
                                <select class="creature-type">
                                    <option value="random">🎲 عشوائي</option>
                                    <option value="normal">🐟 سمكة عادية</option>
                                    <option value="salmon">🐠 سلمون</option>
                                    <option value="dolphin">🐬 دولفين</option>
                                    <option value="shark">🦈 قرش</option>
                                    <option value="whale">🐋 حوت</option>
                                    <option value="eel">⚡ أنقليس</option>
                                    <option value="ray">🌊 راي</option>
                                    <option value="jellyfish">🎐 قنديل البحر</option>
                                    <option value="octopus">🐙 أخطبوط</option>
                                    <option value="seahorse">🐴 حصان البحر</option>
                                    <option value="turtle">🐢 سلحفاة</option>
                                    <option value="crab">🦀 سرطان البحر</option>
                                    <option value="lobster">🦞 كركند</option>
                                    <option value="starfish">⭐ نجمة البحر</option>
                                    <option value="anglerfish">🎣 سمكة الصياد</option>
                                    <option value="swordfish">⚔️ سمكة السيف</option>
                                    <option value="hammerhead">🔨 القرش المطرقة</option>
                                </select>
                            </div>
                            <div class="assignment-row">
                                <label>الحد الأدنى للكمية:</label>
                                <input type="number" class="min-count" value="1" min="1">
                            </div>
                            <div class="assignment-row">
                                <label>مضاعف الحجم:</label>
                                <input type="number" class="size-multiplier" value="1" min="0.1" max="5" step="0.1">
                            </div>
                            <div class="assignment-row">
                                <label>تأثير خاص:</label>
                                <select class="special-effect">
                                    <option value="none">بدون تأثير</option>
                                    <option value="speed_boost">تسريع</option>
                                    <option value="size_boost">تكبير</option>
                                    <option value="wild_mode">وضع متوحش</option>
                                    <option value="electric">كهربائي</option>
                                    <option value="poison">سام</option>
                                    <option value="invisible">شبه مخفي</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fish Auto-Delete Settings -->
            <div class="settings-card">
                <div class="card-header">
                    <h3>🗑️ إعدادات حذف الأسماك التلقائي</h3>
                </div>
                <div class="setting-group">
                    <div class="setting-item">
                        <label class="setting-label">تفعيل الحذف التلقائي:</label>
                        <div class="setting-control">
                            <input type="checkbox" id="autoDeleteEnabled">
                            <span class="toggle-text">معطل</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">مدة بقاء السمكة (بالثواني):</label>
                        <div class="setting-control">
                            <input type="number" id="fishLifetime" min="10" max="300" value="60" style="width: 80px; padding: 5px; border-radius: 5px; border: 1px solid #555; background: rgba(0,0,0,0.7); color: white;">
                            <span style="color: #00ff7f; font-size: 12px;">ثانية</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">الحد الأقصى للأسماك:</label>
                        <div class="setting-control">
                            <input type="number" id="maxFishLimit" min="10" max="500" value="100" style="width: 80px; padding: 5px; border-radius: 5px; border: 1px solid #555; background: rgba(0,0,0,0.7); color: white;">
                            <span style="color: #00ff7f; font-size: 12px;">سمكة</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- UI Visibility Settings -->
            <div class="settings-card">
                <div class="card-header">
                    <h3>👁️ إعدادات إخفاء/إظهار القوائم</h3>
                </div>
                <div class="setting-group">
                    <div class="setting-item">
                        <label class="setting-label">🏆 قائمة اللاعبين النشطين:</label>
                        <div class="setting-control">
                            <input type="checkbox" id="showActivePlayersToggle">
                            <span class="toggle-text">إخفاء</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">📊 إحصائيات اللعبة:</label>
                        <div class="setting-control">
                            <input type="checkbox" id="showGameStatsToggle">
                            <span class="toggle-text">إخفاء</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">🎮 قائمة التحكم:</label>
                        <div class="setting-control">
                            <input type="checkbox" id="showControlsToggle">
                            <span class="toggle-text">إخفاء</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">🏅 قائمة الإنجازات:</label>
                        <div class="setting-control">
                            <input type="checkbox" id="showAchievementsToggle">
                            <span class="toggle-text">إخفاء</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Audio Settings -->
            <div class="settings-card">
                <div class="card-header">
                    <h3>🔊 إعدادات الصوت</h3>
                </div>
                <div class="setting-group">
                    <div class="setting-item">
                        <span class="setting-label">تفعيل الأصوات</span>
                        <div class="setting-control">
                            <input type="checkbox" id="soundEnabled" checked>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">تفعيل الموسيقى</span>
                        <div class="setting-control">
                            <input type="checkbox" id="musicEnabled" checked>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">مستوى الأصوات</span>
                        <div class="setting-control">
                            <input type="range" id="effectsVolume" min="0" max="1" step="0.1" value="0.7">
                            <span class="volume-display" id="effectsVolumeValue">70%</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">مستوى الموسيقى</span>
                        <div class="setting-control">
                            <input type="range" id="musicVolume" min="0" max="1" step="0.1" value="0.3">
                            <span class="volume-display" id="musicVolumeValue">30%</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">أصوات مرعبة للأسماك القوية</span>
                        <div class="setting-control">
                            <input type="checkbox" id="scaryEnabled" checked>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">أصوات الأكل الواقعية</span>
                        <div class="setting-control">
                            <input type="checkbox" id="realisticEating" checked>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">🌊 أصوات المحيط</span>
                        <div class="setting-control">
                            <input type="checkbox" id="ambientSounds" checked>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">🌊 مستوى أصوات المحيط</span>
                        <div class="setting-control">
                            <input type="range" id="ambientVolume" min="0" max="1" step="0.1" value="0.3">
                            <span class="volume-display" id="ambientVolumeValue">30%</span>
                        </div>
                    </div>
                </div>

                <!-- Sound Preview -->
                <div class="preview-section">
                    <h4>🎵 معاينة الأصوات</h4>
                    <div class="preview-buttons">
                        <button class="preview-btn" onclick="previewSound('eat_small')">🍽️ أكل صغير</button>
                        <button class="preview-btn" onclick="previewSound('eat_large')">🍖 أكل كبير</button>
                        <button class="preview-btn" onclick="previewSound('shark_roar')">🦈 زئير قرش</button>
                        <button class="preview-btn" onclick="previewSound('whale_call')">🐋 نداء حوت</button>
                        <button class="preview-btn" onclick="previewSound('spawn')">✨ ظهور</button>
                        <button class="preview-btn" onclick="previewSound('ocean_waves')">🌊 أمواج البحر</button>
                        <button class="preview-btn" onclick="previewSound('underwater_bubbles')">💧 فقاعات</button>
                        <button class="preview-btn" onclick="previewSound('deep_sea')">🌑 أعماق البحر</button>
                        <button class="preview-btn" onclick="previewSound('whale_song')">🎵 أغنية الحوت</button>
                    </div>

                    <!-- قسم رفع الملفات الصوتية المخصصة -->
                    <div class="custom-sounds-section">
                        <h4>🎵 رفع أصوات مخصصة</h4>
                        <div class="sound-upload-grid">
                            <div class="sound-upload-item">
                                <label>🍽️ أكل صغير</label>
                                <input type="file" id="upload_eat_small" accept="audio/*" onchange="uploadCustomSound('eat_small', this)">
                                <button class="remove-custom-btn" onclick="removeCustomSound('eat_small')" style="display: none;">🗑️</button>
                            </div>
                            <div class="sound-upload-item">
                                <label>🍖 أكل كبير</label>
                                <input type="file" id="upload_eat_large" accept="audio/*" onchange="uploadCustomSound('eat_large', this)">
                                <button class="remove-custom-btn" onclick="removeCustomSound('eat_large')" style="display: none;">🗑️</button>
                            </div>
                            <div class="sound-upload-item">
                                <label>🦈 زئير قرش</label>
                                <input type="file" id="upload_shark_roar" accept="audio/*" onchange="uploadCustomSound('shark_roar', this)">
                                <button class="remove-custom-btn" onclick="removeCustomSound('shark_roar')" style="display: none;">🗑️</button>
                            </div>
                            <div class="sound-upload-item">
                                <label>🐋 نداء حوت</label>
                                <input type="file" id="upload_whale_call" accept="audio/*" onchange="uploadCustomSound('whale_call', this)">
                                <button class="remove-custom-btn" onclick="removeCustomSound('whale_call')" style="display: none;">🗑️</button>
                            </div>
                            <div class="sound-upload-item">
                                <label>✨ ظهور</label>
                                <input type="file" id="upload_spawn" accept="audio/*" onchange="uploadCustomSound('spawn', this)">
                                <button class="remove-custom-btn" onclick="removeCustomSound('spawn')" style="display: none;">🗑️</button>
                            </div>
                            <div class="sound-upload-item">
                                <label>🌊 أمواج البحر</label>
                                <input type="file" id="upload_ocean_waves" accept="audio/*" onchange="uploadCustomSound('ocean_waves', this)">
                                <button class="remove-custom-btn" onclick="removeCustomSound('ocean_waves')" style="display: none;">🗑️</button>
                            </div>
                            <div class="sound-upload-item">
                                <label>💧 فقاعات</label>
                                <input type="file" id="upload_underwater_bubbles" accept="audio/*" onchange="uploadCustomSound('underwater_bubbles', this)">
                                <button class="remove-custom-btn" onclick="removeCustomSound('underwater_bubbles')" style="display: none;">🗑️</button>
                            </div>
                            <div class="sound-upload-item">
                                <label>🌑 أعماق البحر</label>
                                <input type="file" id="upload_deep_sea" accept="audio/*" onchange="uploadCustomSound('deep_sea', this)">
                                <button class="remove-custom-btn" onclick="removeCustomSound('deep_sea')" style="display: none;">🗑️</button>
                            </div>
                            <div class="sound-upload-item">
                                <label>🎵 أغنية الحوت</label>
                                <input type="file" id="upload_whale_song" accept="audio/*" onchange="uploadCustomSound('whale_song', this)">
                                <button class="remove-custom-btn" onclick="removeCustomSound('whale_song')" style="display: none;">🗑️</button>
                            </div>
                        </div>
                        <div class="upload-info">
                            <p>📁 الصيغ المدعومة: MP3, WAV, OGG, M4A</p>
                            <p>📏 الحد الأقصى: 10MB لكل ملف</p>
                            <p>⏱️ المدة المثلى: 1-10 ثوان</p>
                        </div>

                        <div class="upload-info" style="background: rgba(231, 76, 60, 0.1); border-color: rgba(231, 76, 60, 0.3);">
                            <p><strong>🔇 نظام إغلاق الأصوات المزعجة:</strong></p>
                            <p>• عند رفع أي صوت مخصص، تُغلق جميع الأصوات الافتراضية</p>
                            <p>• ستعمل الأصوات المخصصة فقط - لا توجد أصوات مزعجة</p>
                            <p>• لاستعادة الأصوات الافتراضية، احذف جميع الأصوات المخصصة</p>
                            <p>• للحصول على صمت كامل، ارفع ملف صوتي صامت</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Game Behavior Settings -->
            <div class="settings-card">
                <div class="card-header">
                    <h3>🎮 سلوك اللعبة</h3>
                </div>
                <div class="setting-group">
                    <div class="setting-item">
                        <span class="setting-label">سرعة نمو الأسماك</span>
                        <div class="setting-control">
                            <select id="growthSpeed">
                                <option value="slow">🐌 بطيء</option>
                                <option value="normal" selected>⚖️ عادي</option>
                                <option value="fast">🚀 سريع</option>
                            </select>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">عدوانية الأسماك</span>
                        <div class="setting-control">
                            <input type="range" id="fishAggression" min="0.1" max="2" step="0.1" value="1">
                            <span class="volume-display" id="aggressionValue">100%</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">كثافة الأسماك القصوى</span>
                        <div class="setting-control">
                            <input type="number" id="maxFish" min="50" max="300" value="150">
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">تفعيل المعارك التلقائية</span>
                        <div class="setting-control">
                            <input type="checkbox" id="autoBattles" checked>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Visual Settings -->
            <div class="settings-card">
                <div class="card-header">
                    <h3>🎨 الإعدادات البصرية</h3>
                </div>
                <div class="setting-group">
                    <div class="setting-item">
                        <span class="setting-label">جودة الرسوميات</span>
                        <div class="setting-control">
                            <select id="graphicsQuality">
                                <option value="low">📱 منخفضة</option>
                                <option value="medium" selected>💻 متوسطة</option>
                                <option value="high">🖥️ عالية</option>
                                <option value="ultra">🎮 فائقة</option>
                            </select>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">عدد الجزيئات</span>
                        <div class="setting-control">
                            <input type="range" id="particleCount" min="0.1" max="2" step="0.1" value="1">
                            <span class="volume-display" id="particleValue">100%</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">تأثيرات التوهج</span>
                        <div class="setting-control">
                            <input type="checkbox" id="glowEffects" checked>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">عرض أسماء اللاعبين</span>
                        <div class="setting-control">
                            <input type="checkbox" id="showPlayerNames" checked>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Camera Settings -->
            <div class="settings-card">
                <div class="card-header">
                    <h3>🔍 إعدادات الكاميرا والعرض</h3>
                    <p style="font-size: 12px; color: #888; margin: 5px 0 0 0;">💡 تحكم في مستوى التكبير والمتابعة التلقائية للأسماك الكبيرة</p>
                </div>
                <div class="setting-group">
                    <div class="setting-item">
                        <span class="setting-label">مستوى التكبير الافتراضي</span>
                        <div class="setting-control">
                            <select id="defaultZoom">
                                <option value="1.5">🔍- تكبير أقل (1.5x)</option>
                                <option value="2.0" selected>🔍 تكبير عادي (2.0x)</option>
                                <option value="2.5">🔍+ تكبير أكثر (2.5x)</option>
                                <option value="3.0">🔍++ تكبير عالي (3.0x)</option>
                                <option value="4.0">🔍+++ تكبير أقصى (4.0x)</option>
                            </select>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">متابعة الأسماك الكبيرة تلقائياً</span>
                        <div class="setting-control">
                            <input type="checkbox" id="autoFollowLargeFish" checked>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">حد المتابعة التلقائية (حجم السمكة)</span>
                        <div class="setting-control">
                            <input type="range" id="autoFollowThreshold" min="50" max="150" step="10" value="80">
                            <span class="volume-display" id="autoFollowValue">80</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">سرعة حركة الكاميرا</span>
                        <div class="setting-control">
                            <select id="cameraSpeed">
                                <option value="0.02">🐌 بطيئة جداً</option>
                                <option value="0.05">🚶 بطيئة</option>
                                <option value="0.08" selected>⚖️ عادية</option>
                                <option value="0.12">🏃 سريعة</option>
                                <option value="0.15">🚀 سريعة جداً</option>
                            </select>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">تحكم عجلة الماوس</span>
                        <div class="setting-control">
                            <input type="checkbox" id="mouseWheelZoom" checked>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">تحكم سريع في الكاميرا</span>
                        <div class="setting-control" style="display: flex; gap: 5px; flex-wrap: wrap;">
                            <button class="demo-btn" style="font-size: 10px; padding: 3px 6px;" onclick="if(window.game) window.game.setZoom(1.5)">🔍- أقل</button>
                            <button class="demo-btn" style="font-size: 10px; padding: 3px 6px;" onclick="if(window.game) window.game.setZoom(2.0)">🔍 عادي</button>
                            <button class="demo-btn" style="font-size: 10px; padding: 3px 6px;" onclick="if(window.game) window.game.setZoom(3.0)">🔍+ أكثر</button>
                            <button class="demo-btn" style="font-size: 10px; padding: 3px 6px;" onclick="if(window.game) window.game.followLargestFish()">👁️ متابعة</button>
                            <button class="demo-btn" style="font-size: 10px; padding: 3px 6px;" onclick="if(window.game) window.game.stopFollowing()">🎯 إيقاف</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Settings -->
            <div class="settings-card">
                <div class="card-header">
                    <h3>🔧 إعدادات متقدمة</h3>
                </div>
                <div class="setting-group">
                    <div class="setting-item">
                        <span class="setting-label">معدل الإطارات المستهدف</span>
                        <div class="setting-control">
                            <select id="targetFPS">
                                <option value="30">30 FPS</option>
                                <option value="60" selected>60 FPS</option>
                                <option value="120">120 FPS</option>
                            </select>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">تحسين الأداء</span>
                        <div class="setting-control">
                            <input type="checkbox" id="performanceMode">
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">حفظ تلقائي للإعدادات</span>
                        <div class="setting-control">
                            <input type="checkbox" id="autoSave" checked>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="btn btn-primary" onclick="openGame()">
                🎮 فتح اللعبة
            </button>
            <button class="btn btn-success" onclick="saveSettings()">
                💾 حفظ الإعدادات
            </button>
            <button class="btn btn-warning" onclick="loadSettings()">
                📂 تحميل الإعدادات
            </button>
            <button class="btn btn-info" onclick="exportAllData()">
                📤 تصدير جميع البيانات
            </button>
            <button class="btn btn-danger" onclick="resetSettings()">
                🔄 إعادة تعيين
            </button>
        </div>
    </div>

    <!-- Status Message -->
    <div id="statusMessage" class="status-message"></div>

    <script>
        // تهيئة نظام الصوت للمعاينة
        let audioContext;
        let soundBuffers = new Map();

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initAudioSystem();
            loadSettings();
            setupEventListeners();
            initializeAssignments();
            loadCustomSoundsInfo();
        });

        // تهيئة نظام الصوت
        async function initAudioSystem() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                await generateSounds();
                console.log('🔊 تم تهيئة نظام الصوت للمعاينة');
            } catch (error) {
                console.warn('⚠️ فشل في تهيئة نظام الصوت:', error);
            }
        }

        // توليد الأصوات للمعاينة
        async function generateSounds() {
            const sounds = {
                eat_small: generateRealisticEatingSound('small'),
                eat_large: generateRealisticEatingSound('large'),
                shark_roar: generateScarySound('shark'),
                whale_call: generateScarySound('whale'),
                spawn: generateSpawnSound(),
                ocean_waves: generateAmbientSound('waves'),
                underwater_bubbles: generateAmbientSound('bubbles'),
                deep_sea: generateAmbientSound('deep'),
                whale_song: generateAmbientSound('whale_song')
            };

            for (const [name, buffer] of Object.entries(sounds)) {
                soundBuffers.set(name, buffer);
            }
        }

        // توليد أصوات أكل واقعية
        function generateRealisticEatingSound(size) {
            const sampleRate = audioContext.sampleRate;
            const duration = size === 'large' ? 1.2 : 0.6;
            const buffer = audioContext.createBuffer(1, sampleRate * duration, sampleRate);
            const data = buffer.getChannelData(0);

            for (let i = 0; i < buffer.length; i++) {
                const t = i / sampleRate;
                let sound = 0;

                if (size === 'large') {
                    // صوت مضغ قوي وواقعي
                    sound += Math.sin(2 * Math.PI * 60 * t) * Math.exp(-t * 1.5) * 0.4;
                    sound += Math.sin(2 * Math.PI * 120 * t) * Math.exp(-t * 2) * 0.3;
                    // صوت تكسير العظام
                    if (t > 0.3 && t < 0.8) {
                        sound += (Math.random() - 0.5) * 0.2 * Math.exp(-(t - 0.5) * 8);
                    }
                } else {
                    // صوت مضغ خفيف
                    sound += Math.sin(2 * Math.PI * 200 * t) * Math.exp(-t * 4) * 0.3;
                    sound += (Math.random() - 0.5) * 0.1 * Math.exp(-t * 3);
                }

                data[i] = sound * 0.5;
            }

            return buffer;
        }

        // توليد أصوات مرعبة محسنة
        function generateScarySound(fishType) {
            const sampleRate = audioContext.sampleRate;
            const duration = 2.0;
            const buffer = audioContext.createBuffer(1, sampleRate * duration, sampleRate);
            const data = buffer.getChannelData(0);

            for (let i = 0; i < buffer.length; i++) {
                const t = i / sampleRate;
                let sound = 0;

                switch (fishType) {
                    case 'shark':
                        // زئير قرش مرعب
                        sound += Math.sin(2 * Math.PI * 40 * t) * Math.exp(-t * 0.6) * 0.6;
                        sound += Math.sin(2 * Math.PI * 80 * t) * Math.exp(-t * 1) * 0.4;
                        // إضافة تشويه للرعب
                        sound += Math.sin(2 * Math.PI * 25 * t) * Math.exp(-t * 0.8) * 0.3;
                        break;

                    case 'whale':
                        // نداء حوت عميق ومخيف
                        const freq = 20 + 10 * Math.sin(t * 3);
                        sound += Math.sin(2 * Math.PI * freq * t) * Math.exp(-t * 0.4) * 0.7;
                        sound += Math.sin(2 * Math.PI * freq * 2 * t) * Math.exp(-t * 0.6) * 0.3;
                        break;
                }

                data[i] = sound * 0.6;
            }

            return buffer;
        }

        // توليد أصوات المحيط
        function generateAmbientSound(type) {
            const sampleRate = audioContext.sampleRate;
            const duration = 3; // مدة أقصر للمعاينة
            const buffer = audioContext.createBuffer(1, sampleRate * duration, sampleRate);
            const data = buffer.getChannelData(0);

            for (let i = 0; i < buffer.length; i++) {
                const t = i / sampleRate;
                let sound = 0;

                switch (type) {
                    case 'waves':
                        // صوت أمواج البحر
                        const waveFreq = 0.3 + Math.sin(t * 0.1) * 0.1;
                        const waveNoise = (Math.random() - 0.5) * 0.4;
                        const wavePattern = Math.sin(2 * Math.PI * waveFreq * t) * 0.3;
                        sound = (waveNoise + wavePattern) * Math.sin(t * 0.5) * 0.6;
                        break;

                    case 'bubbles':
                        // صوت فقاعات تحت الماء
                        if (Math.random() < 0.05) {
                            const bubbleFreq = 800 + Math.random() * 1200;
                            const bubbleDecay = Math.exp(-t * 8);
                            sound += Math.sin(2 * Math.PI * bubbleFreq * t) * bubbleDecay * 0.2;
                        }
                        sound += (Math.random() - 0.5) * 0.1;
                        sound += Math.sin(2 * Math.PI * 60 * t) * 0.05;
                        break;

                    case 'deep':
                        // صوت أعماق البحر المخيف
                        const deepFreq = 20 + Math.sin(t * 0.2) * 10;
                        sound += Math.sin(2 * Math.PI * deepFreq * t) * 0.4;
                        if (Math.random() < 0.01) {
                            const mysteryFreq = 100 + Math.random() * 200;
                            sound += Math.sin(2 * Math.PI * mysteryFreq * t) * Math.exp(-t * 2) * 0.3;
                        }
                        sound += (Math.random() - 0.5) * 0.15;
                        break;

                    case 'whale_song':
                        // أغنية الحيتان البعيدة
                        const whaleFreq = 200 + Math.sin(t * 0.5) * 100;
                        sound += Math.sin(2 * Math.PI * whaleFreq * t) * Math.exp(-Math.abs(t - 1.5) * 0.5) * 0.4;
                        sound += Math.sin(2 * Math.PI * (whaleFreq * 1.5) * t) * Math.exp(-Math.abs(t - 2) * 0.3) * 0.2;
                        break;
                }

                data[i] = sound * 0.3;
            }

            return buffer;
        }

        // توليد صوت الظهور
        function generateSpawnSound() {
            const sampleRate = audioContext.sampleRate;
            const duration = 0.8;
            const buffer = audioContext.createBuffer(1, sampleRate * duration, sampleRate);
            const data = buffer.getChannelData(0);

            for (let i = 0; i < buffer.length; i++) {
                const t = i / sampleRate;
                const freq = 300 + t * 400;
                const sound = Math.sin(2 * Math.PI * freq * t) * Math.exp(-t * 2) * 0.4;
                data[i] = sound;
            }

            return buffer;
        }

        // معاينة الصوت
        function previewSound(soundName) {
            if (!audioContext || !soundBuffers.has(soundName)) return;

            try {
                const source = audioContext.createBufferSource();
                const gainNode = audioContext.createGain();

                source.buffer = soundBuffers.get(soundName);
                gainNode.gain.value = 0.5;

                source.connect(gainNode);
                gainNode.connect(audioContext.destination);

                source.start();

                showStatus('🔊 تم تشغيل المعاينة', 'info');
            } catch (error) {
                showStatus('❌ فشل في تشغيل المعاينة', 'error');
            }
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تحديث قيم المنزلقات
            document.getElementById('effectsVolume').addEventListener('input', function() {
                document.getElementById('effectsVolumeValue').textContent = Math.round(this.value * 100) + '%';
            });

            document.getElementById('musicVolume').addEventListener('input', function() {
                document.getElementById('musicVolumeValue').textContent = Math.round(this.value * 100) + '%';
            });

            document.getElementById('fishAggression').addEventListener('input', function() {
                document.getElementById('aggressionValue').textContent = Math.round(this.value * 100) + '%';
            });

            document.getElementById('particleCount').addEventListener('input', function() {
                document.getElementById('particleValue').textContent = Math.round(this.value * 100) + '%';
            });

            // إضافة مستمع لإعدادات الكاميرا
            const autoFollowThresholdSlider = document.getElementById('autoFollowThreshold');
            const autoFollowValue = document.getElementById('autoFollowValue');
            if (autoFollowThresholdSlider && autoFollowValue) {
                autoFollowThresholdSlider.addEventListener('input', function() {
                    autoFollowValue.textContent = this.value;
                });
            }

            // إضافة مستمع لأصوات المحيط (مع فحص الوجود)
            const ambientVolumeSlider = document.getElementById('ambientVolume');
            const ambientVolumeValue = document.getElementById('ambientVolumeValue');
            if (ambientVolumeSlider && ambientVolumeValue) {
                ambientVolumeSlider.addEventListener('input', function() {
                    ambientVolumeValue.textContent = Math.round(this.value * 100) + '%';
                });
            }

            // مستمعي أحداث إعدادات حذف الأسماك التلقائي
            const autoDeleteToggle = document.getElementById('autoDeleteEnabled');
            if (autoDeleteToggle) {
                autoDeleteToggle.addEventListener('change', function() {
                    updateAutoDeleteToggleText();
                    if (document.getElementById('autoSave').checked) {
                        saveSettings(false);
                    }
                });
            }

            const fishLifetimeInput = document.getElementById('fishLifetime');
            if (fishLifetimeInput) {
                fishLifetimeInput.addEventListener('input', function() {
                    if (document.getElementById('autoSave').checked) {
                        saveSettings(false);
                    }
                });
            }

            const maxFishLimitInput = document.getElementById('maxFishLimit');
            if (maxFishLimitInput) {
                maxFishLimitInput.addEventListener('input', function() {
                    if (document.getElementById('autoSave').checked) {
                        saveSettings(false);
                    }
                });
            }

            // مستمعي أحداث أزرار الإخفاء/الإظهار
            const toggles = ['showActivePlayersToggle', 'showGameStatsToggle', 'showControlsToggle', 'showAchievementsToggle'];
            toggles.forEach(toggleId => {
                const toggle = document.getElementById(toggleId);
                if (toggle) {
                    toggle.addEventListener('change', function() {
                        updateToggleTexts();
                        if (document.getElementById('autoSave').checked) {
                            saveSettings(false);
                        }
                    });
                }
            });

            // حفظ تلقائي عند التغيير
            const inputs = document.querySelectorAll('input, select');
            inputs.forEach(input => {
                input.addEventListener('change', function() {
                    if (document.getElementById('autoSave').checked) {
                        saveSettings(false);
                    }
                });
            });
        }

        // جمع جميع الإعدادات
        function getAllSettings() {
            return {
                // ربط الهدايا (مع فحص الوجود)
                giftToFishMapping: {
                    'Rose': document.getElementById('giftRose') ? document.getElementById('giftRose').value : 'random',
                    'Diamond': document.getElementById('giftDiamond') ? document.getElementById('giftDiamond').value : 'shark',
                    'Crown': document.getElementById('giftCrown') ? document.getElementById('giftCrown').value : 'whale',
                    'Lightning': document.getElementById('giftLightning') ? document.getElementById('giftLightning').value : 'eel',
                    'Butterfly': document.getElementById('giftButterfly') ? document.getElementById('giftButterfly').value : 'ray',
                    'Star': document.getElementById('giftStar') ? document.getElementById('giftStar').value : 'salmon'
                },

                // إعدادات الصوت (مع فحص الوجود)
                soundEnabled: document.getElementById('soundEnabled') ? document.getElementById('soundEnabled').checked : true,
                musicEnabled: document.getElementById('musicEnabled') ? document.getElementById('musicEnabled').checked : true,
                effectsVolume: document.getElementById('effectsVolume') ? parseFloat(document.getElementById('effectsVolume').value) : 0.7,
                musicVolume: document.getElementById('musicVolume') ? parseFloat(document.getElementById('musicVolume').value) : 0.3,
                ambientSounds: document.getElementById('ambientSounds') ? document.getElementById('ambientSounds').checked : true,
                ambientVolume: document.getElementById('ambientVolume') ? parseFloat(document.getElementById('ambientVolume').value) : 0.3,
                scaryEnabled: document.getElementById('scaryEnabled') ? document.getElementById('scaryEnabled').checked : true,
                realisticEating: document.getElementById('realisticEating') ? document.getElementById('realisticEating').checked : true,

                // سلوك اللعبة (مع فحص الوجود)
                growthSpeed: document.getElementById('growthSpeed') ? document.getElementById('growthSpeed').value : 'normal',
                fishAggression: document.getElementById('fishAggression') ? parseFloat(document.getElementById('fishAggression').value) : 1,
                maxFish: document.getElementById('maxFish') ? parseInt(document.getElementById('maxFish').value) : 150,
                autoBattles: document.getElementById('autoBattles') ? document.getElementById('autoBattles').checked : true,

                // الإعدادات البصرية (مع فحص الوجود)
                graphicsQuality: document.getElementById('graphicsQuality') ? document.getElementById('graphicsQuality').value : 'medium',
                particleCount: document.getElementById('particleCount') ? parseFloat(document.getElementById('particleCount').value) : 1,
                glowEffects: document.getElementById('glowEffects') ? document.getElementById('glowEffects').checked : true,
                showPlayerNames: document.getElementById('showPlayerNames') ? document.getElementById('showPlayerNames').checked : true,

                // إعدادات متقدمة (مع فحص الوجود)
                targetFPS: document.getElementById('targetFPS') ? document.getElementById('targetFPS').value : '60',
                performanceMode: document.getElementById('performanceMode') ? document.getElementById('performanceMode').checked : false,
                autoSave: document.getElementById('autoSave') ? document.getElementById('autoSave').checked : true,

                // إعدادات حذف الأسماك التلقائي (مع فحص الوجود)
                autoDeleteEnabled: document.getElementById('autoDeleteEnabled') ? document.getElementById('autoDeleteEnabled').checked : false,
                fishLifetime: document.getElementById('fishLifetime') ? parseInt(document.getElementById('fishLifetime').value) : 60,
                maxFishLimit: document.getElementById('maxFishLimit') ? parseInt(document.getElementById('maxFishLimit').value) : 100,

                // إعدادات إخفاء/إظهار القوائم (مع فحص الوجود) - الوضع الافتراضي مخفي
                showActivePlayers: document.getElementById('showActivePlayersToggle') ? document.getElementById('showActivePlayersToggle').checked : false,
                showGameStats: document.getElementById('showGameStatsToggle') ? document.getElementById('showGameStatsToggle').checked : false,
                showControls: document.getElementById('showControlsToggle') ? document.getElementById('showControlsToggle').checked : false,
                showAchievements: document.getElementById('showAchievementsToggle') ? document.getElementById('showAchievementsToggle').checked : false,

                // إعدادات الكاميرا (مع فحص الوجود)
                defaultZoom: document.getElementById('defaultZoom') ? parseFloat(document.getElementById('defaultZoom').value) : 2.0,
                autoFollowLargeFish: document.getElementById('autoFollowLargeFish') ? document.getElementById('autoFollowLargeFish').checked : true,
                autoFollowThreshold: document.getElementById('autoFollowThreshold') ? parseInt(document.getElementById('autoFollowThreshold').value) : 80,
                cameraSpeed: document.getElementById('cameraSpeed') ? parseFloat(document.getElementById('cameraSpeed').value) : 0.08,
                mouseWheelZoom: document.getElementById('mouseWheelZoom') ? document.getElementById('mouseWheelZoom').checked : true
            };
        }

        // تطبيق الإعدادات على الواجهة
        function applySettings(settings) {
            // ربط الهدايا (مع فحص الوجود)
            if (settings.giftToFishMapping) {
                if (document.getElementById('giftRose')) {
                    document.getElementById('giftRose').value = settings.giftToFishMapping.Rose || 'random';
                }
                if (document.getElementById('giftDiamond')) {
                    document.getElementById('giftDiamond').value = settings.giftToFishMapping.Diamond || 'shark';
                }
                if (document.getElementById('giftCrown')) {
                    document.getElementById('giftCrown').value = settings.giftToFishMapping.Crown || 'whale';
                }
                if (document.getElementById('giftLightning')) {
                    document.getElementById('giftLightning').value = settings.giftToFishMapping.Lightning || 'eel';
                }
                if (document.getElementById('giftButterfly')) {
                    document.getElementById('giftButterfly').value = settings.giftToFishMapping.Butterfly || 'ray';
                }
                if (document.getElementById('giftStar')) {
                    document.getElementById('giftStar').value = settings.giftToFishMapping.Star || 'salmon';
                }
            }

            // إعدادات الصوت (مع فحص الوجود)
            if (document.getElementById('soundEnabled')) {
                document.getElementById('soundEnabled').checked = settings.soundEnabled !== false;
            }
            if (document.getElementById('musicEnabled')) {
                document.getElementById('musicEnabled').checked = settings.musicEnabled !== false;
            }
            if (document.getElementById('effectsVolume')) {
                document.getElementById('effectsVolume').value = settings.effectsVolume || 0.7;
            }
            if (document.getElementById('musicVolume')) {
                document.getElementById('musicVolume').value = settings.musicVolume || 0.3;
            }

            // إعدادات أصوات المحيط (مع فحص الوجود)
            if (document.getElementById('ambientSounds')) {
                document.getElementById('ambientSounds').checked = settings.ambientSounds !== false;
            }
            if (document.getElementById('ambientVolume')) {
                document.getElementById('ambientVolume').value = settings.ambientVolume || 0.3;
            }

            if (document.getElementById('scaryEnabled')) {
                document.getElementById('scaryEnabled').checked = settings.scaryEnabled !== false;
            }
            if (document.getElementById('realisticEating')) {
                document.getElementById('realisticEating').checked = settings.realisticEating !== false;
            }

            // سلوك اللعبة (مع فحص الوجود)
            if (document.getElementById('growthSpeed')) {
                document.getElementById('growthSpeed').value = settings.growthSpeed || 'normal';
            }
            if (document.getElementById('fishAggression')) {
                document.getElementById('fishAggression').value = settings.fishAggression || 1;
            }
            if (document.getElementById('maxFish')) {
                document.getElementById('maxFish').value = settings.maxFish || 150;
            }
            if (document.getElementById('autoBattles')) {
                document.getElementById('autoBattles').checked = settings.autoBattles !== false;
            }

            // الإعدادات البصرية (مع فحص الوجود)
            if (document.getElementById('graphicsQuality')) {
                document.getElementById('graphicsQuality').value = settings.graphicsQuality || 'medium';
            }
            if (document.getElementById('particleCount')) {
                document.getElementById('particleCount').value = settings.particleCount || 1;
            }
            if (document.getElementById('glowEffects')) {
                document.getElementById('glowEffects').checked = settings.glowEffects !== false;
            }
            if (document.getElementById('showPlayerNames')) {
                document.getElementById('showPlayerNames').checked = settings.showPlayerNames !== false;
            }

            // إعدادات متقدمة (مع فحص الوجود)
            if (document.getElementById('targetFPS')) {
                document.getElementById('targetFPS').value = settings.targetFPS || '60';
            }
            if (document.getElementById('performanceMode')) {
                document.getElementById('performanceMode').checked = settings.performanceMode || false;
            }
            if (document.getElementById('autoSave')) {
                document.getElementById('autoSave').checked = settings.autoSave !== false;
            }

            // إعدادات حذف الأسماك التلقائي (مع فحص الوجود)
            if (document.getElementById('autoDeleteEnabled')) {
                document.getElementById('autoDeleteEnabled').checked = settings.autoDeleteEnabled === true;
            }
            if (document.getElementById('fishLifetime')) {
                document.getElementById('fishLifetime').value = settings.fishLifetime || 60;
            }
            if (document.getElementById('maxFishLimit')) {
                document.getElementById('maxFishLimit').value = settings.maxFishLimit || 100;
            }

            // إعدادات إخفاء/إظهار القوائم (مع فحص الوجود) - الوضع الافتراضي مخفي
            if (document.getElementById('showActivePlayersToggle')) {
                document.getElementById('showActivePlayersToggle').checked = settings.showActivePlayers === true;
            }
            if (document.getElementById('showGameStatsToggle')) {
                document.getElementById('showGameStatsToggle').checked = settings.showGameStats === true;
            }
            if (document.getElementById('showControlsToggle')) {
                document.getElementById('showControlsToggle').checked = settings.showControls === true;
            }
            if (document.getElementById('showAchievementsToggle')) {
                document.getElementById('showAchievementsToggle').checked = settings.showAchievements === true;
            }

            // إعدادات الكاميرا (مع فحص الوجود)
            if (document.getElementById('defaultZoom')) {
                document.getElementById('defaultZoom').value = settings.defaultZoom || 2.0;
            }
            if (document.getElementById('autoFollowLargeFish')) {
                document.getElementById('autoFollowLargeFish').checked = settings.autoFollowLargeFish !== false;
            }
            if (document.getElementById('autoFollowThreshold')) {
                document.getElementById('autoFollowThreshold').value = settings.autoFollowThreshold || 80;
            }
            if (document.getElementById('cameraSpeed')) {
                document.getElementById('cameraSpeed').value = settings.cameraSpeed || 0.08;
            }
            if (document.getElementById('mouseWheelZoom')) {
                document.getElementById('mouseWheelZoom').checked = settings.mouseWheelZoom !== false;
            }

            // تحديث عرض القيم (مع فحص الوجود)
            if (document.getElementById('effectsVolumeValue')) {
                document.getElementById('effectsVolumeValue').textContent = Math.round((settings.effectsVolume || 0.7) * 100) + '%';
            }
            if (document.getElementById('musicVolumeValue')) {
                document.getElementById('musicVolumeValue').textContent = Math.round((settings.musicVolume || 0.3) * 100) + '%';
            }

            // تحديث عرض قيمة أصوات المحيط (مع فحص الوجود)
            if (document.getElementById('ambientVolumeValue')) {
                document.getElementById('ambientVolumeValue').textContent = Math.round((settings.ambientVolume || 0.3) * 100) + '%';
            }

            if (document.getElementById('aggressionValue')) {
                document.getElementById('aggressionValue').textContent = Math.round((settings.fishAggression || 1) * 100) + '%';
            }
            if (document.getElementById('particleValue')) {
                document.getElementById('particleValue').textContent = Math.round((settings.particleCount || 1) * 100) + '%';
            }

            // تحديث عرض قيمة حد المتابعة التلقائية (مع فحص الوجود)
            if (document.getElementById('autoFollowValue')) {
                document.getElementById('autoFollowValue').textContent = settings.autoFollowThreshold || 80;
            }

            // تحديث نصوص أزرار الإخفاء/الإظهار
            updateToggleTexts();
            updateAutoDeleteToggleText();
        }

        // تحديث نص زر الحذف التلقائي
        function updateAutoDeleteToggleText() {
            const autoDeleteToggle = document.getElementById('autoDeleteEnabled');
            const autoDeleteText = autoDeleteToggle ? autoDeleteToggle.nextElementSibling : null;
            if (autoDeleteText) {
                autoDeleteText.textContent = autoDeleteToggle.checked ? 'مفعل' : 'معطل';
            }
        }

        // تحديث نصوص أزرار الإخفاء/الإظهار
        function updateToggleTexts() {
            // تحديث نص قائمة اللاعبين النشطين
            const activePlayersToggle = document.getElementById('showActivePlayersToggle');
            const activePlayersText = activePlayersToggle ? activePlayersToggle.nextElementSibling : null;
            if (activePlayersText) {
                activePlayersText.textContent = activePlayersToggle.checked ? 'إظهار' : 'إخفاء';
            }

            // تحديث نص إحصائيات اللعبة
            const gameStatsToggle = document.getElementById('showGameStatsToggle');
            const gameStatsText = gameStatsToggle ? gameStatsToggle.nextElementSibling : null;
            if (gameStatsText) {
                gameStatsText.textContent = gameStatsToggle.checked ? 'إظهار' : 'إخفاء';
            }

            // تحديث نص قائمة التحكم
            const controlsToggle = document.getElementById('showControlsToggle');
            const controlsText = controlsToggle ? controlsToggle.nextElementSibling : null;
            if (controlsText) {
                controlsText.textContent = controlsToggle.checked ? 'إظهار' : 'إخفاء';
            }

            // تحديث نص قائمة الإنجازات
            const achievementsToggle = document.getElementById('showAchievementsToggle');
            const achievementsText = achievementsToggle ? achievementsToggle.nextElementSibling : null;
            if (achievementsText) {
                achievementsText.textContent = achievementsToggle.checked ? 'إظهار' : 'إخفاء';
            }
        }

        // حفظ الإعدادات
        function saveSettings(showMessage = true) {
            try {
                const settings = getAllSettings();
                localStorage.setItem('fishEatFishSettings', JSON.stringify(settings));

                if (showMessage) {
                    showStatus('✅ تم حفظ الإعدادات بنجاح!', 'success');
                }

                console.log('💾 تم حفظ إعدادات اللعبة');
            } catch (error) {
                showStatus('❌ فشل في حفظ الإعدادات', 'error');
                console.error('فشل في حفظ الإعدادات:', error);
            }
        }

        // تحميل الإعدادات
        function loadSettings() {
            try {
                const savedSettings = localStorage.getItem('fishEatFishSettings');
                if (savedSettings) {
                    const settings = JSON.parse(savedSettings);
                    applySettings(settings);
                    showStatus('📂 تم تحميل الإعدادات', 'info');
                    console.log('📂 تم تحميل إعدادات اللعبة');
                }
            } catch (error) {
                showStatus('❌ فشل في تحميل الإعدادات', 'error');
                console.error('فشل في تحميل الإعدادات:', error);
            }
        }

        // إعادة تعيين الإعدادات
        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
                const defaultSettings = {
                    giftToFishMapping: {
                        'Rose': 'random',
                        'Diamond': 'shark',
                        'Crown': 'whale',
                        'Lightning': 'eel',
                        'Butterfly': 'ray',
                        'Star': 'salmon'
                    },
                    soundEnabled: true,
                    musicEnabled: true,
                    effectsVolume: 0.7,
                    musicVolume: 0.3,
                    ambientSounds: true,
                    ambientVolume: 0.3,
                    scaryEnabled: true,
                    realisticEating: true,
                    growthSpeed: 'normal',
                    fishAggression: 1,
                    maxFish: 150,
                    autoBattles: true,
                    graphicsQuality: 'medium',
                    particleCount: 1,
                    glowEffects: true,
                    showPlayerNames: true,
                    targetFPS: '60',
                    performanceMode: false,
                    autoSave: true,

                    // إعدادات الكاميرا الافتراضية
                    defaultZoom: 2.0,
                    autoFollowLargeFish: true,
                    autoFollowThreshold: 80,
                    cameraSpeed: 0.08,
                    mouseWheelZoom: true
                };

                applySettings(defaultSettings);
                localStorage.setItem('fishEatFishSettings', JSON.stringify(defaultSettings));
                showStatus('🔄 تم إعادة تعيين الإعدادات!', 'success');
                console.log('🔄 تم إعادة تعيين إعدادات اللعبة');
            }
        }

        // فتح اللعبة
        function openGame() {
            // حفظ الإعدادات قبل فتح اللعبة
            saveSettings(false);

            // فتح اللعبة في نافذة جديدة
            window.open('/games/fish-eat-fish.html', '_blank');
            showStatus('🎮 تم فتح اللعبة في نافذة جديدة', 'info');
        }

        // عرض رسالة الحالة
        function showStatus(message, type) {
            const statusElement = document.getElementById('statusMessage');
            statusElement.textContent = message;
            statusElement.className = `status-message status-${type} show`;

            setTimeout(() => {
                statusElement.classList.remove('show');
            }, 3000);
        }

        // نظام التعيينات
        let assignments = [];

        function initializeAssignments() {
            // تحميل التعيينات المحفوظة أولاً
            const savedAssignments = loadAssignments();

            if (savedAssignments && savedAssignments.length > 0) {
                assignments = savedAssignments;
                console.log('📂 تم تحميل التعيينات المحفوظة:', assignments.length);
            } else {
                // التعيينات الافتراضية الموسعة
                const defaultAssignments = [
                    // الهدايا الأساسية
                    { id: 'rose_1', giftName: 'Rose', eventType: 'gift', creatureType: 'random', minCount: 1, maxCount: 5, sizeMultiplier: 1, specialEffect: 'none', soundEffect: 'spawn' },
                    { id: 'rose_5', giftName: 'Rose', eventType: 'gift', creatureType: 'salmon', minCount: 5, maxCount: 10, sizeMultiplier: 1.2, specialEffect: 'speed_boost', soundEffect: 'spawn' },
                    { id: 'rose_10', giftName: 'Rose', eventType: 'gift', creatureType: 'shark', minCount: 10, maxCount: 999, sizeMultiplier: 1.5, specialEffect: 'wild_mode', soundEffect: 'shark_roar' },

                    { id: 'diamond_1', giftName: 'Diamond', eventType: 'gift', creatureType: 'shark', minCount: 1, maxCount: 999, sizeMultiplier: 1.8, specialEffect: 'wild_mode', soundEffect: 'shark_roar' },
                    { id: 'crown_1', giftName: 'Crown', eventType: 'gift', creatureType: 'whale', minCount: 1, maxCount: 999, sizeMultiplier: 2.2, specialEffect: 'size_boost', soundEffect: 'whale_call' },
                    { id: 'lightning_1', giftName: 'Lightning', eventType: 'gift', creatureType: 'eel', minCount: 1, maxCount: 999, sizeMultiplier: 1.3, specialEffect: 'electric', soundEffect: 'spawn' },
                    { id: 'butterfly_1', giftName: 'Butterfly', eventType: 'gift', creatureType: 'jellyfish', minCount: 1, maxCount: 999, sizeMultiplier: 1.1, specialEffect: 'poison', soundEffect: 'spawn' },
                    { id: 'star_1', giftName: 'Star', eventType: 'gift', creatureType: 'starfish', minCount: 1, maxCount: 999, sizeMultiplier: 1.2, specialEffect: 'regeneration', soundEffect: 'spawn' },

                    // الأحداث التفاعلية
                    { id: 'join_1', giftName: 'Join', eventType: 'join', creatureType: 'normal', minCount: 1, maxCount: 1, sizeMultiplier: 0.9, specialEffect: 'welcome_glow', soundEffect: 'spawn' },
                    { id: 'like_1', giftName: 'Like', eventType: 'like', creatureType: 'seahorse', minCount: 1, maxCount: 10, sizeMultiplier: 0.7, specialEffect: 'speed_boost', soundEffect: 'spawn' },
                    { id: 'like_10', giftName: 'Like', eventType: 'like', creatureType: 'dolphin', minCount: 10, maxCount: 50, sizeMultiplier: 1.1, specialEffect: 'speed_boost', soundEffect: 'spawn' },
                    { id: 'like_50', giftName: 'Like', eventType: 'like', creatureType: 'ray', minCount: 50, maxCount: 999, sizeMultiplier: 1.4, specialEffect: 'electric', soundEffect: 'spawn' },

                    { id: 'follow_1', giftName: 'Follow', eventType: 'follow', creatureType: 'dolphin', minCount: 1, maxCount: 1, sizeMultiplier: 1.5, specialEffect: 'loyalty_boost', soundEffect: 'spawn' },
                    { id: 'share_1', giftName: 'Share', eventType: 'share', creatureType: 'turtle', minCount: 1, maxCount: 1, sizeMultiplier: 1.7, specialEffect: 'shell_defense', soundEffect: 'spawn' },

                    // تعيينات التعليقات
                    { id: 'comment_fish', giftName: 'fish', eventType: 'comment', creatureType: 'random', minCount: 1, maxCount: 1, sizeMultiplier: 1, specialEffect: 'none', soundEffect: 'spawn' },
                    { id: 'comment_shark', giftName: 'shark', eventType: 'comment', creatureType: 'shark', minCount: 1, maxCount: 1, sizeMultiplier: 1.3, specialEffect: 'wild_mode', soundEffect: 'shark_roar' },
                    { id: 'comment_whale', giftName: 'whale', eventType: 'comment', creatureType: 'whale', minCount: 1, maxCount: 1, sizeMultiplier: 1.8, specialEffect: 'size_boost', soundEffect: 'whale_call' },
                    { id: 'comment_battle', giftName: 'battle', eventType: 'comment', creatureType: 'random', minCount: 1, maxCount: 1, sizeMultiplier: 1.2, specialEffect: 'battle_mode', soundEffect: 'spawn' }
                ];

                assignments = defaultAssignments;
                saveAssignments(); // حفظ التعيينات الافتراضية
                console.log('✨ تم إنشاء التعيينات الافتراضية الموسعة');
            }

            renderAssignments();
        }

        function addNewAssignment() {
            const newAssignment = {
                id: 'custom_' + Date.now(),
                giftName: '',
                eventType: 'gift',
                creatureType: 'random',
                minCount: 1,
                maxCount: 999,
                sizeMultiplier: 1,
                specialEffect: 'none',
                soundEffect: 'spawn'
            };

            assignments.push(newAssignment);
            renderAssignments();
            saveAssignments(); // حفظ التعيينات
            showStatus('✅ تم إضافة تعيين جديد', 'success');
        }

        function removeAssignment(button) {
            const assignmentItem = button.closest('.assignment-item');
            const index = Array.from(assignmentItem.parentNode.children).indexOf(assignmentItem);

            assignments.splice(index, 1);
            renderAssignments();
            saveAssignments(); // حفظ التعيينات
            showStatus('🗑️ تم حذف التعيين', 'info');
        }

        // حفظ التعيينات في localStorage
        function saveAssignments() {
            try {
                localStorage.setItem('fishGameAssignments', JSON.stringify(assignments));
                console.log('💾 تم حفظ التعيينات:', assignments.length);
            } catch (error) {
                console.error('❌ فشل في حفظ التعيينات:', error);
                showStatus('❌ فشل في حفظ التعيينات', 'error');
            }
        }

        // تحميل التعيينات من localStorage
        function loadAssignments() {
            try {
                const savedData = localStorage.getItem('fishGameAssignments');
                if (savedData) {
                    const loadedAssignments = JSON.parse(savedData);
                    console.log('📂 تم تحميل التعيينات من التخزين:', loadedAssignments.length);
                    return loadedAssignments;
                }
            } catch (error) {
                console.error('❌ فشل في تحميل التعيينات:', error);
            }
            return null;
        }

        function renderAssignments() {
            const container = document.getElementById('assignmentsList');
            container.innerHTML = '';

            assignments.forEach((assignment, index) => {
                const assignmentElement = createAssignmentElement(assignment, index);
                container.appendChild(assignmentElement);
            });
        }

        function createAssignmentElement(assignment, index) {
            // إنشاء عنصر HTML للتعيين
            const element = document.createElement('div');
            element.className = 'assignment-item';
            element.innerHTML = `
                <div class="assignment-header">
                    <span class="assignment-id">#${assignment.id || index}</span>
                    <button type="button" class="remove-assignment-btn" onclick="removeAssignment(this)">🗑️</button>
                </div>
                <div class="assignment-fields">
                    <div class="field-group">
                        <label>نوع الحدث:</label>
                        <select class="event-type">
                            <option value="gift">🎁 هدية</option>
                            <option value="like">👍 إعجاب</option>
                            <option value="comment">💬 تعليق</option>
                            <option value="follow">➕ متابعة</option>
                            <option value="share">📤 مشاركة</option>
                            <option value="join">👋 انضمام</option>
                        </select>
                    </div>
                    <div class="field-group">
                        <label>اسم الهدية/الكلمة:</label>
                        <input type="text" class="gift-name-input" placeholder="Rose, Diamond, fish, shark...">
                    </div>
                    <div class="field-group">
                        <label>نوع المخلوق:</label>
                        <select class="creature-type">
                            <option value="random">🎲 عشوائي</option>
                            <option value="normal">🐟 سمكة عادية</option>
                            <option value="shark">🦈 قرش</option>
                            <option value="whale">🐋 حوت</option>
                            <option value="dolphin">🐬 دولفين</option>
                            <option value="eel">🐍 أنقليس</option>
                            <option value="ray">🗲 راي</option>
                            <option value="salmon">🐟 سلمون</option>
                            <option value="jellyfish">🎐 قنديل البحر</option>
                            <option value="octopus">🐙 أخطبوط</option>
                            <option value="seahorse">🐴 حصان البحر</option>
                            <option value="turtle">🐢 سلحفاة</option>
                            <option value="crab">🦀 سرطان</option>
                            <option value="lobster">🦞 كركند</option>
                            <option value="starfish">⭐ نجمة البحر</option>
                            <option value="anglerfish">🎣 سمكة الصياد</option>
                            <option value="swordfish">⚔️ سمكة السيف</option>
                            <option value="hammerhead">🔨 القرش المطرقة</option>
                        </select>
                    </div>
                    <div class="field-group">
                        <label>العدد الأدنى:</label>
                        <input type="number" class="min-count" min="1" value="1">
                    </div>
                    <div class="field-group">
                        <label>العدد الأقصى:</label>
                        <input type="number" class="max-count" min="1" value="999">
                    </div>
                    <div class="field-group">
                        <label>مضاعف الحجم:</label>
                        <input type="number" class="size-multiplier" min="0.1" max="5" step="0.1" value="1">
                    </div>
                    <div class="field-group">
                        <label>التأثير الخاص:</label>
                        <select class="special-effect">
                            <option value="none">بدون تأثير</option>
                            <option value="speed_boost">تسريع</option>
                            <option value="size_boost">زيادة حجم</option>
                            <option value="wild_mode">وضع شراسة</option>
                            <option value="electric">كهربائي</option>
                            <option value="poison">سام</option>
                            <option value="regeneration">تجديد</option>
                            <option value="shell_defense">دفاع الصدفة</option>
                            <option value="welcome_glow">توهج ترحيبي</option>
                            <option value="loyalty_boost">تعزيز الولاء</option>
                            <option value="battle_mode">وضع المعركة</option>
                        </select>
                    </div>
                    <div class="field-group">
                        <label>تأثير صوتي:</label>
                        <select class="sound-effect">
                            <option value="spawn">ظهور</option>
                            <option value="shark_roar">زئير قرش</option>
                            <option value="whale_call">نداء حوت</option>
                            <option value="eat_small">أكل صغير</option>
                            <option value="eat_large">أكل كبير</option>
                        </select>
                    </div>
                </div>
            `;

            // تعبئة القيم
            element.querySelector('.event-type').value = assignment.eventType || 'gift';
            element.querySelector('.gift-name-input').value = assignment.giftName || '';
            element.querySelector('.creature-type').value = assignment.creatureType || 'random';
            element.querySelector('.min-count').value = assignment.minCount || 1;
            element.querySelector('.max-count').value = assignment.maxCount || 999;
            element.querySelector('.size-multiplier').value = assignment.sizeMultiplier || 1;
            element.querySelector('.special-effect').value = assignment.specialEffect || 'none';
            element.querySelector('.sound-effect').value = assignment.soundEffect || 'spawn';

            // إضافة مستمعي الأحداث مع الحفظ التلقائي
            element.querySelector('.event-type').addEventListener('change', function() {
                assignments[index].eventType = this.value;
                saveAssignments();
            });

            element.querySelector('.gift-name-input').addEventListener('input', function() {
                assignments[index].giftName = this.value;
                saveAssignments();
            });

            element.querySelector('.creature-type').addEventListener('change', function() {
                assignments[index].creatureType = this.value;
                saveAssignments();
            });

            element.querySelector('.min-count').addEventListener('input', function() {
                assignments[index].minCount = parseInt(this.value) || 1;
                saveAssignments();
            });

            element.querySelector('.max-count').addEventListener('input', function() {
                assignments[index].maxCount = parseInt(this.value) || 999;
                saveAssignments();
            });

            element.querySelector('.size-multiplier').addEventListener('input', function() {
                assignments[index].sizeMultiplier = parseFloat(this.value) || 1;
                saveAssignments();
            });

            element.querySelector('.special-effect').addEventListener('change', function() {
                assignments[index].specialEffect = this.value;
                saveAssignments();
            });

            element.querySelector('.sound-effect').addEventListener('change', function() {
                assignments[index].soundEffect = this.value;
                saveAssignments();
            });

            return element;
        }

        function getAssignmentForGift(giftName, giftCount) {
            // البحث عن تعيين مطابق
            const assignment = assignments.find(a =>
                a.giftName.toLowerCase() === giftName.toLowerCase() &&
                giftCount >= a.minCount
            );

            if (assignment) {
                return {
                    creatureType: assignment.creatureType,
                    sizeMultiplier: assignment.sizeMultiplier,
                    specialEffect: assignment.specialEffect
                };
            }

            // إرجاع التعيين الافتراضي
            return {
                creatureType: 'random',
                sizeMultiplier: 1,
                specialEffect: 'none'
            };
        }

        // تصدير الوظيفة للاستخدام العام
        window.getAssignmentForGift = getAssignmentForGift;

        // معاينة الأصوات
        function previewSound(soundName) {
            if (!audioContext || !soundBuffers.has(soundName)) {
                console.warn('الصوت غير متاح:', soundName);
                return;
            }

            try {
                const source = audioContext.createBufferSource();
                const gainNode = audioContext.createGain();

                source.buffer = soundBuffers.get(soundName);
                gainNode.gain.value = 0.5;

                source.connect(gainNode);
                gainNode.connect(audioContext.destination);

                source.start();
                console.log('🔊 تشغيل معاينة:', soundName);
            } catch (error) {
                console.error('خطأ في تشغيل الصوت:', error);
            }
        }

        // متغيرات الأصوات المخصصة
        let customSounds = new Map();

        // رفع ملف صوتي مخصص
        async function uploadCustomSound(soundName, fileInput) {
            const file = fileInput.files[0];
            if (!file) return;

            // التحقق من نوع الملف
            const allowedTypes = ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a', 'audio/mpeg'];
            if (!allowedTypes.includes(file.type)) {
                showUploadStatus(soundName, 'error', 'نوع الملف غير مدعوم. استخدم MP3, WAV, OGG, أو M4A');
                fileInput.value = '';
                return;
            }

            // التحقق من حجم الملف (10MB)
            const maxSize = 10 * 1024 * 1024;
            if (file.size > maxSize) {
                showUploadStatus(soundName, 'error', 'حجم الملف كبير جداً. الحد الأقصى 10MB');
                fileInput.value = '';
                return;
            }

            showUploadStatus(soundName, 'loading', 'جاري رفع الملف...');

            try {
                // استخدام FileReader لقراءة الملف بطريقة آمنة
                const fileReader = new FileReader();

                const arrayBuffer = await new Promise((resolve, reject) => {
                    fileReader.onload = () => resolve(fileReader.result);
                    fileReader.onerror = () => reject(fileReader.error);
                    fileReader.readAsArrayBuffer(file);
                });

                // فك تشفير الصوت
                const audioBuffer = await audioContext.decodeAudioData(arrayBuffer.slice());

                // حفظ الصوت المخصص
                customSounds.set(soundName, {
                    buffer: audioBuffer,
                    fileName: file.name,
                    size: file.size
                });

                // حفظ في قائمة الأصوات المخصصة المنفصلة
                if (!window.customSoundBuffers) {
                    window.customSoundBuffers = new Map();
                }
                window.customSoundBuffers.set(soundName, audioBuffer);

                // أيضاً في النظام العادي للمعاينة
                soundBuffers.set(soundName, audioBuffer);

                // حفظ البيانات الصوتية في localStorage بطريقة محسنة ومضمونة
                try {
                    // التحقق من مساحة التخزين المتاحة
                    const testKey = `fishGameCustomSound_${soundName}_test`;

                    // استخدام FileReader لتحويل إلى Base64 مباشرة
                    const base64Reader = new FileReader();
                    const base64Data = await new Promise((resolve, reject) => {
                        base64Reader.onload = () => {
                            try {
                                // إزالة البادئة data:audio/...;base64,
                                const result = base64Reader.result;
                                const base64 = result.split(',')[1];

                                // اختبار الحفظ أولاً
                                localStorage.setItem(testKey, base64);
                                localStorage.removeItem(testKey);

                                resolve(base64);
                            } catch (error) {
                                reject(new Error('فشل في معالجة البيانات الصوتية'));
                            }
                        };
                        base64Reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
                        base64Reader.readAsDataURL(file);
                    });

                    // حفظ البيانات الصوتية
                    localStorage.setItem(`fishGameCustomSound_${soundName}`, base64Data);

                    // التحقق من نجاح الحفظ
                    const savedData = localStorage.getItem(`fishGameCustomSound_${soundName}`);
                    if (!savedData || savedData !== base64Data) {
                        throw new Error('فشل في التحقق من حفظ البيانات');
                    }

                    console.log(`💾 تم حفظ البيانات الصوتية بنجاح: ${soundName} (${(base64Data.length / 1024).toFixed(1)} KB)`);

                } catch (storageError) {
                    console.error('فشل في حفظ البيانات الصوتية:', storageError);
                    showUploadStatus(soundName, 'error', 'فشل في حفظ الملف. جرب ملف أصغر.');

                    // تنظيف البيانات الجزئية
                    localStorage.removeItem(`fishGameCustomSound_${soundName}`);
                    customSounds.delete(soundName);
                    soundBuffers.delete(soundName);

                    return; // إيقاف العملية
                }

                // تحديث واجهة المستخدم
                updateUploadUI(soundName, true);
                showUploadStatus(soundName, 'success', `تم رفع ${file.name} بنجاح`);

                // حفظ معلومات الملف
                saveCustomSounds();

                // إشعار اللعبة بالتحديث
                notifyGameUpdate();

                console.log(`✅ تم رفع صوت مخصص: ${soundName} - ${file.name}`);

            } catch (error) {
                console.error('خطأ في رفع الملف:', error);
                showUploadStatus(soundName, 'error', 'فشل في معالجة الملف الصوتي');
                fileInput.value = '';
            }
        }

        // إزالة صوت مخصص
        function removeCustomSound(soundName) {
            if (customSounds.has(soundName)) {
                // إزالة الصوت المخصص
                customSounds.delete(soundName);

                // حذف البيانات الصوتية من localStorage
                localStorage.removeItem(`fishGameCustomSound_${soundName}`);

                // حذف من قائمة الأصوات المخصصة
                if (window.customSoundBuffers) {
                    window.customSoundBuffers.delete(soundName);
                }

                // استعادة الصوت الافتراضي
                const defaultSound = generateDefaultSound(soundName);
                if (defaultSound) {
                    soundBuffers.set(soundName, defaultSound);
                }

                // تحديث واجهة المستخدم
                updateUploadUI(soundName, false);
                showUploadStatus(soundName, 'success', 'تم حذف الصوت المخصص');

                // حفظ التغييرات
                saveCustomSounds();

                // إشعار اللعبة بالتحديث
                notifyGameUpdate();

                console.log(`🗑️ تم حذف صوت مخصص: ${soundName}`);
            }
        }

        // تحديث واجهة المستخدم للرفع
        function updateUploadUI(soundName, hasCustomFile) {
            const uploadItem = document.querySelector(`#upload_${soundName}`).closest('.sound-upload-item');
            const removeBtn = uploadItem.querySelector('.remove-custom-btn');
            const fileInput = uploadItem.querySelector('input[type="file"]');

            if (hasCustomFile) {
                uploadItem.classList.add('has-file');
                removeBtn.style.display = 'flex';
            } else {
                uploadItem.classList.remove('has-file');
                removeBtn.style.display = 'none';
                fileInput.value = '';
            }

            // تحديث مؤشر الصوت المخصص في أزرار المعاينة
            updatePreviewButtonIndicator(soundName, hasCustomFile);
        }

        // تحديث مؤشر الصوت المخصص في أزرار المعاينة
        function updatePreviewButtonIndicator(soundName, hasCustomFile) {
            const previewButtons = document.querySelectorAll('.preview-btn');
            previewButtons.forEach(btn => {
                const btnText = btn.textContent;
                const soundMap = {
                    '🍽️ أكل صغير': 'eat_small',
                    '🍖 أكل كبير': 'eat_large',
                    '🦈 زئير قرش': 'shark_roar',
                    '🐋 نداء حوت': 'whale_call',
                    '✨ ظهور': 'spawn',
                    '🌊 أمواج البحر': 'ocean_waves',
                    '💧 فقاعات': 'underwater_bubbles',
                    '🌑 أعماق البحر': 'deep_sea',
                    '🎵 أغنية الحوت': 'whale_song'
                };

                if (soundMap[btnText] === soundName) {
                    if (hasCustomFile) {
                        btn.classList.add('has-custom');
                        btn.title = 'صوت مخصص نشط - اضغط للمعاينة';
                    } else {
                        btn.classList.remove('has-custom');
                        btn.title = 'صوت افتراضي - اضغط للمعاينة';
                    }
                }
            });
        }

        // عرض حالة الرفع
        function showUploadStatus(soundName, type, message) {
            const uploadItem = document.querySelector(`#upload_${soundName}`).closest('.sound-upload-item');

            // إزالة رسائل الحالة السابقة
            const existingStatus = uploadItem.querySelector('.upload-status');
            if (existingStatus) {
                existingStatus.remove();
            }

            // إنشاء رسالة حالة جديدة
            const statusDiv = document.createElement('div');
            statusDiv.className = `upload-status ${type}`;
            statusDiv.textContent = message;
            uploadItem.appendChild(statusDiv);

            // إزالة الرسالة بعد 3 ثوان
            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.remove();
                }
            }, 3000);
        }

        // توليد الصوت الافتراضي
        function generateDefaultSound(soundName) {
            switch (soundName) {
                case 'eat_small':
                    return generateRealisticEatingSound('small');
                case 'eat_large':
                    return generateRealisticEatingSound('large');
                case 'shark_roar':
                    return generateScarySound('shark');
                case 'whale_call':
                    return generateScarySound('whale');
                case 'spawn':
                    return generateSpawnSound();
                case 'ocean_waves':
                    return generateAmbientSound('waves');
                case 'underwater_bubbles':
                    return generateAmbientSound('bubbles');
                case 'deep_sea':
                    return generateAmbientSound('deep');
                case 'whale_song':
                    return generateAmbientSound('whale_song');
                default:
                    return null;
            }
        }

        // حفظ الأصوات المخصصة في localStorage
        function saveCustomSounds() {
            try {
                // الحصول على القائمة الحالية أولاً
                let customSoundsData = {};
                const existingData = localStorage.getItem('fishGameCustomSounds');
                if (existingData) {
                    try {
                        customSoundsData = JSON.parse(existingData);
                    } catch (e) {
                        console.warn('فشل في قراءة البيانات الموجودة، سيتم إنشاء قائمة جديدة');
                        customSoundsData = {};
                    }
                }

                // إضافة/تحديث الأصوات الحالية
                for (const [soundName, soundData] of customSounds.entries()) {
                    customSoundsData[soundName] = {
                        fileName: soundData.fileName,
                        size: soundData.size,
                        timestamp: Date.now()
                    };
                }

                // البحث عن أصوات محفوظة في localStorage وإضافتها للقائمة
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('fishGameCustomSound_')) {
                        const soundName = key.replace('fishGameCustomSound_', '');
                        if (!customSoundsData[soundName]) {
                            customSoundsData[soundName] = {
                                fileName: `${soundName}_custom.audio`,
                                size: 0,
                                timestamp: Date.now()
                            };
                        }
                    }
                }

                localStorage.setItem('fishGameCustomSounds', JSON.stringify(customSoundsData));
                console.log('💾 تم حفظ قائمة الأصوات المخصصة:', Object.keys(customSoundsData));

            } catch (error) {
                console.error('فشل في حفظ معلومات الأصوات المخصصة:', error);
            }
        }

        // تحميل معلومات الأصوات المخصصة
        function loadCustomSoundsInfo() {
            try {
                const savedData = localStorage.getItem('fishGameCustomSounds');
                if (savedData) {
                    const customSoundsData = JSON.parse(savedData);
                    for (const [soundName, soundInfo] of Object.entries(customSoundsData)) {
                        // تحديث واجهة المستخدم لإظهار أن هناك ملف مخصص
                        updateUploadUI(soundName, true);
                        console.log(`📁 ملف صوتي مخصص محفوظ: ${soundName} - ${soundInfo.fileName}`);
                    }
                }
            } catch (error) {
                console.warn('فشل في تحميل معلومات الأصوات المخصصة:', error);
            }
        }

        // إشعار اللعبة بتحديث الأصوات
        function notifyGameUpdate() {
            // إرسال حدث مخصص للنوافذ الأخرى
            localStorage.setItem('fishGameSoundUpdate', Date.now().toString());

            // إرسال رسالة للنافذة الأم إذا كانت موجودة
            if (window.opener && !window.opener.closed) {
                try {
                    window.opener.postMessage({
                        type: 'customSoundUpdate',
                        timestamp: Date.now()
                    }, '*');
                } catch (error) {
                    console.warn('فشل في إرسال إشعار للنافذة الأم:', error);
                }
            }

            console.log('📢 تم إرسال إشعار تحديث الأصوات للعبة');
        }

        // تصدير جميع البيانات للنقل للصفحة المحسنة
        function exportAllData() {
            try {
                showStatus('📤 جاري تجميع البيانات...', 'info');

                // جمع جميع البيانات
                const exportData = {
                    version: '1.0',
                    timestamp: new Date().toISOString(),
                    source: 'fish-settings-original',

                    // الإعدادات الأساسية
                    settings: getAllSettings(),

                    // التعيينات (تأكد من أنها مصفوفة صحيحة)
                    assignments: Array.isArray(assignments) ? assignments : [],

                    // معلومات الأصوات المخصصة
                    customSoundsInfo: {},

                    // الأصوات المخصصة (base64)
                    customSounds: {}
                };

                // طباعة معلومات التصدير للتشخيص
                console.log('📊 معلومات التصدير:');
                console.log('- الإعدادات:', Object.keys(exportData.settings).length);
                console.log('- التعيينات:', exportData.assignments.length);
                console.log('- نوع التعيينات:', Array.isArray(exportData.assignments) ? 'مصفوفة' : typeof exportData.assignments);

                // جمع معلومات الأصوات المخصصة
                try {
                    const savedCustomSounds = localStorage.getItem('fishGameCustomSounds');
                    if (savedCustomSounds) {
                        exportData.customSoundsInfo = JSON.parse(savedCustomSounds);
                    }
                } catch (error) {
                    console.warn('تحذير: فشل في تحميل معلومات الأصوات المخصصة:', error);
                }

                // جمع الأصوات المخصصة
                let customSoundsCount = 0;
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('fishGameCustomSound_')) {
                        const soundName = key.replace('fishGameCustomSound_', '');
                        const soundData = localStorage.getItem(key);
                        if (soundData) {
                            exportData.customSounds[soundName] = soundData;
                            customSoundsCount++;
                        }
                    }
                }

                // إنشاء ملف التصدير
                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `fish-game-complete-backup-${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                // عرض تقرير التصدير
                const report = [
                    `✅ الإعدادات: ${Object.keys(exportData.settings).length} إعداد`,
                    `✅ التعيينات: ${exportData.assignments.length} تعيين`,
                    `✅ الأصوات المخصصة: ${customSoundsCount} صوت`,
                    `✅ حجم الملف: ${(dataStr.length / 1024 / 1024).toFixed(2)} MB`
                ].join('\n');

                showStatus('📤 تم تصدير جميع البيانات بنجاح!', 'success');

                setTimeout(() => {
                    alert(`📤 تم تصدير جميع البيانات بنجاح!\n\n${report}\n\nيمكنك الآن استيراد هذا الملف في الصفحة المحسنة.`);
                }, 500);

                console.log('📤 تم تصدير البيانات:', exportData);

            } catch (error) {
                showStatus('❌ فشل في تصدير البيانات: ' + error.message, 'error');
                console.error('خطأ في التصدير:', error);
            }
        }
    </script>
</body>
</html>