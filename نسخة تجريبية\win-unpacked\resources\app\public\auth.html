<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>تسجيل الدخول - StreamTok</title>
  <script src="/preload-theme.js"></script>
  <link rel="stylesheet" href="/dark-mode.css">
  <link rel="stylesheet" href="/css/loading-indicator.css">
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    :root {
      color-scheme: light dark;
    }
    
    body {
      font-family: 'Taja<PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--bg-gradient);
      color: var(--text-color);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .auth-container {
      background: var(--section-bg);
      border-radius: 20px;
      padding: 40px;
      box-shadow: 0 20px 40px var(--shadow-color);
      width: 100%;
      max-width: 400px;
      margin: 20px;
      transition: all 0.3s ease;
    }

    .auth-header {
      text-align: center;
      margin-bottom: 30px;
    }

    .auth-header h1 {
      color: var(--primary-color);
      font-size: 2rem;
      margin-bottom: 10px;
      font-weight: 700;
    }

    .auth-header p {
      color: var(--text-secondary);
      margin: 0;
    }

    .auth-tabs {
      display: flex;
      margin-bottom: 30px;
      background: var(--input-bg);
      border-radius: 10px;
      padding: 5px;
    }

    .auth-tab {
      flex: 1;
      padding: 12px;
      text-align: center;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 500;
    }

    .auth-tab.active {
      background: var(--primary-gradient);
      color: white;
      box-shadow: 0 2px 10px rgba(255, 59, 92, 0.3);
    }

    .auth-form {
      display: none;
    }

    .auth-form.active {
      display: block;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-secondary);
    }

    .form-group input {
      width: 100%;
      padding: 15px;
      border: 2px solid var(--input-border);
      border-radius: 10px;
      background: var(--input-bg);
      color: var(--text-color);
      font-size: 16px;
      transition: all 0.3s ease;
      box-sizing: border-box;
    }

    .form-group input:focus {
      border-color: #ff3b5c;
      outline: none;
      box-shadow: 0 0 0 3px rgba(255, 59, 92, 0.1);
    }

    .auth-button {
      width: 100%;
      padding: 15px;
      background: var(--primary-gradient);
      color: white;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 20px;
    }

    .auth-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(255, 59, 92, 0.3);
    }

    .auth-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .auth-divider {
      text-align: center;
      margin: 20px 0;
      position: relative;
      color: var(--text-secondary);
    }

    .auth-divider::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: var(--border-color);
    }

    .auth-divider span {
      background: var(--section-bg);
      padding: 0 15px;
    }

    .back-link {
      text-align: center;
      margin-top: 20px;
    }

    .back-link a {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .back-link a:hover {
      text-decoration: underline;
    }

    .error-message {
      background: rgba(220, 53, 69, 0.1);
      color: #dc3545;
      padding: 12px;
      border-radius: 8px;
      margin-bottom: 20px;
      border: 1px solid rgba(220, 53, 69, 0.2);
      display: none;
    }

    .success-message {
      background: rgba(25, 135, 84, 0.1);
      color: #198754;
      padding: 12px;
      border-radius: 8px;
      margin-bottom: 20px;
      border: 1px solid rgba(25, 135, 84, 0.2);
      display: none;
    }

    .loading {
      display: none;
      text-align: center;
      margin: 20px 0;
    }

    .loading-spinner {
      width: 30px;
      height: 30px;
      border: 3px solid var(--border-color);
      border-top: 3px solid #ff3b5c;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 10px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="auth-container">
    <div class="auth-header">
      <h1>StreamTok</h1>
      <p>مرحباً بك في منصة البث التفاعلي</p>
    </div>

    <div class="auth-tabs">
      <div class="auth-tab active" data-tab="login">تسجيل الدخول</div>
      <div class="auth-tab" data-tab="register">إنشاء حساب</div>
    </div>

    <div class="error-message" id="errorMessage"></div>
    <div class="success-message" id="successMessage"></div>
    <div class="loading" id="loading">
      <div class="loading-spinner"></div>
      <p>جاري المعالجة...</p>
    </div>

    <!-- Login Form -->
    <form class="auth-form active" id="loginForm">
      <div class="form-group">
        <label for="loginEmail">البريد الإلكتروني</label>
        <input type="email" id="loginEmail" required>
      </div>
      <div class="form-group">
        <label for="loginPassword">كلمة المرور</label>
        <input type="password" id="loginPassword" required>
      </div>
      <button type="submit" class="auth-button">تسجيل الدخول</button>
    </form>

    <!-- Register Form -->
    <form class="auth-form" id="registerForm">
      <div class="form-group">
        <label for="registerName">الاسم الكامل</label>
        <input type="text" id="registerName" required>
      </div>
      <div class="form-group">
        <label for="registerEmail">البريد الإلكتروني</label>
        <input type="email" id="registerEmail" required>
      </div>
      <div class="form-group">
        <label for="registerPassword">كلمة المرور</label>
        <input type="password" id="registerPassword" required minlength="6">
      </div>
      <div class="form-group">
        <label for="confirmPassword">تأكيد كلمة المرور</label>
        <input type="password" id="confirmPassword" required minlength="6">
      </div>
      <button type="submit" class="auth-button">إنشاء حساب</button>
    </form>

    <div class="back-link">
      <a href="/">← العودة إلى الصفحة الرئيسية</a>
    </div>
  </div>

  <script type="module" src="/js/firebase-config.js"></script>
  <script src="/dark-mode.js"></script>
  <script type="module">
    // متغير لمنع التوجيه المتكرر
    let isProcessingAuth = false;
    let hasRedirected = false;

    // Wait for Firebase to be initialized
    window.addEventListener('load', () => {
      initializeAuthPage();
    });

    function initializeAuthPage() {
      // Tab switching
      const tabs = document.querySelectorAll('.auth-tab');
      const forms = document.querySelectorAll('.auth-form');

      tabs.forEach(tab => {
        tab.addEventListener('click', () => {
          const targetTab = tab.dataset.tab;
          
          // Update active tab
          tabs.forEach(t => t.classList.remove('active'));
          tab.classList.add('active');
          
          // Update active form
          forms.forEach(f => f.classList.remove('active'));
          document.getElementById(targetTab + 'Form').classList.add('active');
          
          // Clear messages
          clearMessages();
        });
      });

      // Form submissions
      document.getElementById('loginForm').addEventListener('submit', handleLogin);
      document.getElementById('registerForm').addEventListener('submit', handleRegister);

      // Check if user is already logged in - نسخة مبسطة
      window.addEventListener('authStateChanged', (event) => {
        if (event.detail.user && !hasRedirected) {
          console.log('🔄 Auth page - User already logged in:', event.detail.user.email);
          hasRedirected = true;

          // تحقق من تأكيد البريد الإلكتروني
          if (event.detail.user.emailVerified) {
            console.log('✅ Email verified, going to connection page');
            setTimeout(() => {
              window.location.href = '/connection.html';
            }, 500);
          } else {
            console.log('❌ Email not verified, going to verification page');
            setTimeout(() => {
              window.location.href = '/email-verification.html';
            }, 500);
          }
        }
      });
    }

    async function handleLogin(event) {
      event.preventDefault();

      if (isProcessingAuth) return;
      isProcessingAuth = true;

      const email = document.getElementById('loginEmail').value;
      const password = document.getElementById('loginPassword').value;

      showLoading(true);
      clearMessages();

      try {
        const userCredential = await window.firebaseFunctions.signInWithEmailAndPassword(window.firebaseAuth, email, password);

        // التحقق من تأكيد البريد الإلكتروني
        if (!userCredential.user.emailVerified) {
          showError('يرجى تأكيد بريدك الإلكتروني أولاً.');
          setTimeout(() => {
            window.location.href = '/email-verification.html';
          }, 2000);
          return;
        }

        showSuccess('تم تسجيل الدخول بنجاح! جاري التوجيه...');
        hasRedirected = true;

        // توجيه فوري بعد النجاح
        setTimeout(() => {
          window.location.href = '/connection.html';
        }, 1000);
        
      } catch (error) {
        console.error('Login error:', error);
        showError(getErrorMessage(error.code));
        isProcessingAuth = false;
      } finally {
        showLoading(false);
      }
    }

    async function handleRegister(event) {
      event.preventDefault();

      if (isProcessingAuth) return;
      isProcessingAuth = true;

      const name = document.getElementById('registerName').value;
      const email = document.getElementById('registerEmail').value;
      const password = document.getElementById('registerPassword').value;
      const confirmPassword = document.getElementById('confirmPassword').value;

      // Validate passwords match
      if (password !== confirmPassword) {
        showError('كلمات المرور غير متطابقة');
        isProcessingAuth = false;
        return;
      }

      showLoading(true);
      clearMessages();
      
      try {
        // Create user account
        const userCredential = await window.firebaseFunctions.createUserWithEmailAndPassword(window.firebaseAuth, email, password);

        // Update user profile
        await window.firebaseFunctions.updateProfile(userCredential.user, {
          displayName: name
        });

        // Send email verification
        await window.firebaseFunctions.sendEmailVerification(userCredential.user);

        // Create user profile in Firestore
        await window.firebaseHelpers.createUserProfile(userCredential.user.uid, {
          name: name,
          email: email,
          subscriptionStatus: 'none',
          emailVerified: false
        });

        showSuccess('تم إنشاء الحساب بنجاح! يرجى تأكيد بريدك الإلكتروني.');
        hasRedirected = true;

        // توجيه لصفحة تأكيد البريد الإلكتروني
        setTimeout(() => {
          window.location.href = '/email-verification.html';
        }, 1500);
        
      } catch (error) {
        console.error('Registration error:', error);
        showError(getErrorMessage(error.code));
        isProcessingAuth = false;
      } finally {
        showLoading(false);
      }
    }

    function showLoading(show) {
      document.getElementById('loading').style.display = show ? 'block' : 'none';
      
      // Disable form buttons
      const buttons = document.querySelectorAll('.auth-button');
      buttons.forEach(btn => btn.disabled = show);
    }

    function showError(message) {
      const errorDiv = document.getElementById('errorMessage');
      errorDiv.textContent = message;
      errorDiv.style.display = 'block';
    }

    function showSuccess(message) {
      const successDiv = document.getElementById('successMessage');
      successDiv.textContent = message;
      successDiv.style.display = 'block';
    }

    function clearMessages() {
      document.getElementById('errorMessage').style.display = 'none';
      document.getElementById('successMessage').style.display = 'none';
    }

    function getErrorMessage(errorCode) {
      const errorMessages = {
        'auth/user-not-found': 'لم يتم العثور على حساب بهذا البريد الإلكتروني',
        'auth/wrong-password': 'كلمة المرور غير صحيحة',
        'auth/email-already-in-use': 'هذا البريد الإلكتروني مستخدم بالفعل',
        'auth/weak-password': 'كلمة المرور ضعيفة جداً',
        'auth/invalid-email': 'البريد الإلكتروني غير صحيح',
        'auth/too-many-requests': 'تم تجاوز عدد المحاولات المسموح، حاول مرة أخرى لاحقاً',
        'auth/network-request-failed': 'خطأ في الاتصال بالإنترنت'
      };
      
      return errorMessages[errorCode] || 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى';
    }
  </script>
</body>
</html>
