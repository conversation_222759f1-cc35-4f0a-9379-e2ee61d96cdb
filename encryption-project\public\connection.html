<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>الاتصال بـ TikTok Live</title>
  <!-- تحميل سكريبت الوضع المظلم قبل أي شيء آخر لمنع الوميض -->
  <script src="/preload-theme.js"></script>
  <link rel="stylesheet" href="/dark-mode.css">
  <link rel="stylesheet" href="/css/loading-indicator.css">
  <link rel="stylesheet" href="/css/sidebar-enhanced.css">
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    :root {
      color-scheme: light dark;
    }
    body {
      font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--bg-gradient);
      color: var(--text-color);
      transition: background 0.3s ease, color 0.3s ease;
      min-height: 100vh;
      display: flex;
    }

    .sidebar {
      width: 250px;
      background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
      box-shadow: 3px 0 15px rgba(0, 0, 0, 0.08);
      padding: 0;
      position: fixed;
      height: 100%;
      right: 0;
      top: 0;
      overflow-y: auto;
      z-index: 1000;
      border-left: 1px solid rgba(0, 0, 0, 0.05);
      border-top-left-radius: 15px;
      border-bottom-left-radius: 15px;
      opacity: 0.95 !important;
    }

    .logo {
      text-align: center;
      padding: 25px 15px;
      margin-bottom: 10px;
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      box-shadow: 0 4px 10px rgba(255, 59, 92, 0.2);
      position: relative;
      border-top-left-radius: 15px;
    }

    .logo h3 {
      color: white;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .logo::after {
      content: '';
      position: absolute;
      bottom: -10px;
      right: 50%;
      transform: translateX(50%);
      width: 20px;
      height: 20px;
      background: #ff3b5c;
      transform: rotate(45deg) translateX(50%);
      box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
      z-index: -1;
    }

    .logo img {
      max-width: 80%;
      height: auto;
    }

    .nav-menu {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 20px 15px;
    }

    .nav-menu a {
      display: flex;
      align-items: center;
      padding: 14px 18px;
      color: #444;
      text-decoration: none;
      border-radius: 12px;
      transition: all 0.3s ease;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
      border: 1px solid rgba(0, 0, 0, 0.03);
    }

    .nav-menu a::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 3px;
      height: 100%;
      background: #ff3b5c;
      transform: scaleY(0);
      transition: transform 0.3s ease;
    }

    .nav-menu a:hover {
      background-color: #f0f4f8;
      color: #ff3b5c;
      transform: translateY(-2px);
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
    }

    .nav-menu a:hover::before {
      transform: scaleY(1);
    }

    .nav-menu a.active {
      background: linear-gradient(45deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      box-shadow: 0 5px 15px rgba(255, 59, 92, 0.25);
      border: none;
    }

    .nav-menu a.active::before {
      transform: scaleY(0);
    }

    .nav-icon {
      margin-left: 15px;
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .nav-menu a:hover .nav-icon {
      background-color: rgba(255, 59, 92, 0.1);
      transform: rotate(5deg);
    }

    .nav-menu a.active .nav-icon {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .main-content {
      flex: 1;
      margin-right: 250px;
      padding: 20px;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: var(--section-bg);
      border-radius: 15px;
      padding: 30px;
      box-shadow: 0 10px 30px var(--shadow-color);
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
      margin-top: 30px;
      margin-bottom: 30px;
      opacity: 0.95 !important;
    }

    h1 {
      color: var(--primary-color);
      text-align: center;
      font-size: 2.2rem;
      margin-bottom: 30px;
      font-weight: 700;
      border-bottom: 2px solid var(--border-color);
      padding-bottom: 15px;
    }

    .section {
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid var(--border-color);
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-color);
    }

    input {
      width: 100%;
      padding: 12px;
      border: 1px solid var(--input-border);
      background-color: var(--input-bg);
      color: var(--text-color);
      border-radius: 8px;
      font-size: 16px;
      box-sizing: border-box;
      transition: border-color 0.3s, box-shadow 0.3s;
      font-family: 'Tajawal', sans-serif;
    }

    input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(255, 59, 92, 0.1);
    }

    button {
      background: var(--primary-gradient);
      color: var(--button-text);
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s;
      font-weight: 500;
      box-shadow: 0 4px 6px rgba(255, 59, 92, 0.2);
      font-family: 'Tajawal', sans-serif;
    }

    button:hover {
      background-color: #e6354f;
      transform: translateY(-2px);
      box-shadow: 0 6px 8px rgba(255, 59, 92, 0.25);
    }

    button:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(255, 59, 92, 0.25);
    }

    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
      box-shadow: none;
      transform: none;
    }

    .status {
      padding: 12px;
      border-radius: 8px;
      margin-top: 15px;
      font-weight: 500;
      transition: all 0.3s;
    }

    .connected {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .disconnected {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .copy-section {
      margin-top: 20px;
      padding: 15px;
      background-color: var(--table-header-bg);
      border-radius: 8px;
      border: 1px dashed var(--border-color);
      transition: background-color 0.3s ease;
      opacity: 0.95 !important;
    }

    .copy-url {
      font-family: monospace;
      background-color: #eee;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 10px;
      word-break: break-all;
      opacity: 0.95 !important;
    }

    .footer {
      text-align: center;
      margin-top: 40px;
      color: #777;
      font-size: 0.9rem;
    }

    /* ستايل قسم الأحداث */
    .events-container {
      background-color: var(--table-header-bg);
      border-radius: 10px;
      padding: 15px;
      margin-top: 15px;
      border: 1px solid var(--border-color);
      transition: background-color 0.3s ease;
      opacity: 0.95 !important;
    }

    .events-tabs {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-bottom: 10px;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }

    .tab-btn {
      background-color: #f1f1f1;
      border: 1px solid #ddd;
      color: #555;
      padding: 8px 15px;
      border-radius: 20px;
      cursor: pointer;
      font-size: 14px;
      box-shadow: none;
      transition: all 0.2s;
    }

    .tab-btn:hover {
      background-color: #e9ecef;
      transform: translateY(0);
    }

    .tab-btn.active {
      background-color: #ff3b5c;
      color: white;
      border-color: #ff3b5c;
    }

    .events-list {
      max-height: 300px;
      overflow-y: auto;
      padding: 10px;
      background-color: white;
      border-radius: 8px;
      border: 1px solid #eee;
      opacity: 0.95 !important;
    }

    .event-item {
      padding: 10px;
      border-radius: 8px;
      margin-bottom: 8px;
      border-right: 3px solid #ddd;
      background-color: #fafafa;
      position: relative;
      animation: fadeIn 0.3s ease-in-out;
      opacity: 0.95 !important;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .event-item:last-child {
      margin-bottom: 0;
    }

    .event-item.gift { border-right-color: #ff3b5c; }
    .event-item.like { border-right-color: #ff4d4f; }
    .event-item.comment { border-right-color: #1890ff; }
    .event-item.follow { border-right-color: #52c41a; }
    .event-item.share { border-right-color: #722ed1; }
    .event-item.join { border-right-color: #1677ff; }

    .event-header {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
    }

    .event-type {
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      margin-left: 8px;
      color: white;
    }

    .event-type.gift { background-color: #ff3b5c; }
    .event-type.like { background-color: #ff4d4f; }
    .event-type.comment { background-color: #1890ff; }
    .event-type.follow { background-color: #52c41a; }
    .event-type.share { background-color: #722ed1; }
    .event-type.join { background-color: #1677ff; }

    .event-user {
      font-weight: 500;
    }

    .event-content {
      font-size: 14px;
    }

    .event-time {
      font-size: 11px;
      color: #999;
      margin-top: 5px;
      text-align: left;
    }

    .events-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;
    }

    .events-counter {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }

    /* أنماط لمبة فروست الطويلة */
    .frost-light-container {
      position: relative;
      display: block;
      margin: 0 auto;
      text-align: center;
      width: 100%;
      padding-top: 40px; /* مساحة أكبر للمبة */
    }

    /* حامل اللمبة */
    .frost-tube-fixture {
      position: absolute;
      top: -2px;
      left: 50%;
      transform: translateX(-50%);
      width: 85%;
      max-width: 650px;
      height: 30px;
      background: linear-gradient(to bottom,
                #555 0%,
                #333 100%);
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      z-index: 8;
    }

    /* أسلاك التعليق */
    .frost-tube-fixture::before,
    .frost-tube-fixture::after {
      content: '';
      position: absolute;
      top: -27px;
      width: 2px;
      height: 26px;
      background: #777;
      box-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
    }

    .frost-tube-fixture::before {
      left: 25%;
    }

    .frost-tube-fixture::after {
      right: 25%;
    }

    /* اللمبة نفسها */
    .frost-tube-light {
      position: absolute;
      top: 5px;
      left: 50%;
      transform: translateX(-50%);
      width: 80%;
      max-width: 600px;
      height: 16px;
      border-radius: 8px;
      background: linear-gradient(to right,
                rgba(135,206,250,0) 0%,
                rgba(173,216,230,0.3) 5%,
                rgba(255,255,255,0.9) 20%,
                rgba(255,255,255,0.9) 80%,
                rgba(173,216,230,0.3) 95%,
                rgba(135,206,250,0) 100%);
      box-shadow: 0 0 15px 5px rgba(135,206,250,0.7),
                  0 0 30px 10px rgba(135,206,250,0.4),
                  0 0 45px 15px rgba(135,206,250,0.2);
      animation: tube-pulse 3s infinite alternate;
      z-index: 10;
      /* إضافة تأثير الزجاج الفروست */
      backdrop-filter: blur(2px);
      -webkit-backdrop-filter: blur(2px);
    }

    /* تأثير الانعكاس داخل اللمبة */
    .frost-tube-light::before {
      content: '';
      position: absolute;
      top: 3px;
      left: 5%;
      width: 90%;
      height: 3px;
      border-radius: 3px;
      background: rgba(255,255,255,0.9);
      opacity: 0.7;
    }

    /* تأثير الأطراف المعدنية للمبة */
    .frost-tube-light::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      width: 100%;
      height: 18px;
      border-radius: 9px;
      background: linear-gradient(to right,
                rgba(192,192,192,0.9) 0%,
                rgba(192,192,192,0) 5%,
                rgba(192,192,192,0) 95%,
                rgba(192,192,192,0.9) 100%);
      z-index: -1;
    }

    /* أطراف توصيل اللمبة */
    .frost-tube-cap-left,
    .frost-tube-cap-right {
      position: absolute;
      top: 4px;
      width: 23px;
      height: 18px;
      background: linear-gradient(to right, #aaa, #ddd, #aaa);
      z-index: 11;
    }

    .frost-tube-cap-left {
      left: calc(50% - 36% - 5px);
      border-radius: 4px 0 0 4px;
    }

    .frost-tube-cap-right {
      right: calc(50% - 36% - 5px);
      border-radius: 0 4px 4px 0;
    }

    /* تأثير الضوء المنتشر من اللمبة */
    .frost-tube-glow {
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      width: 90%;
      max-width: 700px;
      height: 80px;
      background: radial-gradient(ellipse at center,
                rgba(135,206,250,0.4) 0%,
                rgba(173,216,230,0.3) 40%,
                rgba(135,206,250,0) 80%);
      filter: blur(15px);
      z-index: 5;
      animation: tube-glow-pulse 4s infinite alternate;
      pointer-events: none; /* لتجنب التداخل مع العناصر الأخرى */
    }

    /* تأثير الغبار والشوائب في اللمبة */
    .frost-tube-dust {
      position: absolute;
      top: 5px;
      left: 50%;
      transform: translateX(-50%);
      width: 80%;
      max-width: 600px;
      height: 16px;
      z-index: 12;
      overflow: hidden;
      border-radius: 8px;
      opacity: 0.2;
      pointer-events: none;
    }

    .frost-tube-dust::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      background-image:
        radial-gradient(circle at 10% 30%, rgba(0,0,0,0.2) 1px, transparent 1px),
        radial-gradient(circle at 20% 70%, rgba(0,0,0,0.1) 1px, transparent 1px),
        radial-gradient(circle at 30% 40%, rgba(0,0,0,0.2) 1px, transparent 1px),
        radial-gradient(circle at 40% 60%, rgba(0,0,0,0.1) 1px, transparent 1px),
        radial-gradient(circle at 60% 30%, rgba(0,0,0,0.2) 1px, transparent 1px),
        radial-gradient(circle at 70% 70%, rgba(0,0,0,0.1) 1px, transparent 1px),
        radial-gradient(circle at 80% 40%, rgba(0,0,0,0.2) 1px, transparent 1px),
        radial-gradient(circle at 90% 60%, rgba(0,0,0,0.1) 1px, transparent 1px);
      background-size: 100% 100%;
    }

    /* إضافة تأثير ضوء على العنوان */
    .frost-light-container h1 {
      position: relative;
      z-index: 6;
      text-shadow: 0 0 10px rgba(135,206,250,0.5);
      margin-top: 30px; /* مساحة بين اللمبة والعنوان */
    }

    /* تأثير الوميض للمبة الفلورسنت */
    @keyframes tube-pulse {
      0%, 5%, 10% {
        opacity: 0.8;
        box-shadow: 0 0 15px 5px rgba(135,206,250,0.7),
                    0 0 30px 10px rgba(135,206,250,0.4),
                    0 0 45px 15px rgba(135,206,250,0.2);
      }
      1%, 6% {
        opacity: 0.9;
        box-shadow: 0 0 20px 8px rgba(135,206,250,0.9),
                    0 0 40px 15px rgba(135,206,250,0.6),
                    0 0 60px 20px rgba(135,206,250,0.3);
      }
      100% {
        opacity: 1;
        box-shadow: 0 0 20px 8px rgba(135,206,250,0.9),
                    0 0 40px 15px rgba(135,206,250,0.6),
                    0 0 60px 20px rgba(135,206,250,0.3);
      }
    }

    /* تأثير الوميض للضوء المنتشر */
    @keyframes tube-glow-pulse {
      0%, 5%, 10% {
        opacity: 0.7;
      }
      1%, 6% {
        opacity: 0.9;
      }
      100% {
        opacity: 0.8;
      }
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="logo">
      <h3>StreamTok</h3>
    </div>
    <div class="nav-menu">
      <a href="/" class="active">
        <span class="nav-icon">🔌</span>
        الاتصال بـ TikTok Live
      </a>
      <a href="/mappings.html">
        <span class="nav-icon">🛠️</span>
        ربط الهدايا بالإجراءات
      </a>
      <a href="/tts-comments.html">
        <span class="nav-icon">🔊</span>
        قراءة التعليقات
      </a>
      <a href="/games.html">
        <span class="nav-icon">🎯</span>
        الألعاب
      </a>
      <a href="/settings.html">
        <span class="nav-icon">⚙️</span>
        الإعدادات
      </a>
      <a href="/profiles.html">
        <span class="nav-icon">👤</span>
        الملفات الشخصية
      </a>
      <a href="/subscriptions.html">
        <span class="nav-icon">⚡</span>
        الاشتراكات
      </a>
      <a href="/contact.html">
        <span class="nav-icon">📞</span>
        اتصل بنا
      </a>
      <a href="/overlay.html" target="_blank">
        <span class="nav-icon">🖥️</span>
        فتح Overlay
      </a>
      <a href="/overlay-bar.html">
        <span class="nav-icon">🎮</span>
        Overlay Bar
      </a>

      <!-- User Info Section -->
      <div class="user-info" style="display: none; margin-top: 20px; padding: 15px; background: var(--card-bg); border-radius: 10px; border: 1px solid var(--border-color);">
        <div class="user-details"></div>
      </div>
    </div>
  </div>

  <div class="main-content">
    <div class="container">
      <div class="frost-light-container">
        <div class="frost-tube-fixture"></div>
        <div class="frost-tube-light"></div>
        <div class="frost-tube-cap-left"></div>
        <div class="frost-tube-cap-right"></div>
        <div class="frost-tube-dust"></div>
        <div class="frost-tube-glow"></div>
        <h1>الاتصال بـ TikTok Live</h1>
      </div>

      <div class="section">
        <div class="form-group">
          <label for="username">اسم المستخدم على TikTok:</label>
          <input type="text" id="username" placeholder="أدخل اسم المستخدم (بدون @)" />
        </div>

        <div class="buttons">
          <button id="connectBtn">اتصال</button>
          <button id="disconnectBtn" disabled>قطع الاتصال</button>
        </div>

        <div class="status disconnected" id="connectionStatus">
          غير متصل. الرجاء إدخال اسم مستخدم.
        </div>
      </div>

      <!-- قسم أحداث TikTok -->
      <div class="section" id="eventsSection">
        <h3>أحداث TikTok الحية</h3>
        <p>سيتم عرض آخر الأحداث من البث المباشر هنا:</p>

        <div class="events-container">
          <div class="events-tabs">
            <button class="tab-btn active" data-tab="all">الكل</button>
            <button class="tab-btn" data-tab="gifts">الهدايا</button>
            <button class="tab-btn" data-tab="likes">الإعجابات</button>
            <button class="tab-btn" data-tab="comments">التعليقات</button>
            <button class="tab-btn" data-tab="follows">المتابعين</button>
            <button class="tab-btn" data-tab="shares">المشاركات</button>
            <button class="tab-btn" data-tab="join">الانضمام</button>
          </div>

          <div class="events-content">
            <div id="events-list" class="events-list"></div>
          </div>

          <div class="events-controls">
            <button id="clearEventsBtn" class="secondary">مسح الأحداث</button>
            <div class="events-counter">
              <span id="events-count">0</span> حدث
            </div>
          </div>
        </div>
      </div>

      <div class="footer">
        StreamTok &copy; 2025
        - By : Abdelrahman Mohamed
      </div>
    </div>
  </div>

  <script src="/socket.io/socket.io.js"></script>
  <script src="/js/settings-manager.js"></script>
  <script>
    const usernameInput = document.getElementById('username');
    const connectBtn = document.getElementById('connectBtn');
    const disconnectBtn = document.getElementById('disconnectBtn');
    const connectionStatus = document.getElementById('connectionStatus');

    // تخزين المتغيرات العامة
    let activeUsername = '';

    // اتصال Socket.IO
    const socket = io();

    // جعل socket متاح عالمي<|im_start|>
    window.socket = socket;

    // إرسال session عند الاتصال إذا كان المستخدم مسجل دخول
    socket.on('connect', () => {
      console.log('🔌 Socket connected');

      // إرسال session إذا كان هناك مستخدم مسجل دخول
      if (window.pendingUserAuth) {
        socket.emit('userAuthenticated', window.pendingUserAuth);
        console.log('✅ Pending user session sent to server:', window.pendingUserAuth.email);
        window.pendingUserAuth = null;
      }
    });

    // عند تحميل الصفحة، استرجع اسم المستخدم المحفوظ
    window.addEventListener('DOMContentLoaded', () => {
      // تهيئة نظام إدارة الإعدادات
      SettingsManager.initialize().then(() => {
        // الحصول على آخر اسم مستخدم تم استخدامه
        const lastUsername = SettingsManager.get('general.lastUsername');
        if (lastUsername) {
          usernameInput.value = lastUsername;
        } else {
          // إذا لم يكن موجوداً في نظام الإعدادات، استخدم القيمة المخزنة محلياً
          const savedUsername = localStorage.getItem('tiktokUsername');
          if (savedUsername) {
            usernameInput.value = savedUsername;
            // حفظ القيمة في نظام الإعدادات الجديد
            SettingsManager.set('general.lastUsername', savedUsername);
          }
        }
      });

      // طلب آخر اسم مستخدم من الخادم أيضاً
      socket.emit('getLastUsername');
    });

    // استقبال اسم المستخدم الأخير من الخادم
    socket.on('lastUsername', (data) => {
      if (data && data.username && !usernameInput.value) {
        usernameInput.value = data.username;
      }
    });

    // معالجة نقر زر الاتصال
    connectBtn.addEventListener('click', () => {
      const username = usernameInput.value.trim();
      if (!username) {
        alert('الرجاء إدخال اسم مستخدم صالح');
        return;
      }

      // حفظ الاسم في نظام الإعدادات والتخزين المحلي
      SettingsManager.set('general.lastUsername', username);
      localStorage.setItem('tiktokUsername', username);

      // تغيير حالة الواجهة
      usernameInput.disabled = true;
      connectBtn.disabled = true;
      connectionStatus.textContent = `جاري الاتصال بـ ${username}...`;
      connectionStatus.className = 'status';

      // طلب الاتصال من الخادم
      socket.emit('connectToTikTok', { username });
    });

    // معالجة نقر زر قطع الاتصال
    disconnectBtn.addEventListener('click', () => {
      // تغيير حالة الواجهة
      disconnectBtn.disabled = true;
      connectionStatus.textContent = 'جاري قطع الاتصال...';
      connectionStatus.className = 'status';

      // طلب قطع الاتصال من الخادم
      socket.emit('disconnectFromTikTok');
    });

    // استقبال تحديثات حالة الاتصال
    socket.on('connectionStatus', (data) => {
      console.log('Connection status update:', data);

      if (data.connected) {
        // تحديث الواجهة للحالة المتصلة
        connectionStatus.textContent = `متصل بـ ${data.username}`;
        connectionStatus.className = 'status connected';

        usernameInput.disabled = true;
        connectBtn.disabled = true;
        disconnectBtn.disabled = false;

        activeUsername = data.username;
      } else {
        // تحديث الواجهة للحالة غير المتصلة
        connectionStatus.textContent = 'غير متصل';
        connectionStatus.className = 'status disconnected';

        usernameInput.disabled = false;
        connectBtn.disabled = false;
        disconnectBtn.disabled = true;

        activeUsername = '';
      }
    });

    // معالجة أخطاء الاتصال
    socket.on('connectionError', (data) => {
      console.error('Connection error:', data);

      // عرض رسالة الخطأ للمستخدم
      connectionStatus.textContent = `خطأ: ${data.message}`;
      connectionStatus.className = 'status disconnected';

      // إعادة ضبط الواجهة
      usernameInput.disabled = false;
      connectBtn.disabled = false;
      disconnectBtn.disabled = true;
    });

    // قسم أحداث TikTok
    const eventsSection = document.getElementById('eventsSection');
    const eventsList = document.getElementById('events-list');
    const eventsCount = document.getElementById('events-count');
    const clearEventsBtn = document.getElementById('clearEventsBtn');
    const tabButtons = document.querySelectorAll('.tab-btn');

    // خاصية لتتبع عدد الأحداث
    let eventsCounter = 0;
    let currentTab = 'all';
    let events = [];

    // التبديل بين علامات التبويب
    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        // إلغاء تنشيط جميع الأزرار
        tabButtons.forEach(btn => btn.classList.remove('active'));

        // تنشيط الزر الحالي
        button.classList.add('active');

        // تحديث التبويب النشط
        currentTab = button.getAttribute('data-tab');

        // تحديث قائمة الأحداث المعروضة
        updateEventsList();
      });
    });

    // مسح الأحداث
    clearEventsBtn.addEventListener('click', () => {
      events = [];
      eventsCounter = 0;
      eventsCount.textContent = '0';
      updateEventsList();
    });

    // تحديث قائمة الأحداث المعروضة
    function updateEventsList() {
      eventsList.innerHTML = '';

      // تصفية الأحداث حسب التبويب النشط
      const filteredEvents = currentTab === 'all' ?
        events :
        events.filter(event => event.type === currentTab);

      // عرض رسالة إذا لم تكن هناك أحداث
      if (filteredEvents.length === 0) {
        eventsList.innerHTML = '<div class="no-events">لا توجد أحداث حتى الآن</div>';
        return;
      }

      // إنشاء عناصر الأحداث
      filteredEvents.forEach(event => {
        const eventItem = document.createElement('div');
        eventItem.className = `event-item ${event.type}`;

        const eventHeader = document.createElement('div');
        eventHeader.className = 'event-header';

        const eventType = document.createElement('span');
        eventType.className = `event-type ${event.type}`;
        eventType.textContent = getEventTypeName(event.type);

        const eventUser = document.createElement('span');
        eventUser.className = 'event-user';
        eventUser.textContent = event.username;

        eventHeader.appendChild(eventType);
        eventHeader.appendChild(eventUser);

        const eventContent = document.createElement('div');
        eventContent.className = 'event-content';
        eventContent.textContent = event.content;

        const eventTime = document.createElement('div');
        eventTime.className = 'event-time';
        eventTime.textContent = event.time;

        eventItem.appendChild(eventHeader);
        eventItem.appendChild(eventContent);
        eventItem.appendChild(eventTime);

        eventsList.appendChild(eventItem);
      });
    }

    // الحصول على اسم نوع الحدث بالعربية
    function getEventTypeName(type) {
      const typeNames = {
        'gift': 'هدية',
        'like': 'إعجاب',
        'comment': 'تعليق',
        'follow': 'متابعة',
        'share': 'مشاركة',
        'join': 'انضمام'
      };

      return typeNames[type] || 'حدث';
    }

    // إضافة حدث جديد
    function addEvent(type, username, content) {
      const now = new Date();
      const timeStr = now.toLocaleTimeString('ar-SA');

      // إضافة الحدث الجديد في بداية المصفوفة
      events.unshift({
        type,
        username,
        content,
        time: timeStr
      });

      // تقييد عدد الأحداث المحفوظة إلى 100 حدث فقط
      if (events.length > 100) {
        events.pop();
      }

      // تحديث العداد
      eventsCounter++;
      eventsCount.textContent = eventsCounter;

      // تحديث العرض
      updateEventsList();
    }

    // استقبال الأحداث من Socket.IO

    // حدث الهدية
    socket.on('gift', (data) => {
      let content;
      if (data.diamondCount) {
        content = `أرسل ${data.giftName} (${data.diamondCount} ماسة)`;
      } else {
        content = `أرسل ${data.giftName}`;
      }

      if (data.repeatCount && data.repeatCount > 1) {
        content += ` ×${data.repeatCount}`;
      }

      addEvent('gift', data.nickname || data.uniqueId, content);
    });

    // حدث الإعجاب
    socket.on('like', (data) => {
      let content = `أبدى إعجابه بالفيديو`;
      if (data.likeCount) {
        content += ` (${data.likeCount} إعجاب)`;
      }
      addEvent('like', data.nickname || data.uniqueId, content);
    });

    // حدث التعليق
    socket.on('comment', (data) => {
      addEvent('comment', data.nickname || data.uniqueId, data.comment);
    });

    // حدث الدردشة
    socket.on('chat', (data) => {
      addEvent('comment', data.nickname || data.uniqueId, data.comment);
    });

    // حدث المتابعة
    socket.on('follow', (data) => {
      addEvent('follow', data.nickname || data.uniqueId, 'قام بمتابعة البث');
    });

    // حدث المشاركة
    socket.on('share', (data) => {
      addEvent('share', data.nickname || data.uniqueId, 'قام بمشاركة البث');
    });

    // حدث الانضمام
    socket.on('join', (data) => {
      addEvent('join', data.nickname || data.uniqueId, 'انضم إلى البث');
    });

    // تحديث القائمة عند بدء التشغيل
    updateEventsList();

    // نظام حماية بسيط للصفحة
    let authCheckDone = false;

    window.addEventListener('authStateChanged', (event) => {
      if (authCheckDone) return; // تجنب التحقق المتكرر

      const user = event.detail.user;
      console.log('🔐 Connection page - Auth state:', user?.email);

      if (!user) {
        // لا يوجد مستخدم، وجه لصفحة تسجيل الدخول
        console.log('❌ No user, redirecting to auth');
        authCheckDone = true;
        window.location.href = '/auth.html';
      } else if (!user.emailVerified) {
        // البريد غير مؤكد، وجه لصفحة التأكيد
        console.log('❌ Email not verified, redirecting to verification');
        authCheckDone = true;
        window.location.href = '/email-verification.html';
      } else {
        // المستخدم مسجل دخول والبريد مؤكد
        console.log('✅ User authenticated and verified');
        authCheckDone = true;

        // تحديث واجهة المستخدم
        updateUserUI(user);
      }
    });

    function updateUserUI(user) {
      // تحديث معلومات المستخدم في الواجهة
      const userInfoElements = document.querySelectorAll('.user-info');
      userInfoElements.forEach(element => {
        element.innerHTML = `
          <div class="user-details">
            <span class="user-name">${user.displayName || user.email}</span>
            <span class="user-email">${user.email}</span>
          </div>
        `;
        element.style.display = 'block';
      });

      // إضافة رابط تسجيل الخروج
      const sidebar = document.querySelector('.nav-menu');
      if (sidebar && !sidebar.querySelector('.logout-link')) {
        const logoutLink = document.createElement('a');
        logoutLink.href = '#';
        logoutLink.className = 'logout-link';
        logoutLink.innerHTML = `
          <span class="nav-icon">🚪</span>
          تسجيل الخروج
        `;
        logoutLink.addEventListener('click', async (e) => {
          e.preventDefault();
          try {
            await window.firebaseFunctions.signOut(window.firebaseAuth);
            window.location.href = '/auth.html';
          } catch (error) {
            console.error('Logout error:', error);
          }
        });
        sidebar.appendChild(logoutLink);
      }
    }
  </script>

  <!-- Firebase Integration -->
  <script type="module" src="/js/firebase-config.js"></script>
  <!-- تم إزالة auth-guard مؤقتاً لحل مشكلة التضارب -->
  <!-- <script type="module" src="/js/auth-guard.js"></script> -->

  <script src="/js/global-text-direction.js"></script>
  <script src="/dark-mode.js"></script>
  <script src="/js/translation.js"></script>
  <script src="/js/smooth-nav.js"></script>
  <script src="/js/background-manager.js"></script>
</body>
</html>