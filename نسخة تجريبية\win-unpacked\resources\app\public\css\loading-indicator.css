/**
 * أنماط مؤشر التحميل
 */

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.loading-overlay.active {
  opacity: 1;
  visibility: visible;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-message {
  color: white;
  margin-top: 20px;
  font-size: 16px;
  text-align: center;
}

/* تعديلات للوضع الداكن */
.dark-mode .loading-overlay {
  background-color: rgba(0, 0, 0, 0.7);
}

.dark-mode .loading-spinner {
  border-color: rgba(255, 255, 255, 0.2);
  border-top-color: #fff;
}

.dark-mode .loading-message {
  color: #f0f0f0;
}
