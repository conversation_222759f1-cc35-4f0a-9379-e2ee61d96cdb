const { contextBridge } = require('electron');

/**
 * Electron Preload Script
 * يوفر واجهة آمنة للتواصل مع العمليات الرئيسية
 */

// تعريض APIs آمنة للنافذة
contextBridge.exposeInMainWorld('electronAPI', {
  // معلومات النظام
  platform: process.platform,
  
  // إصدارات
  versions: {
    electron: process.versions.electron,
    node: process.versions.node,
    chrome: process.versions.chrome
  }
});

// إعدادات خاصة بـ TikTok Live Overlay
contextBridge.exposeInMainWorld('streamTokElectron', {
  // التحقق من وجود Electron
  isElectron: true,
  
  // إصدار Electron
  electronVersion: process.versions.electron,
  
  // إصدار Node.js
  nodeVersion: process.versions.node,
  
  // إصدار Chrome
  chromeVersion: process.versions.chrome
});

console.log('Electron preload script loaded successfully');
