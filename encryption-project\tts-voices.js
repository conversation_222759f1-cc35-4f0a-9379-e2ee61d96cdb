// قائمة نماذج Microsoft Edge TTS المتاحة
const ttsModels = [
  // نماذج اللغة العربية - مصر
  { id: "ar-EG-SalmaNeural", name: "العربية (مصر) - سلمى (أنثى)" },
  { id: "ar-EG-ShakirNeural", name: "العربية (مصر) - شاكر (ذكر)" },

  // نماذج اللغة العربية - السعودية
  { id: "ar-SA-HamedNeural", name: "العربية (السعودية) - حامد (ذكر)" },
  { id: "ar-SA-ZariyahNeural", name: "العربية (السعودية) - زارية (أنثى)" },

  // نماذج اللغة العربية - الإمارات
  { id: "ar-AE-FatimaNeural", name: "العربية (الإمارات) - فاطمة (أنثى)" },
  { id: "ar-AE-HamdanNeural", name: "العربية (الإمارات) - حمدان (ذكر)" },

  // نماذج اللغة العربية - الجزائر
  { id: "ar-DZ-AminaNeural", name: "العربية (الجزائر) - أمينة (أنثى)" },
  { id: "ar-DZ-IsmaelNeural", name: "العربية (الجزائر) - إسماعيل (ذكر)" },

  // نماذج اللغة العربية - البحرين
  { id: "ar-BH-AliNeural", name: "العربية (البحرين) - علي (ذكر)" },
  { id: "ar-BH-LailaNeural", name: "العربية (البحرين) - ليلى (أنثى)" },

  // نماذج اللغة العربية - العراق
  { id: "ar-IQ-BasselNeural", name: "العربية (العراق) - باسل (ذكر)" },
  { id: "ar-IQ-RanaNeural", name: "العربية (العراق) - رنا (أنثى)" },

  // نماذج اللغة العربية - الأردن
  { id: "ar-JO-SanaNeural", name: "العربية (الأردن) - سناء (أنثى)" },
  { id: "ar-JO-TaimNeural", name: "العربية (الأردن) - تيم (ذكر)" },

  // نماذج اللغة العربية - الكويت
  { id: "ar-KW-FahedNeural", name: "العربية (الكويت) - فهد (ذكر)" },
  { id: "ar-KW-NouraNeural", name: "العربية (الكويت) - نورة (أنثى)" },

  // نماذج اللغة العربية - لبنان
  { id: "ar-LB-LaylaNeural", name: "العربية (لبنان) - ليلى (أنثى)" },
  { id: "ar-LB-RamiNeural", name: "العربية (لبنان) - رامي (ذكر)" },

  // نماذج اللغة العربية - ليبيا
  { id: "ar-LY-ImanNeural", name: "العربية (ليبيا) - إيمان (أنثى)" },
  { id: "ar-LY-OmarNeural", name: "العربية (ليبيا) - عمر (ذكر)" },

  // نماذج اللغة العربية - المغرب
  { id: "ar-MA-JamalNeural", name: "العربية (المغرب) - جمال (ذكر)" },
  { id: "ar-MA-MounaNeural", name: "العربية (المغرب) - منى (أنثى)" },

  // نماذج اللغة العربية - عمان
  { id: "ar-OM-AbdullahNeural", name: "العربية (عمان) - عبدالله (ذكر)" },
  { id: "ar-OM-AyshaNeural", name: "العربية (عمان) - عائشة (أنثى)" },

  // نماذج اللغة العربية - قطر
  { id: "ar-QA-AmalNeural", name: "العربية (قطر) - أمل (أنثى)" },
  { id: "ar-QA-MoazNeural", name: "العربية (قطر) - معاذ (ذكر)" },

  // نماذج اللغة العربية - سوريا
  { id: "ar-SY-AmanyNeural", name: "العربية (سوريا) - أماني (أنثى)" },
  { id: "ar-SY-LaithNeural", name: "العربية (سوريا) - ليث (ذكر)" },

  // نماذج اللغة العربية - تونس
  { id: "ar-TN-HediNeural", name: "العربية (تونس) - هادي (ذكر)" },
  { id: "ar-TN-ReemNeural", name: "العربية (تونس) - ريم (أنثى)" },

  // نماذج اللغة العربية - اليمن
  { id: "ar-YE-MaryamNeural", name: "العربية (اليمن) - مريم (أنثى)" },
  { id: "ar-YE-SalehNeural", name: "العربية (اليمن) - صالح (ذكر)" },

  // نماذج اللغة الإنجليزية
  { id: "en-US-AriaNeural", name: "الإنجليزية (أمريكية) - آريا (أنثى)" },
  { id: "en-US-GuyNeural", name: "الإنجليزية (أمريكية) - جاي (ذكر)" },
  { id: "en-US-JennyNeural", name: "الإنجليزية (أمريكية) - جيني (أنثى)" },
  { id: "en-GB-SoniaNeural", name: "الإنجليزية (بريطانية) - سونيا (أنثى)" },
  { id: "en-GB-RyanNeural", name: "الإنجليزية (بريطانية) - رايان (ذكر)" },

  // نماذج اللغة الفرنسية
  { id: "fr-FR-DeniseNeural", name: "الفرنسية - دينيز (أنثى)" },
  { id: "fr-FR-HenriNeural", name: "الفرنسية - هنري (ذكر)" },

  // نماذج اللغة الألمانية
  { id: "de-DE-KatjaNeural", name: "الألمانية - كاتيا (أنثى)" },
  { id: "de-DE-ConradNeural", name: "الألمانية - كونراد (ذكر)" },

  // نماذج اللغة الإسبانية
  { id: "es-ES-ElviraNeural", name: "الإسبانية - إلفيرا (أنثى)" },
  { id: "es-ES-AlvaroNeural", name: "الإسبانية - ألفارو (ذكر)" }
];

// تصدير القوائم للاستخدام في الملفات الأخرى
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { ttsModels };
}